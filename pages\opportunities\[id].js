import "reflect-metadata";
import withAuthenticatedUser from "../../src/utils/WithAuthenticatedUser";
import withRegisteredUser from "../../src/utils/WithRegisteredUser";
import withTermsAndConditionsUpToDate from "../../src/utils/WithTermsAndConditionsUpToDate";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "../../config";
import OpportunityPageWithPerks from "../../components/pages/OpportunityPageWithPerks";
import { useTranslation } from "next-i18next";
import labelsCommon from "../../config/translations/common";
import labelsOpportunities from "../../config/translations/opportunities";
import labelsAddContent from "../../config/translations/add-content";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import flags from "../../utils/feature-flags";
import labelsContentSubmission from "../../config/translations/content-submission";
import labelsFacebookGuide from "../../config/translations/content-submission-facebook-guide";
import labelsYouTubeGuide from "../../config/translations/content-submission-youtube-guide";
import labelsInstagramGuide from "../../config/translations/content-submission-instagram-guide";
import labelsTwitchGuide from "../../config/translations/content-submission-twitch-guide";
import labelsTikTokGuide from "../../config/translations/content-submission-tiktok-guide";
import labelsConnectAccounts from "../../config/translations/connect-accounts";
import labelsWebsiteContent from "../../config/translations/content-submission-website";
import labelsUploadContent from "../../config/translations/content-submission-upload";
import labelsProfile from "../../config/translations/profile";
import labelsMyContent from "../../config/translations/my-content";
import RedirectException from "../../src/utils/RedirectException";
import { labelsPaymentBanner } from "@config/translations/payment-information";
import labelsPointOfContact from "../../config/translations/point-of-contact";
import { SHOW_WARNING_FOR_CHANGES_REQUESTED, useAppContext } from "@src/context";
import { toast } from "react-toastify";
import Error from "../_error";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../../components/Layout";
import Header from "../../components/header/Header";
import Footer from "../../components/footer/ProgramFooter";
import { AuthenticatedUserFactory } from "../../src/analytics/BrowserAnalytics";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../../src/serverprops/middleware/SaveInitialPage";
import errorLogger from "../../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import verifyAccessToProgram from "../../src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "../../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import opportunityDetailProps from "../../src/serverprops/OpportunityDetailProps";
import { useDependency } from "@src/context/DependencyContext";

export default function Opportunity({
  user,
  WATERMARKS_URL,
  YOUTUBE_HOSTS,
  TWITCH_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TIKTOK_HOSTS,
  accountConnected,
  error,
  pages,
  referer,
  invalidTikTokScope,
  UPDATE_OPPORTUNITY_DETAILS,
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED,
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_CONTENT_WITH_FINAL_REMARK
}) {
  const { analytics } = useDependency();
  const [connectedAccount, setConnectedAccount] = useState(accountConnected);
  const router = useRouter();
  const { state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const { t } = useTranslation([
    "common",
    "opportunities",
    "notifications",
    "add-content",
    "connect-accounts",
    "content-submission",
    "content-submission-instagram-guide",
    "content-submission-facebook-guide",
    "content-submission-youtube-guide",
    "content-submission-twitch-guide",
    "content-submission-tiktok-guide",
    "content-submission-website",
    "submit-social-media-content",
    "content-submission-upload",
    "my-content",
    "payment-information",
    "point-of-contact"
  ]);
  const {
    layout,
    addContentLabels,
    opportunitiesLabels,
    notificationsLabels,
    connectAccountLabels,
    contentSubmissionLabels,
    facebookGuideLabels,
    youtubeGuideLabels,
    instagramGuideLabels,
    twitchGuideLabels,
    tiktokGuideLabels,
    websiteContentLabels,
    uploadContentLabels,
    myContentLabels,
    paymentBannerLabels,
    pocLabels
  } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      addContentLabels: { ...labelsAddContent(t), ...labelsProfile(t) },
      opportunitiesLabels: labelsOpportunities(t),
      layout: labelsCommon(t),
      connectAccountLabels: labelsConnectAccounts(t),
      notificationsLabels: notificationBellLabels,
      contentSubmissionLabels: labelsContentSubmission(t),
      facebookGuideLabels: labelsFacebookGuide(t),
      youtubeGuideLabels: labelsYouTubeGuide(t),
      instagramGuideLabels: labelsInstagramGuide(t),
      twitchGuideLabels: labelsTwitchGuide(t),
      tiktokGuideLabels: labelsTikTokGuide(t),
      websiteContentLabels: labelsWebsiteContent(t),
      uploadContentLabels: labelsUploadContent(t),
      myContentLabels: labelsMyContent(t),
      paymentBannerLabels: labelsPaymentBanner(t),
      pocLabels: labelsPointOfContact(t)
    };
    labels.layout.footer = { locale: router.locale, labels: labels.layout.footer };
    return labels;
  }, [t, router.locale]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer.labels;

  const facebookGuideModalLabels = {
    title: facebookGuideLabels.title,
    subTitle: facebookGuideLabels.subTitle,
    contentImages: [
      {
        desc: facebookGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperFacebook1.png",
          "/img/content-submission/helperFacebook2.png",
          "/img/content-submission/helperFacebook4.png"
        ]
      }
    ],
    pointsToNote: [
      facebookGuideLabels.pointsToNote1,
      facebookGuideLabels.pointsToNote2,
      facebookGuideLabels.pointsToNote3,
      facebookGuideLabels.pointsToNote4,
      facebookGuideLabels.pointsToNote5
    ],
    footerLabel: facebookGuideLabels.gotIt,
    pointsToNoteTitle: facebookGuideLabels.pointsToNoteTitle
  };
  const youtubeGuideModalLabels = {
    title: youtubeGuideLabels.title,
    subTitle: youtubeGuideLabels.subTitle,
    contentImages: [
      {
        desc: youtubeGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperYoutube1.png",
          "/img/content-submission/helperYoutube3.png",
          "/img/content-submission/helperYoutube4.png"
        ]
      }
    ],
    pointsToNote: [
      youtubeGuideLabels.pointsToNote1,
      youtubeGuideLabels.pointsToNote2,
      youtubeGuideLabels.pointsToNote3
    ],
    footerLabel: youtubeGuideLabels.gotIt,
    pointsToNoteTitle: youtubeGuideLabels.pointsToNoteTitle
  };

  const instagramGuideModalLabels = {
    title: instagramGuideLabels.title,
    subTitle: instagramGuideLabels.subTitle,
    contentImages: [
      {
        desc: instagramGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperInstagram1.png",
          "/img/content-submission/helperInstagram2.png",
          "/img/content-submission/helperInstagram3.png"
        ]
      }
    ],
    pointsToNote: [
      instagramGuideLabels.pointsToNote1,
      instagramGuideLabels.pointsToNote2,
      instagramGuideLabels.pointsToNote3,
      instagramGuideLabels.pointsToNote4,
      instagramGuideLabels.pointsToNote5,
      ...(FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED ? [] : [instagramGuideLabels.pointsToNote6])
    ],
    footerLabel: instagramGuideLabels.gotIt,
    pointsToNoteTitle: instagramGuideLabels.pointsToNoteTitle
  };

  const twitchGuideModalLabels = {
    title: twitchGuideLabels.title,
    subTitle: twitchGuideLabels.subTitle,
    contentImages: [
      {
        desc: twitchGuideLabels.imageDesc1,
        imageSrc: ["/img/content-submission/helperTwitch1.png"]
      },
      {
        desc: twitchGuideLabels.imageDesc2,
        imageSrc: ["/img/content-submission/helperTwitch2.png"]
      }
    ],
    pointsToNote: [twitchGuideLabels.pointsToNote1, twitchGuideLabels.pointsToNote2, twitchGuideLabels.pointsToNote3],
    footNote: twitchGuideLabels.footNote,
    footerLabel: twitchGuideLabels.gotIt,
    pointsToNoteTitle: twitchGuideLabels.pointsToNoteTitle
  };

  const tiktokGuideModalLabels = {
    title: tiktokGuideLabels.title,
    subTitle: tiktokGuideLabels.subTitle,
    contentImages: [
      {
        desc: tiktokGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperTiktok1.png",
          "/img/content-submission/helperTiktok2.png",
          "/img/content-submission/helperTiktok.png"
        ]
      }
    ],
    pointsToNote: [tiktokGuideLabels.pointsToNote1, tiktokGuideLabels.pointsToNote2],
    footerLabel: tiktokGuideLabels.gotIt,
    pointsToNoteTitle: tiktokGuideLabels.pointsToNoteTitle
  };

  const contentSubmissionTabLabels = {
    contentSubmissionLabels,
    facebookGuideModalLabels,
    youtubeGuideModalLabels,
    instagramGuideModalLabels,
    twitchGuideModalLabels,
    tiktokGuideModalLabels,
    addContentLabels,
    connectAccountLabels,
    websiteContentLabels,
    uploadContentLabels
  };

  useEffect(() => {
    return () => {
      toast.dismiss(SHOW_WARNING_FOR_CHANGES_REQUESTED);
    };
  });

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.opportunities}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={null}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user} className="opportunity-layout">
        <OpportunityPageWithPerks
          key={router.asPath}
          user={user}
          WATERMARKS_URL={WATERMARKS_URL}
          paymentBannerLabels={paymentBannerLabels}
          opportunitiesLabels={opportunitiesLabels}
          layout={layout}
          contentSubmissionTabLabels={contentSubmissionTabLabels}
          pocLabels={pocLabels}
          YOUTUBE_HOSTS={YOUTUBE_HOSTS}
          TWITCH_HOSTS={TWITCH_HOSTS}
          INSTAGRAM_HOSTS={INSTAGRAM_HOSTS}
          FACEBOOK_HOSTS={FACEBOOK_HOSTS}
          TIKTOK_HOSTS={TIKTOK_HOSTS}
          t={t}
          accountConnected={connectedAccount}
          setConnectedAccount={setConnectedAccount}
          error={error}
          pages={pages}
          myContentLabels={myContentLabels}
          referer={referer}
          invalidTikTokScope={invalidTikTokScope}
          UPDATE_OPPORTUNITY_DETAILS={UPDATE_OPPORTUNITY_DETAILS}
          FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED={FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED}
          FLAG_CONTENT_WITH_FINAL_REMARK={FLAG_CONTENT_WITH_FINAL_REMARK}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={router.locale}
          labels={footerLabels}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(opportunityDetailProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;
  const accountConnected = req.session.accountType || null;
  const { pages = [] } = req.session.fbPages || {};
  const error = req.session.error || null;
  const invalidTikTokScope = req.session.INVALID_TIKTOK_SCOPE || null;
  delete req.session.opportunityId;
  delete req.session.INVALID_TIKTOK_SCOPE;
  req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      WATERMARKS_URL: config.WATERMARKS_URL,
      YOUTUBE_HOSTS: config.YOUTUBE_HOSTS,
      TWITCH_HOSTS: config.TWITCH_HOSTS,
      INSTAGRAM_HOSTS: config.INSTAGRAM_HOSTS,
      FACEBOOK_HOSTS: config.FACEBOOK_HOSTS,
      TIKTOK_HOSTS: config.TIKTOK_HOSTS,
      accountConnected,
      ...(await serverSideTranslations(locale, [
        "common",
        "opportunities",
        "notifications",
        "add-content",
        "connect-accounts",
        "content-submission",
        "content-submission-instagram-guide",
        "content-submission-facebook-guide",
        "content-submission-youtube-guide",
        "content-submission-twitch-guide",
        "content-submission-tiktok-guide",
        "content-submission-website",
        "submit-social-media-content",
        "content-submission-upload",
        "my-content",
        "payment-information",
        "point-of-contact"
      ])),
      pages,
      error,
      referer: req.headers?.referer || null,
      invalidTikTokScope,
      UPDATE_OPPORTUNITY_DETAILS: flags.isUpdateOpportunityDetailsEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED: flags.isInstagramMediaSupportEnabled(),
      FLAG_CONTENT_WITH_FINAL_REMARK: flags.isContentWithFinalRemarksEnabled()
    }
  };
};
