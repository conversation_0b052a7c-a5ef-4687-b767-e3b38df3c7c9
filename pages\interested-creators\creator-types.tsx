import "reflect-metadata";
import React, { memo, useCallback, useMemo, useState } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Error from "../_error";
import { useAppContext } from "@src/context";
import { useToast } from "../../components/toast";
import InterestedCreatorsCreatorTypePage from "../../components/pages/interested-creators/InterestedCreatorsCreatorTypePage";
import { useTranslation } from "next-i18next";
import labelsCommon from "../../config/translations/common";
import MigrationLayout from "../../components/MigrationLayout";
import labelsCreatorType from "../../config/translations/creator-type";
import labelsInformation from "../../config/translations/information";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import config from "../../config";
import { interestedCreatorPages } from "./information";
import { useRouter } from "next/router";
import { AuthenticatedUser, AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import labelsBreadCrumb from "../../config/translations/breadcrumb";
import flags from "../../utils/feature-flags";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorCreatorTypesProps from "@src/serverprops/InterestedCreatorCreatorTypesProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import { useDependency } from "@src/context/DependencyContext";

export type PageLabels = {
  interestedCreatorTitle: string;
  interestedCreatorDescription: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
};
export type FormLabels = {
  cancel: string;
  next: string;
  yes: string;
  no: string;
  close: string;
};
export type CreatorType = {
  imageAsIcon: string;
  value: string;
  label: string;
};
export type InterestedCreatorsCreatorType = {
  nucleusId?: number;
  creatorTypes?: CreatorType[] | string[];
};

export type InterestedCreatorCreatorTypeProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InterestedCreator;
  user: AuthenticatedUser;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

export default memo(function InterestedCreatorCreatorType({
  interestedCreator,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: InterestedCreatorCreatorTypeProps) {
  const { analytics } = useDependency();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { dispatch, state, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const router = useRouter();
  const { locale } = useRouter();
  const { t } = useTranslation(["common", "breadcrumb", "information", "communication-preferences", "opportunities"]);
  const { layout, pageLabels, formLabels } = useMemo(() => {
    const layout = {
      ...labelsCommon(t),
      ...labelsBreadCrumb(t)
    };
    const infoLabels = labelsInformation(t);
    const creatorTypeLabels = labelsCreatorType(t);
    const {
      buttons: { cancel, next, yes, no, close }
    } = layout;
    const { interestedCreatorTitle, interestedCreatorDescription } = creatorTypeLabels;
    const formLabels: FormLabels = Object.assign(
      {},
      {
        cancel,
        next,
        yes,
        no,
        close
      }
    );
    const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = infoLabels;
    const pageLabels: PageLabels = Object.assign(
      {},
      {
        interestedCreatorTitle,
        interestedCreatorDescription,
        confirmationDesc1,
        confirmationDesc2,
        modalConfirmationTitle
      }
    );
    return { layout, pageLabels, formLabels };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const onClose = useCallback(() => {
    setShowConfirmation(true);
  }, []);

  const onGoBack = () => router.push("/interested-creators/information");

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={pageLabels.interestedCreatorTitle}
      className="interested-creator"
      {...{ ...layout, onClose }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      onGoBack={onGoBack}
    >
      <InterestedCreatorsCreatorTypePage
        {...{
          formLabels,
          pageLabels,
          t,
          onClose,
          showConfirmation,
          interestedCreator,
          stableDispatch,
          errorToast,
          unhandledError,
          setShowConfirmation,
          state,
          router,
          locale,
          analytics,
          INTERESTED_CREATOR_REAPPLY_PERIOD
        }}
      />
    </MigrationLayout>
  );
});

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorCreatorTypesProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorCreatorTypeProps>;
  }
  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.creatorTypes);
  if (!config.INTERESTED_CREATOR || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      ...(await serverSideTranslations(locale, ["common", "breadcrumb", "creator-type"])),
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
