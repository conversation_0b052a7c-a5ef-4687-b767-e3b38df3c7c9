import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ConnectAccountsPageLabels = {
  connectAccountsLabels: {
    subscribers: string;
    messages: {
      actionDescription1: string;
      removeAccountDescription: string;
      youtubeNoChannelError: string;
      cannotConnectInstaAccount: string;
      cannotConnectInstaAccountHeader: string;
      actionDescription3: string;
      actionDescription2: string;
      actionDescription4: string;
      removeAccountTitle: string;
      actionTitle: string;
    };
    modal: {
      removeAccountTitle: string;
      removeAccountDescription1: string;
      removeAccountDescription2: string;
    };
    cancel: string;
    remove: string;
    connect: string;
    modalTitle: string;
    accounts: {
      instagram: string;
      facebook: string;
      twitch: string;
      youTube: string;
      tiktok: string;
    };
    confirmationDesc1: string;
    confirmationDesc2: string;
    reconnectAccount: string;
    modalMessage: string;
    message: string;
    modalConfirmationTitleFB: string;
    connectNewAccountDescription: string;
    connectNewAccount: string;
    reVerifyAccount: string;
    myAccount: string;
    or: string;
    description: string;
    comma: string;
    connectNewAccountDescriptionWithTikTok: string;
    verificationPending: string;
    title: string;
    expireAccount: string;
    removeAccount: string;
    subTitle: string;
    addAccount: string;
  };
};

export class ConnectAccountsPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ConnectAccountsPageLabels {
    const microCopy = new MicroCopy(microCopies);
    return {
      connectAccountsLabels: {
        subscribers: microCopy.get("connectAccounts.subscribers"),
        modalTitle: microCopy.get("connectAccounts.modalTitle"),
        messages: {
          actionTitle: microCopy.get("connectAccounts.messages.actionTitle"),
          actionDescription1: microCopy.get("connectAccounts.messages.actionDescription1"),
          actionDescription2: microCopy.get("connectAccounts.messages.actionDescription2"),
          actionDescription3: microCopy.get("connectAccounts.messages.actionDescription3"),
          actionDescription4: microCopy.get("connectAccounts.messages.actionDescription4"),
          cannotConnectInstaAccount: microCopy.get("connectAccounts.messages.cannotConnectInstaAccount"),
          cannotConnectInstaAccountHeader: microCopy.get("connectAccounts.messages.cannotConnectInstaAccountHeader"),
          removeAccountDescription: microCopy.get("connectAccounts.messages.removeAccountDescription"),
          removeAccountTitle: microCopy.get("connectAccounts.messages.removeAccountTitle"),
          youtubeNoChannelError: microCopy.get("connectAccounts.messages.youtubeNoChannelError")
        },
        modal: {
          removeAccountTitle: microCopy.get("connectAccounts.removeAccountTitle"),
          removeAccountDescription1: microCopy.get("connectAccounts.removeAccountDescription1"),
          removeAccountDescription2: microCopy.get("connectAccounts.removeAccountDescription2")
        },
        accounts: {
          instagram: microCopy.get("connectAccounts.accounts.instagram"),
          facebook: microCopy.get("connectAccounts.accounts.facebook"),
          twitch: microCopy.get("connectAccounts.accounts.twitch"),
          youTube: microCopy.get("connectAccounts.accounts.youTube"),
          tiktok: microCopy.get("connectAccounts.accounts.tiktok")
        },
        cancel: microCopy.get("connectAccounts.cancel"),
        remove: microCopy.get("connectAccounts.remove"),
        connect: microCopy.get("connectAccounts.connect"),
        confirmationDesc1: microCopy.get("connectAccounts.confirmationDesc1"),
        confirmationDesc2: microCopy.get("connectAccounts.confirmationDesc2"),
        reconnectAccount: microCopy.get("connectAccounts.reconnectAccount"),
        modalMessage: microCopy.get("connectAccounts.modalMessage"),
        message: microCopy.get("connectAccounts.message"),
        modalConfirmationTitleFB: microCopy.get("connectAccounts.modalConfirmationTitleFB"),
        connectNewAccountDescription: microCopy.get("connectAccounts.connectNewAccountDescription"),
        connectNewAccount: microCopy.get("connectAccounts.connectNewAccount"),
        reVerifyAccount: microCopy.get("connectAccounts.reVerifyAccount"),
        myAccount: microCopy.get("connectAccounts.myAccount"),
        or: microCopy.get("connectAccounts.or"),
        description: microCopy.get("connectAccounts.description"),
        comma: microCopy.get("connectAccounts.comma"),
        connectNewAccountDescriptionWithTikTok: microCopy.get("connectAccounts.connectNewAccountDescriptionWithTikTok"),
        verificationPending: microCopy.get("connectAccounts.verificationPending"),
        title: microCopy.get("connectAccounts.title"),
        expireAccount: microCopy.get("connectAccounts.expireAccount"),
        removeAccount: microCopy.get("connectAccounts.removeAccount"),
        subTitle: microCopy.get("connectAccounts.subTitle"),
        addAccount: microCopy.get("connectAccounts.addAccount")
      }
    };
  }
}
