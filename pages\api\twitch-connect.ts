import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onOAuthError from "@src/api/OAuthErrorHandler";
import ConnectTwitchAccountController from "@src/controllers/ConnectTwitchAccountController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ConnectTwitchAccountController);
    await controller.handle(req, res);
  });

export default router.handler({ onError: onOAuthError });
