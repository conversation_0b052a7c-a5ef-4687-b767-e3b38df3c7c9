import information from "../icons/Information";
import franchisesYouPlay from "../icons/FranchisesYouPlay";
import creatorType from "../icons/CreatorType";
import connectAccounts from "../icons/ConnectAccounts";
import communicationPreferences from "../icons/CommunicationPreferences";
import termsAndConditions from "../icons/TermsAndConditions";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { FC } from "react";

export type NavStep = {
  prev: string | null;
  icon: FC<SvgProps>;
  title: string;
  href: string;
  next: string;
};

export const completeProfileSteps: Array<NavStep> = [
  {
    prev: null,
    icon: information,
    title: "Information",
    href: "/onboarding/information",
    next: "/franchises-you-play"
  },
  {
    prev: "/onboarding/information",
    icon: franchisesYouPlay,
    title: "Franchises You Play",
    href: "/franchises-you-play",
    next: "/creator-type"
  },
  {
    prev: "/franchises-you-play",
    icon: creatorType,
    title: "Creator Type",
    href: "/creator-type",
    next: "/connect-accounts"
  },
  {
    prev: "/creator-type",
    icon: connectAccounts,
    title: "Connect Accounts",
    href: "/connect-accounts",
    next: "/communication-preferences"
  },
  {
    prev: "/connect-accounts",
    icon: communicationPreferences,
    title: "Communication Preferences",
    href: "/communication-preferences",
    next: "/terms-and-conditions"
  },
  {
    prev: "/communication-preferences",
    icon: termsAndConditions,
    title: "Terms And Conditions",
    href: "/terms-and-conditions",
    next: null
  }
];

export const interestedCreatorSteps: Array<NavStep> = [
  {
    prev: null,
    icon: information,
    title: "Information",
    href: "/interested-creators/information",
    next: "/interested-creators/creator-types"
  },
  {
    prev: "/interested-creators/information",
    icon: creatorType,
    title: "Creator Type",
    href: "/interested-creators/creator-types",
    next: "/interested-creators/franchises-you-play"
  },
  {
    prev: "/interested-creators/creator-types",
    icon: franchisesYouPlay,
    title: "Franchises You Play",
    href: "/interested-creators/franchises-you-play",
    next: null
  }
];
