import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import InterestedCreatorsFranchisesYouPlayForm from "@components/forms/InterestedCreatorsFranchisesYouPlayForm";
import { NextRouter } from "next/router";
import { franchisesYouPlayFormLabels } from "../../translations/utils";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { anInterestedCreator } from "../../factories/interestedCreators/InterestedCreator";
import InterestedCreatorsServices, { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/api/services/InterestedCreatorsServices");
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsFranchisesYouPlayForm", () => {
  const analytics = {} as unknown as BrowserAnalytics;
  const franchises = [
    { value: "Apex", label: "Apex", image: "" },
    { value: "Fifa", label: "Fifa", image: "" }
  ];
  const interestedCreator = anInterestedCreator({
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    preferredLanguage: { code: "en_US", name: "English" }
  }) as unknown as InterestedCreator;
  const router = { locale: "en-us", push: jest.fn() } as unknown as NextRouter;
  const interestedCreatorWithFranchises = {
    ...interestedCreator,
    preferredFranchises: [
      { id: "Apex", type: "PRIMARY" },
      { id: "Fifa", type: "SECONDARY" }
    ]
  } as unknown as InterestedCreator;
  const interestedCreatorsFranchisesYouPlayFormProps = {
    franchises,
    interestedCreator,
    franchisesYouPlayFormLabels,
    stableDispatch: jest.fn(),
    onClose: jest.fn(),
    router,
    analytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ errorHandler: jest.fn() });
  });

  it("enables next button when primary franchise is selected", async () => {
    render(<InterestedCreatorsFranchisesYouPlayForm {...interestedCreatorsFranchisesYouPlayFormProps} />);
    const nextButton = screen.getByText("Submit");
    expect(nextButton).toBeDisabled();
    await userEvent.click(screen.getByRole("textbox", { name: franchisesYouPlayFormLabels.labels.primaryFranchise }));

    await userEvent.click(screen.getByText("Apex"));

    await waitFor(() => expect(nextButton).toBeEnabled());
  });

  it("pre-populates the form with current preferred primary franchise", async () => {
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        interestedCreator={interestedCreatorWithFranchises}
      />
    );

    await waitFor(() => expect(screen.getByDisplayValue("Apex")).toHaveClass("search-text-field"));
  });

  it("pre-populates the form with current preferred secondary franchise", async () => {
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        interestedCreator={interestedCreatorWithFranchises}
      />
    );

    expect(screen.getByText("Select more Franchises")).toBeInTheDocument();
    expect(screen.getAllByRole("checkbox")[1]).toBeChecked();
  });

  it("saves preferred franchises", async () => {
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const selectedFranchise = { id: "Apex", type: "PRIMARY" };
    const interestedCreatorWithFranchises = {
      ...interestedCreator,
      preferredFranchises: [selectedFranchise]
    };
    (InterestedCreatorsServices.saveInterestedCreatorInformation as jest.Mock).mockResolvedValue({
      data: interestedCreatorWithFranchises
    });
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        analytics={analytics}
      />
    );
    await userEvent.click(screen.getByRole("textbox", { name: franchisesYouPlayFormLabels.labels.primaryFranchise }));
    // Select Primary franchise
    await userEvent.click(screen.getByText("Apex"));
    // Wait for submit button to be enabled
    const nextButton = screen.getByText("Submit");
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(InterestedCreatorsServices.saveInterestedCreatorInformation).toHaveBeenCalledTimes(1);
      expect(InterestedCreatorsServices.saveInterestedCreatorInformation).toHaveBeenCalledWith(
        interestedCreatorWithFranchises
      );
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: true,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toBeCalledTimes(1);
      expect(router.push).toBeCalledWith("/interested-creators/complete");
    });
  });

  it("shows 'Load more' button if there are more than 8 franchises", async () => {
    const franchises = [
      { value: "a0GK000000S0rHmMAJ", label: "testdisble3", image: "" },
      { value: "a0GK000000RpU56MAF", label: "Need for Speed", image: "" },
      { value: "a0GK000000RpSuCMAV", label: "Apex Legends", image: "" },
      { value: "a0GK000000RpSuRMAV", label: "Madden NFL", image: "" },
      { value: "a0GK000000RpSuMMAA", label: "Test", image: "" },
      { value: "a0GK000000RpSuMPPA", label: "Test 2021", image: "" },
      { value: "a0GK000000RpKuFMMM", label: "Fifa 2020", image: "" },
      { value: "a0GK000000RpKuFMAP", label: "The Sims", image: "" },
      { value: "a0GK000000RpKuFMAV", label: "Fifa 2021", image: "" }
    ];
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        franchises={franchises}
      />
    );
    await userEvent.click(screen.getByRole("textbox", { name: franchisesYouPlayFormLabels.labels.primaryFranchise }));
    await userEvent.click(screen.getByText("Apex Legends"));
    const loadMoreButton = screen.queryByText("Load more...");
    await waitFor(() => expect(loadMoreButton).toBeInTheDocument());

    await userEvent.click(loadMoreButton);

    await waitFor(() => expect(screen.getByText("Fifa 2021")).toBeVisible());
  });

  it("keeps last selected franchise when input looses focus", async () => {
    render(<InterestedCreatorsFranchisesYouPlayForm {...interestedCreatorsFranchisesYouPlayFormProps} />);
    const nextButton = screen.getByText("Submit");
    expect(nextButton).toBeDisabled();
    const primaryFranchise = screen.getByRole("textbox", { name: franchisesYouPlayFormLabels.labels.primaryFranchise });
    await userEvent.click(primaryFranchise);
    await userEvent.click(screen.getByText("Apex"));
    await userEvent.clear(primaryFranchise);
    await waitFor(() => expect(primaryFranchise).toHaveValue(""));

    await userEvent.tab();

    await waitFor(() => {
      expect(primaryFranchise).toHaveValue("Apex");
      expect(nextButton).toBeEnabled();
    });
  });

  describe("with 'INTERESTED_CREATOR_ADD_ACCOUNT' enabled", () => {
    it("saves preferred franchises with additional links added", async () => {
      const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
      const selectedFranchise = { id: "Fifa", type: "PRIMARY" };
      const interestedCreatorWithFranchises = {
        ...interestedCreator,
        preferredFranchises: [selectedFranchise]
      };
      (InterestedCreatorsServices.saveInterestedCreatorInformation as jest.Mock).mockResolvedValue({
        data: interestedCreatorWithFranchises
      });
      render(
        <InterestedCreatorsFranchisesYouPlayForm
          {...interestedCreatorsFranchisesYouPlayFormProps}
          analytics={analytics}
        />
      );
      await userEvent.click(screen.getByRole("textbox", { name: franchisesYouPlayFormLabels.labels.primaryFranchise }));
      // Select Primary franchise
      await userEvent.click(screen.getByText("Fifa"));
      // Wait for submit button to be enabled
      const nextButton = screen.getByText("Submit");
      await waitFor(() => expect(nextButton).toBeEnabled());

      await userEvent.click(nextButton);

      await waitFor(() => {
        expect(InterestedCreatorsServices.saveInterestedCreatorInformation).toHaveBeenCalledTimes(1);
        expect(InterestedCreatorsServices.saveInterestedCreatorInformation).toHaveBeenCalledWith(
          interestedCreatorWithFranchises
        );
        expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
        expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
          finalStep: true,
          locale: "en-us",
          page: "/"
        });
        expect(router.push).toBeCalledTimes(1);
        expect(router.push).toBeCalledWith("/interested-creators/complete");
      });
    });
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' flag enabled", () => {
    it("pre-populates primary & seconday franchises and enables Next button to submit", async () => {
      render(
        <InterestedCreatorsFranchisesYouPlayForm
          {...interestedCreatorsFranchisesYouPlayFormProps}
          interestedCreator={interestedCreatorWithFranchises}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      expect(screen.getByDisplayValue("Apex")).toHaveClass("search-text-field");
      expect(screen.getAllByRole("checkbox")[1]).toBeChecked();
      expect(screen.getByRole("button", { name: "Submit" })).toBeEnabled();
    });

    it("saves updated preferred franchises", async () => {
      const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
      const interestedCreatorWithUpdatedFranchises = {
        ...interestedCreator,
        preferredFranchises: [{ id: "Fifa", type: "PRIMARY" }]
      };
      (InterestedCreatorsServices.addRequestToJoinFor as jest.Mock).mockResolvedValue({
        data: interestedCreatorWithFranchises
      });
      render(
        <InterestedCreatorsFranchisesYouPlayForm
          {...interestedCreatorsFranchisesYouPlayFormProps}
          analytics={analytics}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          interestedCreator={{
            ...interestedCreatorWithFranchises,
            preferredFranchises: [{ id: "Apex", type: "PRIMARY" }]
          }}
        />
      );
      // Displays previously selected value in primary franchise dropdown
      expect(screen.getByDisplayValue("Apex")).toHaveClass("search-text-field");
      const submitButton = screen.getByRole("button", { name: "Submit" });
      expect(submitButton).toBeEnabled();
      // Update primary franchise
      await userEvent.click(screen.getByDisplayValue("Apex"));
      await userEvent.click(screen.getByRole("button", { name: "Fifa" }));

      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(InterestedCreatorsServices.addRequestToJoinFor).toHaveBeenCalledTimes(1);
        expect(InterestedCreatorsServices.addRequestToJoinFor).toHaveBeenCalledWith(
          interestedCreatorWithUpdatedFranchises
        );
        expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
        expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
          finalStep: true,
          locale: "en-us",
          page: "/"
        });
        expect(router.push).toBeCalledTimes(1);
        expect(router.push).toBeCalledWith("/interested-creators/complete");
      });
    });

    it("shows 'Load more' button if there are more than 8 franchises", async () => {
      const franchises = [
        { value: "Apex", label: "Apex", image: "" },
        { value: "Fifa", label: "Fifa", image: "" },
        { value: "a0GK000000RpU56MAF", label: "Need for Speed", image: "" },
        { value: "a0GK000000RpSuCMAV", label: "Apex Legends", image: "" },
        { value: "a0GK000000RpSuRMAV", label: "Madden NFL", image: "" },
        { value: "a0GK000000RpSuMMAA", label: "Test", image: "" },
        { value: "a0GK000000RpSuMPPA", label: "Test 2021", image: "" },
        { value: "a0GK000000RpKuFMMM", label: "Fifa 2020", image: "" },
        { value: "a0GK000000RpKuFMAP", label: "The Sims", image: "" },
        { value: "a0GK000000RpKuFMAV", label: "Fifa 2021", image: "" }
      ];
      render(
        <InterestedCreatorsFranchisesYouPlayForm
          {...interestedCreatorsFranchisesYouPlayFormProps}
          interestedCreator={interestedCreatorWithFranchises}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          franchises={franchises}
        />
      );

      expect(screen.getByRole("button", { name: "Load more..." })).toBeInTheDocument();
    });
  });
});
