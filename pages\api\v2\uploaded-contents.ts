import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import ApiContainer from "../../../src/ApiContainer";
import SaveUploadContentDeliverableController from "../../../src/controllers/SaveUploadContentDeliverableController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../config";
import VerifyContentUrlController from "@src/controllers/VerifyContentUrlController";
import session from "@src/middleware/Session";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    if (config.FLAG_SIGNED_URL_V2_ENABLED || config.FLAG_SIGNED_URL_V1_ENABLED) {
      const controller = ApiContainer.get(VerifyContentUrlController);
      await controller.handleMarkUploadComplete(req, res);
    } else {
      const controller = ApiContainer.get(SaveUploadContentDeliverableController);
      await controller.handle(req, res);
    }
  });

export default router.handler({ onError });
