---
currentMenu: howto
---

# Working with Amplitude Events

New features will require you to track new analytics events and send them to [Amplitude](https://developers.amplitude.com/docs).

## Before you get started

You will need access to the Amplitude dashboard for this project in order to understand how events are defined, queried and visualized.

Please request to be added to the project group in Amplitude, and make sure you have access to the following dashboard: https://analytics.amplitude.com/itpxd

All events will have a source, depending on where they're logged.

- [Browser](https://data.amplitude.com/itpxd/Creator%20Network/implementation/main/latest/getting-started/browser_typescript/), or
- [Server](https://data.amplitude.com/itpxd/Creator%20Network/implementation/main/latest/getting-started/typescript_sdk/)

## How to verify the Browser implementation

If you're adding or modifying a new event with **Browser** source, for a new or an existing feature, you'll need to verify that the implementation matches the latest published plan in [Amplitude](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/).

Please use the following `make` target before and after making your changes.

```bash
make analytics/browser
```

## How to verify the Server implementation

If you're adding or modifying a new event with **Server** source, for a new or an existing feature, you'll need to verify that the implementation matches the latest published plan in [Amplitude](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/).

Please use the following `make` target before and after making your changes.

```bash
make analytics/server
```

## What to do before pushing

Make sure the command that verifies your implementation doesn't error out before pushing your changes.

If you're facing issues, please refer to Amplitude's [documentation page](https://data.amplitude.com/itpxd/Creator%20Network/implementation/main/latest/getting-started/browser_typescript/) for more details.
