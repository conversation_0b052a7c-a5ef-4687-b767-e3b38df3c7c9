import type { Preview } from "@storybook/react";
import { Description, Stories, Title } from "@storybook/blocks";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
import "../styles/globals.css";
import "../styles/storybook.css";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    },
    docs: {
      page: () => (
        <>
          <Title />
          <Description />
          <Stories includePrimary />
        </>
      )
    }
  }
};

export default preview;
