import "reflect-metadata";
import type { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onError from "@src/api/AuthErrorHandler";
import logLocale from "@src/middleware/LocaleLogger";
import { AuthenticateController } from "@eait-playerexp-cn/authentication";
import LegacyAuthenticateController from "@src/controllers/AuthenticateController";
import flags from "../../utils/feature-flags";
import initializeAnalytics from "@src/middleware/InitializeAnalytics";
import MigrationAuthenticateController from "@src/controllers/MigrationAuthenticateController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import config from "../../config";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(logLocale)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .use(initializeAnalytics)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = flags.isPerProgramProfileEnabled()
      ? ApiContainer.get(AuthenticateController)
      : flags.isInterestedCreatorFlowEnabled()
      ? ApiContainer.get(LegacyAuthenticateController)
      : ApiContainer.get(MigrationAuthenticateController);

    await controller.handle(req, res);
  });

export default router.handler({ onError });
