import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsApplicationPending from "../../config/translations/application-pending";
import labelsCommon from "../../config/translations/common";
import flags from "../../utils/feature-flags";
import { ApplicationPendingPage } from "@components/pages/interested-creators/ApplicationPendingPage";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import InterestedCreatorApplication from "../../src/interestedCreators/InterestedCreatorApplication";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import config from "../../config";
import InterestedCreatorApplicationStatus from "../../src/interestedCreators/InterestedCreatorApplicationStatus";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Layout, { LayoutBody } from "../../components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationPendingPageProps from "@src/serverprops/ApplicationPendingPageProps";
import { useDependency } from "@src/context/DependencyContext";

export type ApplicationPendingProps = {
  runtimeConfiguration?: Record<string, unknown>;
  application: InterestedCreatorApplicationStatus;
  locale: string;
  showInitialMessage?: boolean;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  RE_APPLY_THRESHOLD_IN_DAYS: number;
};

export default function Pending({
  application,
  locale,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  RE_APPLY_THRESHOLD_IN_DAYS
}: ApplicationPendingProps): JSX.Element {
  const { analytics } = useDependency();
  const { t } = useTranslation(["common", "application-pending"]);
  const { applicationPendingLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);

    return {
      applicationPendingLabels: { creatorNetwork, close, ...labelsApplicationPending(t, RE_APPLY_THRESHOLD_IN_DAYS) }
    };
  }, [t]);

  const { creatorNetwork, close } = applicationPendingLabels;

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />

          <ApplicationPendingPage
            applicationPendingLabels={applicationPendingLabels}
            defaultGamerTag={application?.gamerTag}
            emailId={application?.email}
            locale={locale}
            analytics={analytics}
            canApply={application?.canApply}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={application.createdDate as unknown as string}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

type ApplicationPendingPage = {
  locale: string;
  application: InterestedCreatorApplication;
};

export const getServerSideProps: GetServerSideProps<ApplicationPendingPage> = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationPendingPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationPendingProps>;
  }

  const application = await withCreatorApplication(req, res, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isPending()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      application: Object.assign({}, application),
      ...(await serverSideTranslations(locale, ["common", "application-pending"])),
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      RE_APPLY_THRESHOLD_IN_DAYS: config.RE_APPLY_THRESHOLD_IN_DAYS,
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
