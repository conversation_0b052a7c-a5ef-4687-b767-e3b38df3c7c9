import React, { FC, memo, useEffect, useState } from "react";
import { Icon, leftArrow, Stepper } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { OpportunityLabelsType } from "./JoinOpportunityHeader";
import { useAppContext } from "../../src/context";
import { useDetectScreen } from "../../utils";

type BackButtonProps = {
  label: string;
  onGoBack: () => void;
};

export const BackButton: FC<BackButtonProps> = ({ label, onGoBack }) => {
  return (
    <button onClick={onGoBack} className="join-opportunity-breadcrumb-back-with-icon">
      <Icon icon={leftArrow} className="breadcrumb-nav breadcrumb-back-bt" />
      <span>{label}</span>
    </button>
  );
};

type JoinOpportunityStepperProps = {
  opportunitiesLabels: OpportunityLabelsType;
  opportunityId: string;
  onGoBack: () => void;
};

const JoinOpportunityStepper: FC<JoinOpportunityStepperProps> = ({ opportunitiesLabels, opportunityId, onGoBack }) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(null);
  const {
    state: { joinOpportunitySteps }
  } = useAppContext();

  const queryString = router.query.step ? `?step=${router.query.step}` : "";

  useEffect(() => {
    if (joinOpportunitySteps.length) {
      setCurrentPage(joinOpportunitySteps.find(({ href }) => href === `${router.pathname}${queryString}`) || null);
    }
  }, [joinOpportunitySteps, queryString]);

  const handleStepperNavigation = (href) => {
    const url = new URL(`${window.location.origin}${href}`);
    const step = url.searchParams.has("step") ? { step: url.searchParams.get("step") } : null;
    router.push({ pathname: url.pathname, query: { id: opportunityId, ...step } });
  };

  const isMobileOrTab = useDetectScreen(1279);

  return (
    <>
      <div className="join-opportunity-breadcrumb-back" role="navigation">
        {currentPage && !isMobileOrTab && <BackButton label={opportunitiesLabels.back} onGoBack={onGoBack} />}
      </div>
      <Stepper
        completedLabel={opportunitiesLabels.completed}
        {...{
          steps: joinOpportunitySteps,
          current: currentPage?.title,
          handleNavigate: handleStepperNavigation
        }}
      />
    </>
  );
};

export default memo(JoinOpportunityStepper);
