---
currentMenu: flows
---

# Authentication Flow

There are two types of creators that will go through authentication.

- Creators without an account
- Creators with an account

Each type of user will land on a different page based on the following criteria.

- Creators without an account
  - Creators that apply to join the Network
    - If they don't have an existing application they'll go to the **start application** page
    - If they have an existing applicatino, they'll go to their **application status** page
      - **Pending Application** page
      - **Rejected Application** page
      - **Accepted Application** page
  - Creators with an invitation to join
    - If they do not have a registration code, they'll be invited to **start** their **application process**
    - If they have a registration code
      - They'll go to the registration page if the **code is valid**
      - They'll go to the **invalid code page** if their code is invalid
- C<PERSON>s with an account
  - If their account is **disabled**, they'll be invited to **start** their **application process**
  - If they haven't completed their registration process, they'll go to the **registration page**
  - If their account is **inactive**, they'll need to **sign Terms and Conditions**
  - If their signed Terms and Conditions are not updated, they'll need to **sign Terms and Conditions**
  - If they were trying to access a specific page, and they were redirected to the login page, they'll go to **the page they were originally trying to visit**, otherwise, they'll go to their **dashboard**

The diagram below depicts the flow described above.

<div class="mermaid">
flowchart TD
    A[Start] --> B{Has a valid Electronic Arts Account?};
    B -- Yes --> C{Has a CN account?};
    B -- No --> E[/Show Error Page\];
    V -- Yes --> W{Has an existing application?};
    V -- No --> M;
    W -- No --> K[/Start Application Page\]; 
    W -- Yes --> X{Is the application waiting for review?};
    X -- Yes --> Y[/Pending Application page\];
    X -- No --> Z{Is the application rejected?};
    Z -- Yes --> F[/Rejected Application page\];
    Z -- No --> L[/Accepted Application page\];
    D -- Yes --> M[/No Account Page\];
    D -- No --> O{Is account unregistered?};
    O -- No --> P{Is account inactive?};
    O -- Yes --> I;
    P -- Yes --> Q[/T&Cs Page\];
    P -- No --> R{Are T&Cs up to date?};
    R -- No --> Q;
    R -- Yes --> S{Has an initial page?};
    S -- Yes --> T[/Initial page\];
    S -- No --> U[/Dashboard\];
    C -- Yes --> D{Is account disabled?};
    C -- No --> G{Has a registration code?};
    G -- Yes --> H{Is the code valid?};
    G -- No --> V{Is an Interested Creator?};
    H -- Yes --> I[/Start Registration Page\];
    H -- No --> J[/Invalid Code Page\];
</div>
