import "reflect-metadata";
import { NextApiRequest, NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import DiscordClient from "../../src/channels/discord/DiscordClient";
import { createRouter } from "next-connect";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequest, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get((_req: NextApiRequest, res: NextApiResponse) => {
    const client = ApiContainer.get(DiscordClient);

    res.setHeader("Location", client.authorizationUrl());
    res.status(302);
    res.end();
  });

export default router.handler({ onError });
