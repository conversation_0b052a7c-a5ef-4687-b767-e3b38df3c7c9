import React from "react";
import { Meta, StoryObj } from "@storybook/react";
import { AccountDeactivatedPage } from "../components/pages/AccountDeactivatedPage";

/**
 * This page will be shown to creators whose account has been deactivated
 */
const meta: Meta<typeof AccountDeactivatedPage> = {
  title: "Creator Network/Pages/Interested Creators/Account Deactivated Page",
  component: AccountDeactivatedPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof AccountDeactivatedPage>;

/**
 * Default copy shown to creators whose account has been deactivated
 */
export const Page: Story = {
  args: {
    loginWithDifferentAccount: "Log In With Different Account",
    title: "Your account has been disabled",
    username: "<PERSON><PERSON><PERSON>",
    image: "img/signup/characters-signup.png",
    t: (_, { username }) =>
      `Please contact your community manager to reactivate this account “${username}” or log in using a different Electronic Arts account.`
  }
};
