import { act, render, screen, waitFor } from "@testing-library/react";
import {
  ApplicationAcceptedLabels,
  ApplicationAcceptedPage
} from "../../../../components/pages/interested-creators/ApplicationAcceptedPage";
import { axe } from "jest-axe";
import { applicationAcceptedTranslations } from "../../../translations/index";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";

describe("ApplicationAcceptedPage", () => {
  const { applicationAcceptedLabels }: { applicationAcceptedLabels: ApplicationAcceptedLabels } =
    applicationAcceptedTranslations;
  const locale = "en-us";
  const analytics = { checkedApplicationStatus: jest.fn() } as unknown as BrowserAnalytics;
  const applicationAcceptedProps = {
    applicationAcceptedLabels,
    locale,
    analytics
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows interested creator application accepted labels", async () => {
    render(<ApplicationAcceptedPage {...applicationAcceptedProps} />);

    expect(screen.getByText(applicationAcceptedLabels.title)).toBeInTheDocument();
    expect(screen.getByText(applicationAcceptedLabels.descriptionPara1)).toBeInTheDocument();
    expect(screen.getByText(applicationAcceptedLabels.descriptionPara2)).toBeInTheDocument();
    expect(screen.getByText(applicationAcceptedLabels.descriptionPara3)).toBeInTheDocument();
    expect(screen.getByText(applicationAcceptedLabels.returnToCreatorNetwork)).toBeInTheDocument();
    await waitFor(() => {
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledTimes(1);
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledWith({ locale, status: "Accepted" });
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationAcceptedPage {...applicationAcceptedProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
