import { Inject, Service } from "typedi";
import HttpClient from "../HttpClient";

@Service()
export default class {{pascalCase ApiResourceName}}HttpClient {
  constructor(@Inject("operationsClient") private client: HttpClient) {}

  async callApi({{camelCase ApiResourceName}}Id: string): Promise<void> {
    await this.client.get(`{{ApiEndpointUrl}}/${ {{camelCase ApiResourceName}}Id }`);
    return Promise.resolve();
  }
}