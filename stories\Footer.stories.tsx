import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import Footer from "../components/forms/Footer";

const meta: Meta<typeof Footer> = {
  title: "Creator Network/Pages/Interested Creators/Footer",
  component: Footer,
  decorators: [
    (Story) => (
      <div className="storybook-dark" style={{ height: "100vh", width: "100vw" }}>
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof Footer>;

/**
 * To disable next button you need to set `disableSubmit` to `true
 */
export const DisabledNextButton: Story = {
  args: {
    buttons: { cancel: "Cancel", next: "Next" },
    onCancel: () => {},
    disableSubmit: true,
    isPending: false
  }
};

export const EnabledNextButton: Story = {
  args: {
    buttons: { cancel: "Cancel", next: "Next" },
    onCancel: () => {},
    disableSubmit: false,
    isPending: false
  }
};

/**
 * In order to show creators that an operation is in progress you'll show a spinner in the next button.
 *
 * To show the spinner you need to set `isPending` to true.
 *
 * You'll usually want to disable the button while showing the spinner
 */
export const SpinnerButton: Story = {
  args: {
    buttons: { cancel: "Cancel", next: "Next" },
    onCancel: () => {},
    disableSubmit: true,
    isPending: true
  }
};

/**
 * Use the prop `button.next` if you want to change the label of the next button
 */
export const EnabledSubmitButton: Story = {
  args: {
    buttons: { cancel: "Cancel", next: "Submit" },
    onCancel: () => {},
    disableSubmit: false,
    isPending: false
  }
};
