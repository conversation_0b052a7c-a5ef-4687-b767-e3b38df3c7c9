import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import SaveUploadedContentController from "../../src/controllers/SaveUploadedContentController";
import UpdateFileContentController from "../../src/controllers/UpdateFileContentController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(SaveUploadedContentController);
    await controller.handle(req, res);
  })
  .put(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(UpdateFileContentController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
