import "reflect-metadata";
import { fireEvent, render, screen, waitFor, within } from "@testing-library/react";
import InterestedCreatorsInformationPage, {
  InterestedCreatorsInformationPageProps,
  YOUTUBE_NO_CHANNEL_ERROR
} from "@components/pages/interested-creators/InterestedCreatorsInformationPage";
import {
  commonTranslations,
  communicationPreferencesTranslations,
  connectAccountTranslations,
  informationTranslations,
  labels,
  profileLabels
} from "../../../translations";
import CreatorForm from "@components/FormRules/CreatorForm";
import { Information, Rules } from "../../../../pages/interested-creators/information";
import { useRouter } from "next/router";
import { axe } from "jest-axe";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../../../helpers/page";
import { aConnectedAccount } from "../../../factories/creators/ConnectedAccounts";
import { mockWindowReload, restoreWindowReload } from "../../../helpers/window";
import { triggerAnimationEnd } from "../../../helpers/toast";
import { useState } from "react";
import { WINDOW_PARAMS } from "../../../../utils";
import { aLocalizedDate } from "__tests__/factories/LocalizedDateBuilder";
import { toast } from "react-toastify";
import ConnectedAccountsService from "@src/api/services/ConnectedAccountsService";
import InterestedCreatorsServices, { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import SubmittedContentService from "@src/api/services/SubmittedContentService";
import { useDependency } from "@src/context/DependencyContext";
import { aCountry, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../../src/context/DependencyContext");
jest.mock("../../../../src/api/services/ConnectedAccountsService", () => {
  return {
    ...jest.requireActual("../../../../src/api/services/ConnectedAccountsService"),
    connectFbPages: jest.fn(),
    clearAccountType: jest.fn(),
    getConnectedAccounts: jest.fn(),
    getFacebookPages: jest.fn(),
    removeConnectedAccount: jest.fn(),
    getConnectAccountErrors: jest.fn(),
    deleteConnectedAccountErrors: jest.fn(),
    getAllConnectedAccountsWithExpirationStatus: jest.fn()
  };
});
jest.mock("../../../../src/api/services/InterestedCreatorsServices");
jest.mock("../../../../src/api/services/SubmittedContentService", () => {
  return {
    ...jest.requireActual("../../../../src/api/services/SubmittedContentService"),
    validateContent: jest.fn()
  };
});
jest.mock("../../../../utils", () => ({
  ...(jest.requireActual("../../../../utils") as Record<string, unknown>),
  onToastClose: jest.fn(),
  errorHandling: jest.fn()
}));
jest.mock("react-toastify", () => {
  const originalModule = jest.requireActual("react-toastify");
  return {
    ...originalModule,
    toast: {
      ...originalModule.toast,
      dismiss: jest.fn()
    }
  };
});

const addAccounts = [
  ["YOUTUBE", "YouTube"],
  ["INSTAGRAM", "Instagram"],
  ["TWITCH", "Twitch"],
  ["TIKTOK", "Tiktok"]
];

const connectedAccountErrors = [
  [
    "YOUTUBE",
    "save-you-tube-account-unknown-connected-account",
    "Oops! Something has gone wrong.",
    "Unable to add YouTube account as no channel is associated with it."
  ],
  [
    "INSTAGRAM",
    "save-instagram-account-cannot-connect-account",
    "Unable to connect Instagram account",
    "Only one Instagram account can be connected to your Creator Network profile at a time"
  ],
  [
    "INSTAGRAM",
    "save-instagram-account-unknown-instagram-business-account",
    "Action required!",
    "To connect your Instagram account you must first link it to your Facebook profile."
  ],
  ["TWITCH", "no_write_scope", "Oops! Something has gone wrong.", "Oops! Something has gone wrong."],
  ["TIKTOK", "invalid-tiktok-scope", "Oops! Something has gone wrong.", "Oops! Something has gone wrong."]
];

describe("InterestedCreatorsInformationPage", () => {
  const { formLabels, pageLabels } = labels;
  const locale = "en-us";
  const allRules = {
    ...CreatorForm.rules(informationTranslations),
    ...CreatorForm.communicationRules(communicationPreferencesTranslations)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url };
  const interestedCreator = {
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    dateOfBirth: dateOfBirthAfter18years,
    contentUrls: [{ url: "", followers: "" }],
    country: {
      value: "CA",
      label: "Canada",
      name: "Canada"
    }
  };
  const countries = [aCountry(), aCountry(), interestedCreator.country];
  const languages = [aLanguage({ value: "en", label: "English" })];
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const interestedCreatorsInformationPageProps: InterestedCreatorsInformationPageProps = {
    formLabels,
    pageLabels,
    stableDispatch: jest.fn(),
    setShowConfirmation: jest.fn(),
    locale,
    rules,
    layout: commonTranslations,
    interestedCreator,
    router: useRouter(),
    analytics: {} as unknown as BrowserAnalytics,
    showAddConfirmation: false,
    setShowAddConfirmation: jest.fn(),
    connectAccountLabels: { ...connectAccountTranslations, ...profileLabels },
    accountToRemove: null,
    setAccountToRemove: jest.fn(),
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    pages: [],
    accessToken: "abc-23457",
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    FLAG_COUNTRIES_BY_TYPE: false
  };
  const metadataService = {
    getCountries: jest.fn().mockResolvedValue(countries),
    getLanguages: jest.fn().mockResolvedValue(languages),
    getLocales: jest.fn().mockResolvedValue(locales)
  } as unknown as MetadataService;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale }));
    (useDependency as jest.Mock).mockReturnValue({
      metadataClient: {},
      configuration: { FLAG_PER_PROGRAM_PROFILE: false }
    });
    (MetadataService as jest.Mock).mockReturnValue(metadataService);
  });

  it("shows placeholders when no creator information has been entered", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Hariraj" })
    ]);

    (useRouter as jest.Mock).mockImplementation(() => ({ locale }));

    render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);

    const multiSelectContainer: HTMLElement = await screen.findByTestId("multi-select-id");
    const { getByText } = within(multiSelectContainer);
    expect(await screen.findByPlaceholderText(/First Name/i)).toBeInTheDocument();
    expect(await screen.findByPlaceholderText(/Last Name/i)).toBeInTheDocument();
    expect(await screen.findByPlaceholderText(/Preferred Email/i)).toBeInTheDocument();
    expect(await screen.findByPlaceholderText(/Date of Birth/i)).toBeInTheDocument();
    expect(await screen.findByText(/Country\/Region/i)).toBeInTheDocument();
    expect(getByText(/Channel Language/i)).toBeInTheDocument();
  });

  it("calls its submit handler and redirects to the next page", async () => {
    const newInterestedCreator = {
      ...interestedCreator,
      firstName: "Jane",
      lastName: "Doe",
      contentUrls: [{ url: "https://www.google.com", followers: "" }],
      contentLanguages: [
        {
          value: "en",
          label: "English",
          id: "a0LK0000008epjzMAA"
        }
      ]
    } as unknown as InterestedCreator & Information;
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ push: jest.fn(), locale }));
    const router = useRouter();
    const scanResult = {
      url: newInterestedCreator.contentUrls[0].url,
      isSecure: true
    };
    const urlScanResult = { results: [scanResult] };
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Test", accountId: "DFGH3456" })
    ]);
    (InterestedCreatorsServices.saveApplication as jest.Mock).mockImplementationOnce(() => Promise.resolve());
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValueOnce({
      data: urlScanResult
    });
    renderPage(
      <InterestedCreatorsInformationPage
        {...{
          ...interestedCreatorsInformationPageProps,
          interestedCreator: newInterestedCreator,
          analytics,
          router
        }}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith({
        ...newInterestedCreator,
        country: { ...interestedCreator.country },
        preferredEmail: "<EMAIL>",
        preferredLanguage: { code: undefined, name: undefined }
      });
    });
  });

  it("is accessible", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale }));
    const { container } = render(
      <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
    );
    await waitFor(async () => expect(await screen.findByText(/Canada/i)).toBeInTheDocument());

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });

  it("shows 'Language for Communication' default value based on locale", async () => {
    render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);

    expect(await screen.findByText(/Language for Communication/i)).toBeInTheDocument();
    expect(await screen.findByText(/English/i)).toBeInTheDocument();
  });

  it("shows 'Language for Communication' value based on creator information", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale }));
    const customInterestedCreator = {
      ...interestedCreator,
      preferredLanguage: { code: "ja_JP", name: "日本語" }
    };

    render(
      <InterestedCreatorsInformationPage
        {...interestedCreatorsInformationPageProps}
        router={useRouter()}
        interestedCreator={customInterestedCreator}
      />
    );

    expect(await screen.findByText(/Language for Communication/i)).toBeInTheDocument();
    expect(await screen.findByText(/日本語/i)).toBeInTheDocument();
  });

  it("displays the 'Add accounts' section", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Hariraj" })
    ]);

    render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);

    await waitFor(() => {
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(1);
      expect(screen.getByText("Connect your Social Media Accounts")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Please connect at least one of your social media accounts with your content. You may also connect multiple accounts on each social media. This will help our team assess our compatibility."
        )
      ).toBeInTheDocument();
      expect(screen.getByRole("heading", { name: "My Accounts" })).toBeInTheDocument();
      expect(screen.getByTestId("remove-account-button-YOUTUBE-Hariraj")).toBeInTheDocument();
      expect(screen.getByRole("heading", { name: "Add Accounts" })).toBeInTheDocument();
      expect(screen.getByTestId("add-account-button-YOUTUBE")).toBeInTheDocument();
      expect(screen.getByTestId("add-account-button-INSTAGRAM")).toBeInTheDocument();
      expect(screen.getByTestId("add-account-button-FACEBOOK")).toBeInTheDocument();
      expect(screen.getByTestId("add-account-button-TWITCH")).toBeInTheDocument();
      expect(screen.getByTestId("add-account-button-TIKTOK")).toBeInTheDocument();
    });
  });

  it.each(addAccounts)(
    "refreshes the 'Add accounts' section alone without reloading the page for a %s account",
    async (accountType, accountLabel) => {
      mockWindowReload();
      window.open = jest.fn().mockReturnValue({ closed: true });
      const username = `Hari ${accountLabel}`;
      (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
      (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
      (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: accountType, isExpired: false, username: username })
      ]);

      renderPage(
        <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
      );
      // Called on initial load

      await userEvent.click(screen.getByTestId(`add-account-button-${accountType}`));

      await waitFor(() => {
        // Called for refreshing the connected accounts
        expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(2);
        expect(ConnectedAccountsService.getConnectAccountErrors).toHaveBeenCalledTimes(1);
        expect(window.location.reload).not.toBeCalled();
        expect(screen.getByText(username)).toBeInTheDocument();
        expect(screen.getByRole("heading", { name: "My Accounts" })).toBeInTheDocument();
      });
      restoreWindowReload();
    }
  );

  it("refreshes the 'Add accounts' section alone without reloading the page for showing facebook pages in modal", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Test1"
      }
    ];
    const username = "Hari Facebook";
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "FACEBOOK", isExpired: false, username })
    ]);
    (ConnectedAccountsService.getFacebookPages as jest.Mock).mockResolvedValue({ pages: facebookPages });

    renderPage(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);
    // Called on initial load

    await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));

    await waitFor(() => {
      // Called for refreshing the connected accounts
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(2);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(window.location.reload).not.toBeCalled();
      const { getByRole, getByText } = within(screen.getByRole("dialog"));
      expect(getByText("Please select a Facebook page")).toBeInTheDocument();
      expect(getByRole("radio", { name: "Test1" })).toBeInTheDocument();
    });
    restoreWindowReload();
  });

  it("refreshes only 'Add accounts' section instead of page when a facebook page is selected from modal", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Test1"
      }
    ];
    const username = "Hari Facebook";
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "FACEBOOK", isExpired: false, username: username })
    ]);
    (ConnectedAccountsService.getFacebookPages as jest.Mock).mockResolvedValue({ pages: facebookPages });
    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());

    renderPage(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);
    // Called on initial load
    await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));
    const { findByRole } = within(await screen.findByRole("dialog"));
    await userEvent.click(await findByRole("radio"));

    await userEvent.click(await findByRole("button", { name: "Connect" }));

    await waitFor(() => {
      // Called for refreshing the connected accounts
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(2);
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(2);
      expect(window.location.reload).not.toBeCalled();
    });
    restoreWindowReload();
  });

  it("open remove account dialogue when user clicks on remove account button", async () => {
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Test123" })
    ]);

    await waitFor(() => {
      const InterestedCreatorsInformationPageWrapperComponent = () => {
        const [showRemoveAccModal, setShowRemoveAccModal] = useState(false);
        return (
          <InterestedCreatorsInformationPage
            {...interestedCreatorsInformationPageProps}
            router={useRouter()}
            showRemoveAccountModal={showRemoveAccModal}
            setShowRemoveAccountModal={setShowRemoveAccModal}
          />
        );
      };
      renderPage(<InterestedCreatorsInformationPageWrapperComponent />);
    });

    await userEvent.click(await screen.findByTestId("remove-account-button-YOUTUBE-Test123"));

    const { getByRole, getByText } = within(await screen.findByRole("dialog"));
    expect(getByText(connectAccountTranslations.modal.removeAccountDescription1)).toBeInTheDocument();
    expect(getByText(connectAccountTranslations.modal.removeAccountDescription2)).toBeInTheDocument();
    expect(getByRole("button", { name: connectAccountTranslations.buttons.remove })).toBeInTheDocument();
    expect(getByRole("button", { name: connectAccountTranslations.buttons.cancel })).toBeInTheDocument();
  });

  it("removes a connected account", async () => {
    const accountId = "a1LDF00000K4z0i2AB";
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: accountId, type: "YOUTUBE", isExpired: false, username: "Test123" }),
      aConnectedAccount({ id: accountId, type: "YOUTUBE", isExpired: false, username: "Test123" })
    ]);
    (ConnectedAccountsService.removeConnectedAccount as jest.Mock).mockImplementation(() => Promise.resolve());
    const InterestedCreatorsInformationPageWrapperComponent = () => {
      const [showRemoveAccModal, setShowRemoveAccModal] = useState(false);
      const [removeAccount, setRemoveAccount] = useState(null);
      return (
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          showRemoveAccountModal={showRemoveAccModal}
          setShowRemoveAccountModal={setShowRemoveAccModal}
          accountToRemove={removeAccount}
          setAccountToRemove={setRemoveAccount}
        />
      );
    };
    renderPage(<InterestedCreatorsInformationPageWrapperComponent />);
    const removeAccountButtons = await screen.findAllByRole("button", {
      name: connectAccountTranslations.removeAccount
    });
    await userEvent.click(removeAccountButtons[0]);
    const { getByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getByRole("button", { name: connectAccountTranslations.buttons.remove }));

    await waitFor(() => {
      expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledTimes(1);
    });
  });

  it("allows to refetch the facebook pages after removing a connected facebook account & connecting back", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Test1"
      }
    ];
    const accountId = "a1LDF00000K4z0i2AB";
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: accountId, type: "YOUTUBE", isExpired: false, username: "Test123" }),
      aConnectedAccount({ id: accountId, type: "FACEBOOK", isExpired: false, username: "Hariea Test" })
    ]);
    (ConnectedAccountsService.removeConnectedAccount as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getFacebookPages as jest.Mock).mockResolvedValue({ pages: facebookPages });

    const InterestedCreatorsInformationPageWrapperComponent = () => {
      const [showRemoveAccModal, setShowRemoveAccModal] = useState(false);
      const [removeAccount, setRemoveAccount] = useState(null);
      return (
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          showRemoveAccountModal={showRemoveAccModal}
          setShowRemoveAccountModal={setShowRemoveAccModal}
          accountToRemove={removeAccount}
          setAccountToRemove={setRemoveAccount}
        />
      );
    };
    renderPage(<InterestedCreatorsInformationPageWrapperComponent />);
    const removeAccountButtons = await screen.findAllByRole("button", {
      name: connectAccountTranslations.removeAccount
    });
    await userEvent.click(removeAccountButtons[0]);
    const { getByRole } = within(await screen.findByRole("dialog"));
    await userEvent.click(getByRole("button", { name: connectAccountTranslations.buttons.remove }));
    await waitFor(() => {
      expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledTimes(1);
    });

    await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));

    await waitFor(() => {
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(3);
      expect(ConnectedAccountsService.getFacebookPages).toHaveBeenCalledTimes(1);
      expect(window.location.reload).not.toBeCalled();
      const { getByRole, getByText } = within(screen.getByRole("dialog"));
      expect(getByText("Please select a Facebook page")).toBeInTheDocument();
      expect(getByRole("radio", { name: "Test1" })).toBeInTheDocument();
    });
    restoreWindowReload();
  });

  it("open facebook pages modal after connecting selected page successfully", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Test1"
      },
      {
        accessToken: "dfkshfkjsh",
        id: "r345",
        name: "Test2"
      }
    ];
    const username = "Hari Facebook";
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ type: "FACEBOOK", isExpired: false, username: username })
    ]);
    (ConnectedAccountsService.getFacebookPages as jest.Mock).mockResolvedValue({ pages: facebookPages });

    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());

    renderPage(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />);
    // Called on initial load
    await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));
    const { findByRole, getByRole } = within(await screen.findByRole("dialog"));
    await userEvent.click(getByRole("radio", { name: "Test1" }));
    await userEvent.click(await findByRole("button", { name: "Connect" }));
    await waitFor(() => {
      // Called for refreshing the connected accounts
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(2);
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(2);
      expect(window.location.reload).not.toBeCalled();
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });

    await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));

    const { getByText, getAllByRole } = within(await screen.findByRole("dialog"));
    expect(getByText("Please select a Facebook page")).toBeInTheDocument();
    expect(getAllByRole("radio")).toHaveLength(2);
    restoreWindowReload();
  });

  it("shows toast message when a Google account doesn't have a YouTube channel", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });

    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: "a1LDF00000K4z0i2AB", type: "YOUTUBE", isExpired: false, username: "Test123" })
    ]);
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue({
      code: YOUTUBE_NO_CHANNEL_ERROR
    });
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());

    const { unmount } = renderPage(
      <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
    );

    await userEvent.click(await screen.findByTestId("add-account-button-YOUTUBE"));

    await waitFor(() => {
      const toastContainer = screen.getByRole("alert");
      const { getByRole, getByText } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
      expect(getByText(connectAccountTranslations.messages.youtubeNoChannelError)).toBeInTheDocument();
      expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();
    });
    unmount();
  });

  it("shows toast message when the user deselected the required scopes while connecting a Facebook account", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });

    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: "a1LDF00000K4z0i2AB", type: "FACEBOOK", isExpired: false, username: "Test123" })
    ]);
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue({
      code: "invalid-facebook-scope",
      message: "Cannot connect a Facebook Page, not enough authorization scopes were selected"
    });
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());

    const { unmount } = renderPage(
      <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
    );

    await userEvent.click(await screen.findByTestId("add-account-button-FACEBOOK"));

    await waitFor(() => {
      const toastContainer = screen.getByRole("alert");
      const { getByRole } = within(toastContainer);
      expect(getByRole("heading", { name: "Oops! Something has gone wrong." })).toBeInTheDocument();
      expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();
    });
    unmount();
  });

  it.each(connectedAccountErrors)(
    "shows toast message on connect a %s account returns with error code `%s`",
    async (accountType, errorCode, expectedMessage) => {
      jest.useFakeTimers();
      (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([aConnectedAccount()]);
      (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue({
        code: errorCode
      });
      (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
      (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());

      window.open = jest.fn().mockReturnValue({ closed: true });
      const { unmount } = renderPage(
        <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
      );

      fireEvent.click(screen.getByTestId(`add-account-button-${accountType}`));

      const { getByRole } = within(await screen.findByRole("alert"));
      expect(getByRole("heading")).toHaveTextContent(expectedMessage);
      expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();
      triggerAnimationEnd(screen.getByRole("heading", { name: expectedMessage }));
      unmount();
      jest.useRealTimers();
    }
  );

  it("closes the existing toast message on click of 'Add Account' button", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: "a1LDF00000K4z0i2AB", type: "YOUTUBE", isExpired: false, username: "Test123" })
    ]);
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue({
      code: YOUTUBE_NO_CHANNEL_ERROR
    });
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    const { unmount } = renderPage(
      <InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} router={useRouter()} />
    );

    await userEvent.click(await screen.findByTestId("add-account-button-YOUTUBE"));

    await waitFor(() => {
      expect(toast.dismiss).toHaveBeenCalledTimes(1);
    });
    unmount();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' flag enabled", () => {
    it("pre-populates country and content urls", async () => {
      (ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Test1", accountId: 1234 }),
        aConnectedAccount({ type: "YOUTUBE", isExpired: true, username: "Test2", accountId: 2345 })
      ]);
      const interestedCreatorInformation = {
        nucleusId: 1234567,
        defaultGamerTag: "RiffleShooter",
        originEmail: "<EMAIL>",
        dateOfBirth: dateOfBirthAfter18years,
        countryCode: "CA",
        contentAccounts: [
          {
            url: "https://qa-creatornetwork.ea.com/",
            followers: null
          },
          {
            url: "https://dev-creatornetwork.ea.com/",
            followers: null
          }
        ]
      };
      const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
      (useRouter as jest.Mock).mockImplementation(() => ({ push: jest.fn(), locale }));
      const router = useRouter();

      render(
        <InterestedCreatorsInformationPage
          {...{
            ...interestedCreatorsInformationPageProps,
            interestedCreator: interestedCreatorInformation,
            analytics,
            router
          }}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      expect(await screen.findByText("Canada")).toBeInTheDocument();
      expect(await screen.findByDisplayValue(interestedCreatorInformation.contentAccounts[0].url)).toBeInTheDocument();
      expect(await screen.findByDisplayValue(interestedCreatorInformation.contentAccounts[1].url)).toBeInTheDocument();
    });

    it("displays the 'Add accounts' section", async () => {
      (ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Test1", accountId: 1234 }),
        aConnectedAccount({ type: "YOUTUBE", isExpired: true, username: "Test2", accountId: 2345 })
      ]);

      render(
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      await waitFor(() => {
        expect(ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledTimes(1);
        expect(screen.getByTestId("account-card-container-1234")).toBeInTheDocument();
        expect(screen.getByTestId("account-card-container-2345")).toBeInTheDocument();
        expect(screen.getByText("Verification Pending")).toBeInTheDocument();
        expect(screen.getByText("Click to re-verify this account")).toBeInTheDocument();
      });
    });

    it("refreshes the 'Add accounts' section without reloading the page after reconnecting an account", async () => {
      (ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Test1", accountId: 1234 }),
        aConnectedAccount({ type: "YOUTUBE", isExpired: true, username: "Test2", accountId: 2345 })
      ]);

      mockWindowReload();
      window.open = jest.fn().mockReturnValue({ closed: true });
      render(
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      await userEvent.click(await screen.findByText("Click to re-verify this account"));

      await waitFor(() => {
        expect(window.open).toHaveBeenCalledTimes(1);
        expect(window.open).toHaveBeenCalledWith(`/api/youtube-login`, "_blank", WINDOW_PARAMS);
        expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
        expect(window.location.reload).not.toBeCalled();
      });
    });

    it("disables 'Next' button when form has only one connected account with expired state", async () => {
      (ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: "YOUTUBE", isExpired: true, username: "Test2", accountId: 2345 })
      ]);

      mockWindowReload();
      window.open = jest.fn().mockReturnValue({ closed: true });
      render(
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      expect(await screen.findByRole("button", { name: "Next" })).toBeDisabled();
    });

    it("shows facebook pages modal in re-verify flow and successfully connects to a page", async () => {
      mockWindowReload();
      window.open = jest.fn().mockReturnValue({ closed: true });
      const facebookPages = [
        {
          accessToken: "asdhgkfdgh",
          id: "s1556",
          name: "Test1"
        }
      ];
      const username = "Test Facebook";
      (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
      (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValue(null);
      (ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus as jest.Mock).mockResolvedValue([
        aConnectedAccount({ type: "FACEBOOK", isExpired: false, username: username })
      ]);
      (ConnectedAccountsService.getFacebookPages as jest.Mock).mockResolvedValue({ pages: facebookPages });
      (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());

      renderPage(
        <InterestedCreatorsInformationPage
          {...interestedCreatorsInformationPageProps}
          router={useRouter()}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );
      // Called on initial load
      await userEvent.click(await screen.findByTestId(`add-account-button-FACEBOOK`));
      const { findByRole } = within(await screen.findByRole("dialog"));
      await userEvent.click(await findByRole("radio"));

      await userEvent.click(await findByRole("button", { name: "Connect" }));

      await waitFor(() => {
        // Called for refreshing the connected accounts
        expect(ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledTimes(2);
        expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
        expect(window.location.reload).not.toBeCalled();
      });
      restoreWindowReload();
    });

    describe("with 'FLAG_COUNTRIES_BY_TYPE' flag enabled", () => {
      it("pre-populates country in information page", async () => {
        metadataService.getCountriesMatching = jest.fn().mockResolvedValue(countries);
        const interestedCreatorInformation = {
          nucleusId: 1234567,
          defaultGamerTag: "RiffleShooter",
          originEmail: "<EMAIL>",
          dateOfBirth: dateOfBirthAfter18years,
          countryCode: "CA",
          contentAccounts: []
        };
        const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
        (useRouter as jest.Mock).mockImplementation(() => ({ push: jest.fn(), locale }));
        const router = useRouter();

        render(
          <InterestedCreatorsInformationPage
            {...{
              ...interestedCreatorsInformationPageProps,
              interestedCreator: interestedCreatorInformation,
              analytics,
              router
            }}
            INTERESTED_CREATOR_REAPPLY_PERIOD
            FLAG_COUNTRIES_BY_TYPE
          />
        );

        await screen.findByText("Canada");
        expect(metadataService.getCountriesMatching).toHaveBeenCalledTimes(1);
        expect(metadataService.getCountriesMatching).toHaveBeenCalledWith();
      });
    });
  });
});
