import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
{{#if version}}
{{#if folderName}}
import ApiContainer from "../../../../src/ApiContainer";
import onError from "../../../../src/api/ErrorHandler";
import session from "../../../../src/middleware/Session";
import verifySession from "../../../../src/middleware/VerifySession";
import addTracingInformation from "../../../../src/middleware/AddTracingInformation";
import { NextApiRequestWithSession } from "../../../../src/middleware/types";
import {{pascalCase ApiResourceName}}Controller from "../../../../src/controllers/{{pascalCase ApiResourceName}}Controller";
{{/if}}
{{#unless folderName}}
import ApiContainer from "../../../src/ApiContainer";
import onError from "../../../src/api/ErrorHandler";
import session from "../../../src/middleware/Session";
import verifySession from "../../../src/middleware/VerifySession";
import addTracingInformation from "../../../src/middleware/AddTracingInformation";
import { NextApiRequestWithSession } from "../../../src/middleware/types";
import {{pascalCase ApiResourceName}}Controller from "../../../src/controllers/{{pascalCase ApiResourceName}}Controller";
{{/unless}}
{{else}}
{{#if folderName}}
import ApiContainer from "../../../src/ApiContainer";
import onError from "../../../src/api/ErrorHandler";
import session from "../../../src/middleware/Session";
import verifySession from "../../../src/middleware/VerifySession";
import addTracingInformation from "../../../src/middleware/AddTracingInformation";
import { NextApiRequestWithSession } from "../../../src/middleware/types";
import {{pascalCase ApiResourceName}}Controller from "../../../src/controllers/{{pascalCase ApiResourceName}}Controller";
{{/if}}
{{#unless folderName}}
import ApiContainer from "../../src/ApiContainer";
import onError from "../../src/api/ErrorHandler";
import session from "../../src/middleware/Session";
import verifySession from "../../src/middleware/VerifySession";
import addTracingInformation from "../../src/middleware/AddTracingInformation";
import { NextApiRequestWithSession } from "../../src/middleware/types";
import {{pascalCase ApiResourceName}}Controller from "../../src/controllers/{{pascalCase ApiResourceName}}Controller";
{{/unless}}
{{/if}}

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTracingInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get({{pascalCase ApiResourceName}}Controller);
    await controller.execute(req, res);
  });

export default router.handler({ onError });


