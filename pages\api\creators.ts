import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import { createRouter } from "next-connect";
import User from "../../src/authentication/User";
import SaveCreatorProfileAction from "../../src/actions/Creators/SaveCreatorProfile/SaveCreatorProfileAction";
import SaveCreatorProfileInput from "../../src/actions/Creators/SaveCreatorProfile/SaveCreatorProfileInput";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(SaveCreatorProfileAction);
    const user = req.session.user as User;
    const code = req.session.registrationCode as string;

    const input = await action.execute(new SaveCreatorProfileInput(user, req.body, code));

    if (user.id === null && code && input?.id) {
      const creator = User.from(user);
      creator.updateId(input.id);
      delete req.session.registrationCode;
      req.session.user = creator;
      req.session.save();
    }

    res.status(201).end();
  })
  .put(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(SaveCreatorProfileAction);
    const user = req.session.user as User;

    const response = await action.execute(new SaveCreatorProfileInput(user, req.body));
    if (response?.accountInformation?.status && user.status !== response?.accountInformation?.status) {
      user.status = response?.accountInformation?.status;
      req.session.user = user;
      req.session.save();
    }

    res.status(201).json(response);
    res.end();
  });

export default router.handler({ onError });
