import { DropDownMenu } from "@eait-playerexp-cn/core-ui-kit";
import NavLink from "./WebNavLink";
import { useRouter } from "next/router";
import Link from "next/link";
import { ComponentType, useCallback } from "react";
import ProfileDropdown from "./ProfileDropdown";
import Loading from "@components/Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";

const NotificationsBell: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationsBell"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

const HeaderWeb = ({ labels, user, interestedCreator, notificationsLabels, analytics }) => {
  const router = useRouter();
  const {
    notificationsClient,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      PROGRAM_CODE,
      DEFAULT_NOTIFICATION_PROGRAM,
      FLAG_NEW_NAVIGATION_ENABLED
    }
  } = useDependency();
  const onMenuChange = useCallback(
    (item) => {
      router.push(item.value);
    },
    [router]
  );

  return (
    <div className="header-web-container">
      {/* Below method user.status === "ACTIVE" should be removed. isActive() method should be added in Identity  */}
      <Link href={user?.status === "ACTIVE" ? "/dashboard" : "/"} className="header-web-ea-title-container">
        <img
          src="/img/icons/logo-ea-medallion.svg"
          alt="EA Creator Network Logo"
          aria-label={labels.creatorNetworkHomepage}
          className="ea-icon-header-web"
        />
        <img
          src="/img/icons/logo-creator-network-wordmark.svg"
          alt="EA Creator Network Logo"
          aria-label={labels.creatorNetworkHomepage}
        />
      </Link>
      <div className="header-web-nav-col">
        {user?.status === "ACTIVE" ? (
          <>
            <NavLink title={labels.dashboard} current={router.pathname === "/dashboard"} href="/dashboard" />
            <NavLink
              title={labels.opportunities}
              current={router.pathname === "/opportunities"}
              href="/opportunities"
            />
            <NavLink title={labels.myContent} current={router.pathname === "/my-content"} href="/my-content" />
            <div className="header-web-select">
              <DropDownMenu
                label={labels.about}
                menuItems={[
                  { label: labels.how, value: "/how-it-works" },
                  { label: labels.perks, value: "/opportunities-rewards" },
                  { label: labels.faqs, value: "/faq" },
                  { label: labels.policy, value: "/trust-and-safety-guidelines" },
                  { label: labels.disclosure, value: "/disclosure" }
                ]}
                onMenuItemSelect={(item) => {
                  router.push(item.value);
                }}
                classes="header-web-drop-down"
              />
            </div>
          </>
        ) : (
          <>
            <NavLink title={labels.home} current={router.pathname === "/"} href="/" />
            <NavLink title={labels.works} current={router.pathname === "/how-it-works"} href="/how-it-works" />
            <NavLink
              title={labels.rewards}
              current={router.pathname === "/opportunities-rewards"}
              href="/opportunities-rewards"
            />
            <div className="header-web-select">
              <DropDownMenu
                classes="header-web-drop-down"
                label={labels.about}
                menuItems={[
                  { label: labels.faqs, value: "/faq" },
                  { label: labels.policy, value: "/trust-and-safety-guidelines" },
                  { label: labels.disclosure, value: "/disclosure" }
                ]}
                onMenuItemSelect={(item) => onMenuChange(item)}
              />
            </div>
          </>
        )}
      </div>
      <div className="header-web-action-btn-col">
        {user?.status === "ACTIVE" ? (
          <>
            <NotificationsBell
              labels={notificationsLabels}
              locale={router.locale}
              configuration={{
                client: notificationsClient,
                programHosts: NOTIFICATION_BASE_URLS,
                program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
                defaultProgram: DEFAULT_NOTIFICATION_PROGRAM,
                enableNewNavigation: FLAG_NEW_NAVIGATION_ENABLED
              }}
            />
            <ProfileDropdown labels={labels} analytics={analytics}>
              <img
                alt="Your avatar"
                className="ea-header-web-profile-image"
                src={user.avatar ? user.avatar : "/img/icons/GenericAvatar.png"}
              />
            </ProfileDropdown>
          </>
        ) : (
          <>
            {interestedCreator && (
              <Link href="/interested-creators/start" className="btn btn-tertiary btn-dark btn-md">
                {labels.requestToJoin}
              </Link>
            )}
            <Link href="/api/login" className="signup-page-apply-button btn btn-primary btn-md">
              {interestedCreator ? labels.logIn : labels.signIn}
            </Link>
          </>
        )}
      </div>
    </div>
  );
};
export default HeaderWeb;
