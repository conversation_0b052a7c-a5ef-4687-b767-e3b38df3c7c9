import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Form from "../Form";
import CreatorsService from "../../src/api/services/CreatorsService";
import CreatorForm from "../FormRules/CreatorForm";
import PreferredEmailForm from "./forms/PreferredEmailForm";
import PreferredPhoneNumberForm from "./forms/PreferredPhoneNumberForm";
import PreferredLanguageForm from "./forms/PreferredLanguageForm";
import PreferredContentLanguagesForm from "./forms/PreferredContentLanguagesForm";
import { AddDiscordAccount } from "../migrations/CommunicationPreferencesInput";
import { ERROR, onToastClose, toastContent, useAsync, useIsMounted, VALIDATION_ERROR } from "../../utils";
import { useAppContext } from "@src/context";
import Loading from "../Loading";
import FormTitle from "../formTitle/FormTitle";
import { Toast, useToast } from "../toast";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export default memo(function CommunicationPreferences({ translations, buttons, labels, layout, analytics, locale }) {
  const {
    main: { unhandledError }
  } = layout;
  const isMounted = useIsMounted();
  const { dispatch, state: { popupOpened = false, isValidationError, isError } = {} } = useAppContext() || {};
  const { error: errorToast } = useToast();
  const stableDispatch = useCallback(dispatch, []);
  const [languages, setLanguages] = useState(null);
  const [locales, setLocales] = useState(null);
  const [isEmailSaved, setIsEmailSaved] = useState(false);
  const [isPhoneSaved, setIsPhoneSaved] = useState(false);
  const [isLanguageSaved, setIsLanguageSaved] = useState(false);
  const [isContentLanguageSaved, setIsContentLanguageSaved] = useState(false);
  const [communicationPreferences, setCommunicationPreferences] = useState(null);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const [removeAccount, setRemoveAccount] = useState(null);
  const [accountToRemove, setAccountToRemove] = useState(false);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);
  const rules = useMemo(() => CreatorForm.communicationRules(translations), [translations]);
  const { discord = null } = useMemo(() => communicationPreferences || {}, [communicationPreferences]);
  const {
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const updateCreator = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        let preferredLanguage;
        if (data.preferredLanguage) {
          data.preferredLanguage = {
            ...data.preferredLanguage,
            code: data.preferredLanguage.value,
            name: data.preferredLanguage.label
          };
          preferredLanguage = data.preferredLanguage;
        } else {
          preferredLanguage = {
            ...communicationPreferences.preferredLanguage,
            code: communicationPreferences.preferredLanguage.value,
            name: communicationPreferences.preferredLanguage.label
          };
        }
        const formData = { ...communicationPreferences, ...data };
        const contentLanguages = formData.contentLanguages.map((language) => ({
          ...language,
          code: language.value,
          name: language.label
        }));
        const communicationPreferencesData = {
          ...formData,
          contentLanguages
        };
        delete communicationPreferencesData.preferredLanguage;

        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              communicationPreferences: communicationPreferencesData,
              program: { code: PROGRAM_CODE, preferredLanguage }
            })
          : await CreatorsService.update({ communicationPreferences: formData });

        if (FLAG_PER_PROGRAM_PROFILE) {
          formData.preferredLanguage = preferredLanguage;
        }
        formData.contentLanguages = formData.contentLanguages.map((language) => ({
          ...language,
          code: language.value,
          name: language.label
        }));
        setCommunicationPreferences(formData);
        setIsEmailSaved(formData.email !== undefined);
        setIsPhoneSaved(formData.phone !== undefined);
        setIsLanguageSaved(preferredLanguage !== undefined);
        setIsContentLanguageSaved(formData.contentLanguages !== undefined);
        analytics.confirmedCommunicationPreferences({
          locale,
          contentLanguages: formData.contentLanguages?.map((language) => language.label).join(",")
        });
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [communicationPreferences, stableDispatch, locale]
  );

  const emailOnChange = useCallback(() => setIsEmailSaved(false), []);
  const phoneOnChange = useCallback(() => setIsPhoneSaved(false), []);
  const contentLanguagesOnChange = useCallback(() => setIsContentLanguageSaved(false), []);
  const languageOnChange = useCallback(() => setIsLanguageSaved(false), []);

  useEffect(() => {
    metadataService
      .getLanguages()
      .then((languages) => setLanguages(languages))
      .catch((e) => errorHandler(stableDispatch, e));
    metadataService
      .getLocales()
      .then((locales) => setLocales(locales))
      .catch((e) => errorHandler(stableDispatch, e));
  }, []);

  //----------------------------------
  // ReFetch Creators on window closed
  //----------------------------------
  useEffect(() => {
    async function fetchData() {
      if ((!popupOpened && showAddConfirmation) || !accountToRemove) {
        try {
          if (isMounted()) {
            // Creators BFF GET
            const creator = FLAG_PER_PROGRAM_PROFILE
              ? await creatorService.getCreator(PROGRAM_CODE)
              : (await CreatorsService.getCreatorWithPayableStatus()).data;
            if (FLAG_PER_PROGRAM_PROFILE) {
              creator.communicationPreferences.contentLanguages = creator.communicationPreferences.contentLanguages.map(
                (language) => ({
                  ...language,
                  value: language.code,
                  label: language.name
                })
              );
              if (creator.program.preferredLanguage) {
                creator.communicationPreferences.preferredLanguage = {
                  ...creator.program.preferredLanguage,
                  value: creator.program.preferredLanguage.code,
                  label: creator.program.preferredLanguage.name
                };
              }
            } else {
              creator.communicationPreferences.contentLanguages = creator.communicationPreferences.contentLanguages.map(
                (language) => ({
                  ...language,
                  code: language.value,
                  name: language.label
                })
              );
              if (creator.communicationPreferences.preferredLanguage) {
                creator.communicationPreferences.preferredLanguage = {
                  ...creator.communicationPreferences.preferredLanguage,
                  code: creator.communicationPreferences.preferredLanguage.value,
                  name: creator.communicationPreferences.preferredLanguage.label
                };
              }
            }
            setCommunicationPreferences(creator.communicationPreferences);
            setShowAddConfirmation(false);
          }
        } catch (error) {
          errorHandler(stableDispatch, error);
        }
      }
    }
    fetchData();
  }, [popupOpened, accountToRemove, stableDispatch, showAddConfirmation]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const { pending, execute: onSubmit } = useAsync(updateCreator, false);

  return (
    (!communicationPreferences && (
      <div className="loader">
        <Loading />
      </div>
    )) || (
      <div className="profile-communication-preferences">
        <div className="profile-communication-preferences-intro">
          <FormTitle title={translations.profileTitle} />
          <div className="profile-communication-preferences-intro-description">{translations.description}</div>
        </div>
        <AddDiscordAccount
          {...{
            translation: translations,
            showAddConfirmation,
            setShowAddConfirmation,
            discord,
            layout,
            labels,
            setRemoveAccount,
            removeAccount,
            accountToRemove,
            setAccountToRemove,
            showRemoveAccountModal,
            setShowRemoveAccountModal
          }}
        />
        {communicationPreferences && rules && (
          <Form key="email" mode="onChange" onSubmit={onSubmit}>
            <PreferredEmailForm
              {...{
                translations,
                rules,
                communicationPreferences,
                buttons,
                onChange: emailOnChange,
                isSaved: isEmailSaved,
                isLoader: pending
              }}
            />
          </Form>
        )}
        {communicationPreferences && rules && (
          <Form key="phone" mode="onChange" onSubmit={onSubmit}>
            <PreferredPhoneNumberForm
              {...{
                translations,
                rules,
                communicationPreferences,
                buttons,
                onChange: phoneOnChange,
                isSaved: isPhoneSaved,
                isLoader: pending
              }}
            />
          </Form>
        )}
        {communicationPreferences && languages && rules && (
          <Form key="contentLanguages" mode="onChange" onSubmit={onSubmit}>
            <PreferredContentLanguagesForm
              {...{
                translations,
                rules,
                communicationPreferences,
                languages,
                buttons,
                onChange: contentLanguagesOnChange,
                isSaved: isContentLanguageSaved,
                isLoader: pending
              }}
            />
          </Form>
        )}
        {communicationPreferences && locales && rules && (
          <Form key="language" mode="onChange" onSubmit={onSubmit}>
            <PreferredLanguageForm
              {...{
                translations,
                rules,
                communicationPreferences,
                locales,
                buttons,
                onChange: languageOnChange,
                isSaved: isLanguageSaved,
                isLoader: pending
              }}
            />
          </Form>
        )}
      </div>
    )
  );
});
