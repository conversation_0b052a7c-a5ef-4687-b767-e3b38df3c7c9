import { act, render, screen, waitFor } from "@testing-library/react";
import { ApplicationRejectedPage } from "../../../../components/pages/interested-creators/ApplicationRejectedPage";
import { axe } from "jest-axe";
import { applicationRejectedTranslations } from "../../../translations/index";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { commonTranslations } from "../../../translations";

describe("ApplicationRejectedPage", () => {
  const applicationRejectedLabels = {
    ...applicationRejectedTranslations.applicationRejectedLabels,
    ...commonTranslations.applicantLabels
  };
  const locale = "en-us";
  const analytics = { checkedApplicationStatus: jest.fn() } as unknown as BrowserAnalytics;
  const applicationRejectedPageProps = {
    applicationRejectedLabels,
    locale,
    analytics
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows interested creator application rejected labels", async () => {
    render(<ApplicationRejectedPage {...applicationRejectedPageProps} />);

    expect(screen.getByText(applicationRejectedLabels.title)).toBeInTheDocument();
    expect(screen.getByText(applicationRejectedLabels.descriptionPara1)).toBeInTheDocument();
    expect(screen.getByText(applicationRejectedLabels.descriptionPara2)).toBeInTheDocument();
    expect(screen.getByText(applicationRejectedLabels.descriptionPara3)).toBeInTheDocument();
    expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toBeInTheDocument();
    await waitFor(() => {
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledTimes(1);
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledWith({ locale, status: "Rejected" });
    });
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows application rejected page when applicant cannot reapply before resubmit request date", async () => {
      render(
        <ApplicationRejectedPage
          {...applicationRejectedPageProps}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 02, 2024"
          emailId="<EMAIL>"
          defaultGamerTag="Test"
          reSubmitRequestDate="March 15, 2024"
        />
      );

      expect(screen.getByText(applicationRejectedLabels.submissionReviewed)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.submissionReviewedDescription)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.email)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.gamerTag)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.submissionDate)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.status)).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.closed)).toBeInTheDocument();
      expect(screen.getByText("Jan 02, 2024")).toBeInTheDocument();
      expect(screen.queryByRole("link", { name: applicationRejectedLabels.reviewAndResubmit })).not.toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toHaveClass(
        "btn-primary"
      );
    });

    it("shows application rejected page when applicant can reapply after resubmit request date", async () => {
      render(
        <ApplicationRejectedPage
          {...applicationRejectedPageProps}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 02, 2024"
          emailId="<EMAIL>"
          defaultGamerTag="Test"
          canApply={true}
        />
      );

      expect(screen.getByText(applicationRejectedLabels.reApplyTitle)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.reApplyDescription)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.email)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.gamerTag)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.submissionDate)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.status)).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.closed)).toBeInTheDocument();
      expect(screen.getByText("Jan 02, 2024")).toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.reviewAndResubmit })).toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toHaveClass(
        "btn-secondary"
      );
    });

    it("shows application rejected page when applicant is rejected for any non-eligible reason", async () => {
      render(
        <ApplicationRejectedPage
          {...applicationRejectedPageProps}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 02, 2024"
          emailId="<EMAIL>"
          defaultGamerTag="Test"
        />
      );

      expect(screen.getByText(applicationRejectedLabels.title)).toBeInTheDocument();
      expect(screen.getByText(applicationRejectedLabels.descriptionPara1)).toBeInTheDocument();
      expect(screen.queryByTestId("request-to-join-status-table")).not.toBeInTheDocument();
      expect(screen.queryByRole("link", { name: applicationRejectedLabels.reviewAndResubmit })).not.toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toBeInTheDocument();
      expect(screen.getByRole("link", { name: applicationRejectedLabels.returnToCreatorNetwork })).toHaveClass(
        "btn-primary"
      );
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationRejectedPage {...applicationRejectedPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
