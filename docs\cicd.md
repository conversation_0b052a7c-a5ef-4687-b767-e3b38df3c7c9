---
currentMenu: cicd
---

# CI/CD configuration

This repository uses the following CI/CD templates

- [COMMITS.gitlab-ci.yml](https://eait-playerexp-cn.gitlab.ea.com/cn-deployments/docs/templates/includes/commits.html).
- [DOCS.gitlab-ci.yml](https://eait-playerexp-cn.gitlab.ea.com/cn-deployments/docs/templates/includes/docs.html).
- [TEST-TALOS.gitlab-ci.yml](https://eait-playerexp-cn.gitlab.ea.com/cn-deployments/docs/templates/includes/test-talos.html).
- [EKS-DEPLOY.gitlab-ci.yml](https://eait-playerexp-cn.gitlab.ea.com/cn-deployments/docs/templates/includes/deployment.html).
- [RELEASE.gitlab-ci.yml](https://eait-playerexp-cn.gitlab.ea.com/cn-deployments/docs/templates/includes/release.html).

The following CI/CD variables are overridden

| Variable                   | Location                                                                                                                                                    | Job                      | Description                                                                       |
| -------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------ | --------------------------------------------------------------------------------- |
| `BUILD_ARGS`               | [CI/CD Configuration](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/settings/ci_cd#js-cicd-variables-settings) | `publish`                | Build arguments to be passed while building the Docker image for this application |
| `CN_SERVICE_REGISTRY`      | [CI/CD Configuration](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/settings/ci_cd#js-cicd-variables-settings) | `publish`                | Registry for pre-production Docker images. It holds stable and unstable images    |
| `CN_SERVICE_REGISTRY_PROD` | [CI/CD Configuration](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/settings/ci_cd#js-cicd-variables-settings) | `publish`                | Registry for production Docker images. It holds stable images only                |
| `SENTRY_AUTH_TOKEN`        | [CI/CD Configuration](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/settings/ci_cd#js-cicd-variables-settings) | `publish`                | Authentication token to upload source maps to Sentry                              |
| `REPLICAS`                 | [CI/CD Configuration](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/settings/ci_cd#js-cicd-variables-settings) | `dev`, `qa`, `uat`, etc. | Number of replicas to be deployed                                                 |
