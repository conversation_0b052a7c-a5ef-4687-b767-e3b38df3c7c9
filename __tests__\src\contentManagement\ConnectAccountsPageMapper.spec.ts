import { ConnectAccountsPageMapper } from "@src/contentManagement/ConnectAccountsPageMapper";

describe("ConnectAccountsPageMapper", () => {
  const microCopies = {
    "connectAccounts.subscribers": "Subscribers",
    "connectAccounts.modalTitle": "Modal Title",
    "connectAccounts.messages.actionTitle": "Action Title",
    "connectAccounts.messages.actionDescription1": "Action Description 1",
    "connectAccounts.messages.actionDescription2": "Action Description 2",
    "connectAccounts.messages.actionDescription3": "Action Description 3",
    "connectAccounts.messages.actionDescription4": "Action Description 4",
    "connectAccounts.messages.cannotConnectInstaAccount": "Cannot Connect Instagram Account",
    "connectAccounts.messages.cannotConnectInstaAccountHeader": "Instagram Connection Error",
    "connectAccounts.messages.removeAccountDescription": "Remove Account Description",
    "connectAccounts.messages.removeAccountTitle": "Remove Account Title",
    "connectAccounts.messages.youtubeNoChannelError": "YouTube No Channel Error",
    "connectAccounts.removeAccountTitle": "Remove Account Title",
    "connectAccounts.removeAccountDescription1": "Remove Account Description 1",
    "connectAccounts.removeAccountDescription2": "Remove Account Description 2",
    "connectAccounts.accounts.instagram": "Instagram",
    "connectAccounts.accounts.facebook": "Facebook",
    "connectAccounts.accounts.twitch": "Twitch",
    "connectAccounts.accounts.youTube": "YouTube",
    "connectAccounts.accounts.tiktok": "TikTok",
    "connectAccounts.cancel": "Cancel",
    "connectAccounts.remove": "Remove",
    "connectAccounts.connect": "Connect",
    "connectAccounts.confirmationDesc1": "Confirmation Description 1",
    "connectAccounts.confirmationDesc2": "Confirmation Description 2",
    "connectAccounts.reconnectAccount": "Reconnect Account",
    "connectAccounts.modalMessage": "Modal Message",
    "connectAccounts.message": "Message",
    "connectAccounts.modalConfirmationTitleFB": "Facebook Confirmation Title",
    "connectAccounts.connectNewAccountDescription": "Connect New Account Description",
    "connectAccounts.connectNewAccount": "Connect New Account",
    "connectAccounts.reVerifyAccount": "Re-verify Account",
    "connectAccounts.myAccount": "My Account",
    "connectAccounts.or": "Or",
    "connectAccounts.description": "Description",
    "connectAccounts.comma": ",",
    "connectAccounts.connectNewAccountDescriptionWithTikTok": "Connect New Account with TikTok",
    "connectAccounts.verificationPending": "Verification Pending",
    "connectAccounts.title": "Title",
    "connectAccounts.expireAccount": "Expire Account",
    "connectAccounts.removeAccount": "Remove Account",
    "connectAccounts.subTitle": "Subtitle",
    "connectAccounts.addAccount": "Add Account"
  };

  it("maps connect accounts page labels", () => {
    const mapper = new ConnectAccountsPageMapper();
    const labels = mapper.map(microCopies).connectAccountsLabels;

    expect(labels.subscribers).toEqual("Subscribers");
    expect(labels.modalTitle).toEqual("Modal Title");
    expect(labels.messages.actionTitle).toEqual("Action Title");
    expect(labels.messages.actionDescription1).toEqual("Action Description 1");
    expect(labels.messages.actionDescription2).toEqual("Action Description 2");
    expect(labels.messages.actionDescription3).toEqual("Action Description 3");
    expect(labels.messages.actionDescription4).toEqual("Action Description 4");
    expect(labels.messages.cannotConnectInstaAccount).toEqual("Cannot Connect Instagram Account");
    expect(labels.messages.cannotConnectInstaAccountHeader).toEqual("Instagram Connection Error");
    expect(labels.messages.removeAccountDescription).toEqual("Remove Account Description");
    expect(labels.messages.removeAccountTitle).toEqual("Remove Account Title");
    expect(labels.messages.youtubeNoChannelError).toEqual("YouTube No Channel Error");
    expect(labels.modal.removeAccountTitle).toEqual("Remove Account Title");
    expect(labels.modal.removeAccountDescription1).toEqual("Remove Account Description 1");
    expect(labels.modal.removeAccountDescription2).toEqual("Remove Account Description 2");
    expect(labels.accounts.instagram).toEqual("Instagram");
    expect(labels.accounts.facebook).toEqual("Facebook");
    expect(labels.accounts.twitch).toEqual("Twitch");
    expect(labels.accounts.youTube).toEqual("YouTube");
    expect(labels.accounts.tiktok).toEqual("TikTok");
    expect(labels.cancel).toEqual("Cancel");
    expect(labels.remove).toEqual("Remove");
    expect(labels.connect).toEqual("Connect");
    expect(labels.confirmationDesc1).toEqual("Confirmation Description 1");
    expect(labels.confirmationDesc2).toEqual("Confirmation Description 2");
    expect(labels.reconnectAccount).toEqual("Reconnect Account");
    expect(labels.modalMessage).toEqual("Modal Message");
    expect(labels.message).toEqual("Message");
    expect(labels.modalConfirmationTitleFB).toEqual("Facebook Confirmation Title");
    expect(labels.connectNewAccountDescription).toEqual("Connect New Account Description");
    expect(labels.connectNewAccount).toEqual("Connect New Account");
    expect(labels.reVerifyAccount).toEqual("Re-verify Account");
    expect(labels.myAccount).toEqual("My Account");
    expect(labels.or).toEqual("Or");
    expect(labels.description).toEqual("Description");
    expect(labels.comma).toEqual(",");
    expect(labels.connectNewAccountDescriptionWithTikTok).toEqual("Connect New Account with TikTok");
    expect(labels.verificationPending).toEqual("Verification Pending");
    expect(labels.title).toEqual("Title");
    expect(labels.expireAccount).toEqual("Expire Account");
    expect(labels.removeAccount).toEqual("Remove Account");
    expect(labels.subTitle).toEqual("Subtitle");
    expect(labels.addAccount).toEqual("Add Account");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ConnectAccountsPageMapper();
    expect(() => mapper.map({})).toThrow();
  });
});