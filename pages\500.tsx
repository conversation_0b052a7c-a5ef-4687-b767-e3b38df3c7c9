import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsCommon from "../config/translations/common";
import ErrorComponent from "../components/ErrorComponent";
import { memo, useMemo } from "react";
import config from "../config";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import Footer from "../components/footer/ProgramFooter";
import flags from "../utils/feature-flags";
import { useDependency } from "@src/context/DependencyContext";

export type ErrorPageProps = {
  user?: AuthenticatedUser;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
};

export default memo(function Custom500({ user, FLAG_NEW_FOOTER_ENABLED, FLAG_NEW_NAVIGATION_ENABLED }: ErrorPageProps) {
  const { locale } = useRouter();
  const { t } = useTranslation(["common", "notifications", "connect-accounts", "opportunities"]);
  const notificationBellLabels = mapNotificationsBellLabels(t);
  const { main, header, footer, buttons } = labelsCommon(t);
  const headerLabels = { labels: { ...header, ...buttons } };
  const { analytics } = useDependency();
  const errorProps = useMemo(() => {
    return {
      code: 500,
      image: "/img/500-bg.png",
      title: main.unhandledError,
      description: main.unhandledErrorMessage,
      header: header,
      footer: footer
    };
  }, [main, header, footer]);

  return (
    <>
      {errorProps && (
        <Layout>
          <LayoutHeader pageTitle={main.unhandledError}>
            <Header
              {...headerLabels}
              user={user}
              notificationsLabels={notificationBellLabels}
              FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
              analytics={analytics}
              interestedCreator={null}
            />
          </LayoutHeader>
          <LayoutBody className="error-container-section">
            <ErrorComponent {...errorProps} />
          </LayoutBody>
          <LayoutFooter>
            <Footer
              FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
              locale={locale}
              labels={footer}
              analytics={analytics}
            />
          </LayoutFooter>
        </Layout>
      )}
    </>
  );
});

export type StaticErrorProps = {
  locale: string;
};

export const getStaticProps = async ({ locale }: StaticErrorProps) => ({
  props: {
    runtimeConfiguration: runtimeConfiguration(),
    interestedCreator: config.INTERESTED_CREATOR,
    ...(await serverSideTranslations(locale, [
      "common",
      "index",
      "notifications",
      "connect-accounts",
      "opportunities"
    ])),
    FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
    FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled()
  }
});
