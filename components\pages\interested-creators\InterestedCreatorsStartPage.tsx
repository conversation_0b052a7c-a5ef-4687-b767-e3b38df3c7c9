import { memo, useCallback, useEffect } from "react";
import ExplorePages, { PageProps } from "./ExplorePages";
import Link from "next/link";
import { PageLabels } from "../../../pages/interested-creators/start";
import { ERROR, onToastClose, toastContent, VALIDATION_ERROR } from "../../../utils";
import { Toast } from "../../toast";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { NextRouter } from "next/router";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";

export type Dispatch = {
  type: string;
  data: boolean;
};

export type InterestedCreatorsStartPageProps = {
  pageLabels: PageLabels;
  explorePages: PageProps[];
  stableDispatch: (param: Dispatch) => void;
  errorToast: (JSXElementConstructor, CloseHandler) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  state: any; // will need to revisit this later
  unhandledError: string;
  router: NextRouter;
  analytics: BrowserAnalytics;
};

export default memo(function InterestedCreatorsStartPage({
  pageLabels,
  explorePages,
  state,
  errorToast,
  stableDispatch,
  unhandledError,
  router,
  analytics
}: InterestedCreatorsStartPageProps) {
  const { title, subTitle, description, descriptionSuffix, button, alreadyApplied, alreadyAppliedSuffix, explore } =
    pageLabels;
  const { isError = false, isValidationError = false } = state;

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={pageLabels.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  const startApplication = useCallback(() => {
    analytics.startedCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/applications");
  }, [router]);

  return (
    <>
      <div className="mg-bg"> </div>
      <div className="mg-page">
        <div className="start-wrapper">
          <div className="start-thumbnail" />
          <div className="start-content-container">
            <h3 className="start-title">{title}</h3>
            <h4 className="start-sub-title">{subTitle}</h4>
            <div className="start-body">
              {description}{" "}
              <Link href="/api/accounts" className="start-ea-account">
                {descriptionSuffix}
              </Link>
            </div>
            <div className="start-apply-button">
              <Button variant="primary" size="md" onClick={startApplication}>
                {button}
              </Button>
            </div>
            <div className="start-already-applied-content-container">
              {alreadyApplied}{" "}
              <Link href="/api/applications" className="interested-creator-already-applied">
                {alreadyAppliedSuffix}
              </Link>
            </div>
          </div>
        </div>
        <ExplorePages title={explore} explorePages={explorePages} />
      </div>
    </>
  );
});
