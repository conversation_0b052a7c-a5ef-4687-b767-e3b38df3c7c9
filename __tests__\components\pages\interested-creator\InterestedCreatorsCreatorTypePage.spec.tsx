import { render, screen } from "@testing-library/react";
import InterestedCreatorsCreatorTypePage from "@components/pages/interested-creators/InterestedCreatorsCreatorTypePage";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import {
  commonTranslations,
  communicationPreferencesTranslations,
  informationTranslations,
  labels
} from "../../../translations";
import CreatorForm from "@components/FormRules/CreatorForm";
import { Rules } from "../../../../pages/interested-creators/information";
import { useRouter } from "next/router";
import { axe } from "jest-axe";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { aLocalizedDate } from "__tests__/factories/LocalizedDateBuilder";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../../src/context/DependencyContext");

describe("InterestedCreatorsCreatorTypePage", () => {
  const creatorTypes = [
    aCreatorType({ value: "YOUTUBER", label: "YouTuber" }),
    aCreatorType({ value: "BLOGGER", label: "Blogger" }),
    aCreatorType({ value: "LIFESTYLE", label: "lifestyle" })
  ];
  const { creatorsTypeLabels } = labels;
  const { formLabels, pageLabels } = creatorsTypeLabels;
  const allRules = {
    ...CreatorForm.rules(informationTranslations),
    ...CreatorForm.communicationRules(communicationPreferencesTranslations)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url };
  const interestedCreatorsCreatorTypePageProps = {
    formLabels,
    pageLabels,
    t: (str) => str,
    stableDispatch: jest.fn(),
    errorToast: jest.fn(),
    setShowConfirmation: jest.fn(),
    rules,
    locale: "en-us",
    layout: commonTranslations,
    state: {},
    router: useRouter(),
    analytics: {} as unknown as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ metadataClient: {} });
    (MetadataService as jest.Mock).mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue(creatorTypes)
    });
  });

  it("shows placeholders when no creator information has been entered", async () => {
    const interestedCreator = {
      nucleusId: 1234567,
      defaultGamerTag: "RiffleShooter",
      originEmail: "<EMAIL>",
      dateOfBirth: dateOfBirthAfter18years,
      creatorTypes
    };

    render(
      <InterestedCreatorsCreatorTypePage
        {...interestedCreatorsCreatorTypePageProps}
        interestedCreator={interestedCreator}
      />
    );

    expect(await screen.findByText(/Choose your Creator Types/i)).toBeInTheDocument();
    expect(
      await screen.findByText(/Select the types of creator that fits the content you create./i)
    ).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' flag enabled", () => {
    it("pre-populates checkboxes values", async () => {
      const interestedCreator = {
        nucleusId: 1234567,
        defaultGamerTag: "RiffleShooter",
        originEmail: "<EMAIL>",
        dateOfBirth: dateOfBirthAfter18years,
        creatorTypes: ["LIFESTYLE", "YOUTUBER"]
      };

      render(
        <InterestedCreatorsCreatorTypePage
          {...interestedCreatorsCreatorTypePageProps}
          interestedCreator={interestedCreator}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      expect(await screen.findByTestId(/creator-type:labels.lifestyle/)).toBeChecked();
      expect(await screen.findByTestId(/creator-type:labels.YouTuber/)).toBeChecked();
    });
  });

  it("is accessible", async () => {
    const { container } = render(<InterestedCreatorsCreatorTypePage {...interestedCreatorsCreatorTypePageProps} />);
    await screen.findByRole("checkbox", { name: "creator-type:labels.YouTuber" });
    screen.getByRole("checkbox", { name: "creator-type:labels.Blogger" });

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
