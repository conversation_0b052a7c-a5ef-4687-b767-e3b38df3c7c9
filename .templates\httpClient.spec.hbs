import "reflect-metadata";
import HttpClient from "../../../src/HttpClient";
{{#if folderName}}
import {{pascalCase ApiResourceName}}HttpClient from "../../../src/{{camelCase folderName}}/{{pascalCase ApiResourceName}}HttpClient";
{{else}}
import {{pascalCase ApiResourceName}}HttpClient from "../../../src/{{camelCase ApiResourceName}}/{{pascalCase ApiResourceName}}HttpClient";
{{/if}}

describe("{{pascalCase ApiResourceName}}HttpClient", () => {
  it("calls {{lowerCase (sentenceCase ApiResourceName)}} api", async () => {
    const mockGet = jest.fn().mockReturnValue({});
    const client = { get: mockGet };
    /** The payload is harcoded, need to update manually **/
    const criteria = {
      creatorId: "123",
      participationId: "a0YK0000004zHaoMAE",
      page: 1,
      size: 3
    };
    const {{camelCase ApiResourceName}}Id = "12566435";
    
    const {{camelCase ApiResourceName}} = new {{pascalCase ApiResourceName}}HttpClient((client as unknown) as HttpClient);

    await {{camelCase ApiResourceName}}.callApi({{camelCase ApiResourceName}}Id);

    expect(mockGet).toHaveBeenCalledTimes(1);
    expect(mockGet).toHaveBeenCalledWith(`{{ApiEndpointUrl}}`);
  });
});
