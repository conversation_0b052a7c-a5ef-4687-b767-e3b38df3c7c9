import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import ConnectedDiscordAccount from "@src/channels/ConnectedDiscordAccount";
import { createRouter } from "next-connect";
import User from "@src/authentication/User";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../config";
import onError from "@src/middleware/JsonErrorHandler";
import { Identity } from "@eait-playerexp-cn/identity-types";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .delete(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const params = req.query;
    const user = config.FLAG_PER_PROGRAM_PROFILE ? (req.session.identity as Identity) : (req.session.user as User);
    const discordAccount: ConnectedDiscordAccount = ApiContainer.get("discordAccount");
    await discordAccount.disconnectDiscordAccount(user.id, params.id as string);

    res.status(200);
    res.end();
  });

export default router.handler({ onError });
