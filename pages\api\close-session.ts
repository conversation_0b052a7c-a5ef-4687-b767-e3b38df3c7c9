import "reflect-metadata";
import { CloseSessionController } from "@eait-playerexp-cn/authentication";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import config from "../../config";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import onError from "@src/middleware/JsonErrorHandler";
import ApiContainer from "@src/ApiContainer";
import LegacyCloseSessionController from "@src/controllers/CloseSessionController";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(
    config.FLAG_PER_PROGRAM_PROFILE ? CloseSessionController : LegacyCloseSessionController
  );
  await controller.handle(req, res);
});

export default router.handler({ onError });
