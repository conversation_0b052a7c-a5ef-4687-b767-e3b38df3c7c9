# Naming conventions

## Types

- Prefer `type` over `class`
- Use `class` only when methods are needed
- Use `class` if you need to transform your data, from backend type to frontend type and vice-versa

### From form to Java service

Types for values passed from forms to client-side services should be suffixed with `FormValues`

Supposing we have a `JoinOpportunityForm` component, and its submit handler class the `OpportunitiesService::joinOpportunity` method.

The type for the request to be sent to the Next.js API handler would be: `JoinOpportunityFormValues`, as shown in the snippet below.

```ts
const joinOpportunity = async (opportunityId: string, participation: JoinOpportunityFormValues): Promise<void> => {
  await axios.post(`/api/opportunity/${opportunityId}/participations`, participation);
  return Promise.resolve();
};
```

In most cases `JoinOpportunityFormValues` won't be the same as the request required by the Java code.
In such cases you should create a `JoinOpportunityRequest` type.

```ts
class JoinOpportunityRequest {
  constructor(values: JoinOpportunityFormValues) {
    // transform here from form values to HTTP request body for Java service
  }
}
```

## From Java service to React component

Responses coming from Java should have the suffix `Response`.
For example `AccountInformationResponse`.

```ts
export type AccountInformationResponse = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName: string;
  lastName: string;
  originEmail: string;
  dateOfBirth: number;
  needsMigration: boolean;
  status: string;
  registrationDate: number;
};
```

If the type requires no changes to be passed to the client, we use the type directly.

As shown in the previous section, if the types are different, we create a class and do the transformation in the constructor

```ts
export default class AccountInformationPayload {
  constructor(accountInformation: AccountInformationResponse) {
    // do the transformation here...
  }
}
```

As shown in the snippet above, we use the suffix `Payload` for this kind of classes.

In cases when the object returned contains dates, we create a model for client code (components).

```ts
class AccountInformation {
  readonly registrationDate: LocalizedDate;

  constructor(accountInformation: AccountInformationResponse) {
    this.registrationDate = new LocalizedDate(accountInformation.registrationDate);
  }
}
```

We also create such a model if we need methods to encapsulate logic from components.
For example `account.isExpired()`, `opportunity.isClosed()`, `creator.isFlagged()`, etc.
