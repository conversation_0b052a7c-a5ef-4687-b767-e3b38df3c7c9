import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsBreadCrumb from "../config/translations/breadcrumb";
import labelsCreatorType from "../config/translations/creator-type";
import MigrationLayout from "../components/MigrationLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Form from "../components/Form";
import Footer from "../components/migrations/Footer";
import CreatorTypeInputs from "../components/migrations/CreatorTypeInputs";
import { useRouter } from "next/router";
import CreatorsService from "../src/api/services/CreatorsService";
import Error from "./_error";
import { useAppContext } from "../src/context";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  onToastClose,
  SESSION_USER,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import withUnregisteredUser from "../src/utils/WithUnregisteredUser";
import Loading from "../components/Loading";
import { Toast, useToast } from "../components/toast";
import RedirectException from "../src/utils/RedirectException";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import MigrationModal from "../components/migrations/MigrationModal";
import { useFormContext } from "react-hook-form";
import labelsOpportunities from "../config/translations/opportunities";
import CancelRegistrationModal from "../components/pages/interested-creators/CancelRegistrationModal";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import creatorTypeProps from "../src/serverprops/CreatorTypeProps";
import verifyIncompleteRegistration from "../src/serverprops/middleware/VerifyIncompleteRegistration";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const CreatorTypeForm = ({
  t,
  creatorsType,
  creator,
  layout,
  onClose,
  isPending,
  setShowMigration,
  showMigration,
  submitHandle,
  stableDispatch,
  router,
  navigateToPage
}) => {
  const { getValues, formState } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const {
    state: { userNavigated },
    dispatch
  } = useAppContext();

  const onSave = () => submitHandle(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    navigateToPage ? router.push(navigateToPage) : router.push("/franchises-you-play");
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      navigateToPage ? router.push(navigateToPage) : router.push("/franchises-you-play");
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  return (
    <>
      <CreatorTypeInputs {...{ t, creatorsType, values: creator.creatorTypes || [] }} />
      <Footer
        {...{
          buttons: layout.buttons,
          onCancel: onClose,
          disableSubmit: isPending,
          isPending
        }}
      />
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard }} />
    </>
  );
};

export default function CreatorType({ user }) {
  const {
    analytics,
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(
    () => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE),
    [creatorsClient, DEFAULT_AVATAR_IMAGE]
  );
  const router = useRouter();
  const {
    dispatch,
    state: { exceptionCode = null, sessionUser = null, isValidationError, isError, onboardingSteps } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { t } = useTranslation(["common", "breadcrumb", "creator-type", "opportunities"]);
  const { error: errorToast } = useToast();
  const { layout, labels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      labels: labelsCreatorType(t)
    };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = labels;
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [creator, setCreator] = useState(null);
  const [creatorsType, setCreatorsType] = useState(null);
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");

  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  const submitHandle = useCallback(
    async (data, navigateBack, navigateToPage) => {
      stableDispatch({ type: USER_NAVIGATED, data: false });
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      // Creators BFF PUT
      try {
        const values = data.creatorTypes.map((item) => item.value);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({ creatorTypes: values, program: { code: PROGRAM_CODE } })
          : await CreatorsService.update(data);
        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        creator.creatorTypes = data.creatorTypes.map((creatorType) => creatorType.value);
        analytics.confirmedCreatorType({ locale: router.locale, creator });
        navigateToPage
          ? router.push(navigateToPage)
          : navigateBack
          ? router.push("/franchises-you-play")
          : router.push("/connect-accounts");
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [creatorsType, stableDispatch, creator, router, onboardingSteps]
  );
  const { pending: isPending, execute: submitHandleClb } = useAsync(submitHandle, false);

  useEffect(() => {
    async function fetchData() {
      try {
        // Creators BFF GET
        const creator = FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.getCreator(PROGRAM_CODE)
          : (await CreatorsService.getCreatorWithPayableStatus()).data;
        setCreator(creator);
        const creatorType = await metadataService.getCreatorTypes();
        creatorType && setCreatorsType(creatorType);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => user && stableDispatch({ type: SESSION_USER, data: user }), [user, stableDispatch]);
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <MigrationLayout
      pageTitle={labels.title}
      {...{
        ...layout,
        onClose,
        isRegistrationFlow: true,
        isOnboardingFlow: true,
        stableDispatch,
        onGoBack,
        setShowMigration,
        setNavigateToPage,
        completed: layout.completed
      }}
    >
      <div className="creator-types-container">
        <div className="mg-intro">
          <h3 className="mg-intro-title">{labels.title}</h3>
          <div className="mg-intro-description">{labels.infoTitle}</div>
        </div>
        {creatorsType && creator && (
          <Form mode="onChange" key="creatorType" onSubmit={submitHandleClb}>
            <CreatorTypeForm
              {...{
                t,
                creatorsType,
                creator,
                layout,
                onClose,
                isPending,
                setShowMigration,
                showMigration,
                submitHandle,
                stableDispatch,
                router,
                navigateToPage
              }}
            />
          </Form>
        )}
      </div>
      {(!creator || !creatorsType) && (
        <div className="loader">
          <Loading />
        </div>
      )}
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyIncompleteRegistration)
      .use(addLocaleCookie(locale))
      .get(creatorTypeProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      ...(await serverSideTranslations(locale, ["common", "breadcrumb", "creator-type", "opportunities"]))
    }
  };
};
