import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ApplicationCompletePageLabels = {
  applicationCompletePageLabels: {
    title: string;
    backHome: string;
    description: string;
    submissionCompleteDescription: string;
    submissionDate: string;
    email: string;
    subTitle: string;
    unReviewed: string;
    submissionCompleteSubTitle: string;
    gamerTag: string;
    status: string;
    programLabel: string;
    programName: string;
  };
};

export class ApplicationCompletePageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ApplicationCompletePageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      applicationCompletePageLabels: {
        title: microCopy.get("applicationComplete.title"),
        backHome: microCopy.get("applicationComplete.backHome"),
        description: microCopy.get("applicationComplete.description"),
        submissionCompleteDescription: microCopy.get("applicationComplete.submissionCompleteDescription"),
        submissionDate: microCopy.get("applicationComplete.submissionDate"),
        email: microCopy.get("applicationComplete.email"),
        subTitle: microCopy.get("applicationComplete.subTitle"),
        unReviewed: microCopy.get("applicationComplete.unReviewed"),
        submissionCompleteSubTitle: microCopy.get("applicationComplete.submissionCompleteSubTitle"),
        gamerTag: microCopy.get("applicationComplete.gamerTag"),
        status: microCopy.get("applicationComplete.status"),
        programLabel: microCopy.get("applicationComplete.programLabel"),
        programName: microCopy.get("applicationComplete.programName")
      }
    };
  }
}
