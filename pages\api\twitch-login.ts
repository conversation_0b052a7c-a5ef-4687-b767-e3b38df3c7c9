import "reflect-metadata";
import { NextApiRequest, NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import TwitchClient from "../../src/channels/twitch/TwitchClient";
import { createRouter } from "next-connect";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../config";
import onOAuthError from "@src/api/OAuthErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequest, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get((_req: NextApiRequest, res: NextApiResponse) => {
    const client = ApiContainer.get(TwitchClient);

    res.setHeader("Location", client.authorizationUrl());
    res.status(302);
    res.end();
  });

export default router.handler({ onError: onOAuthError });
