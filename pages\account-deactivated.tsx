import "reflect-metadata";
import { useTranslation } from "next-i18next";
import { memo, useEffect, useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsAccountDeactivated from "../config/translations/account-deactivated";
import labelsCommon from "../config/translations/common";
import Head from "next/head";
import featureFlags from "../utils/feature-flags";
import withDeactivatedAccount from "../src/utils/WithDeactivatedAccount";
import { AccountDeactivatedPage } from "@components/pages/AccountDeactivatedPage";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../src/utils/WithLocalizedUrl";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import accountDeactivatedProps from "@src/serverprops/AccountDeactivatedProps";

export type AccountDeactivatedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  showInitialMessage?: boolean;
  deactivatedAccount: {
    defaultGamerTag: string;
  };
};

export default memo(function AccountDeactivated({ deactivatedAccount }: AccountDeactivatedProps) {
  const { t } = useTranslation(["common", "account-deactivated"]);
  const { accountDeactivatedLabels } = useMemo(() => {
    const {
      header: { creatorNetwork }
    } = labelsCommon(t);
    return {
      accountDeactivatedLabels: {
        creatorNetwork,
        ...labelsAccountDeactivated(t),
        ...labelsCommon(t)
      }
    };
  }, [t]);

  useEffect(() => {
    document.title = accountDeactivatedLabels.pageTitle;
  }, []);

  return (
    <>
      <Head>
        <title>{accountDeactivatedLabels.pageTitle || accountDeactivatedLabels.creatorNetwork}</title>
      </Head>
      <AccountDeactivatedPage
        {...{
          ...accountDeactivatedLabels,
          username: deactivatedAccount.defaultGamerTag,
          t,
          image: "../img/signup/characters-signup.png"
        }}
      />
    </>
  );
});

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(accountDeactivatedProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<AccountDeactivatedProps>;
  }

  const deactivatedAccount = await withDeactivatedAccount(req, res);
  if (!featureFlags.isInterestedCreatorFlowEnabled() || !deactivatedAccount) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      deactivatedAccount,
      ...(await serverSideTranslations(locale, ["common", "account-deactivated"])),
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
