import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import InterestedCreatorInformationForm, { FormLabels } from "../../forms/InterestedCreatorInformationForm";
import {
  DOMAIN_ERROR,
  ERROR,
  GET_FB_PAGES,
  getExtractedErrorMessage,
  LOADING,
  onToastClose,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  toastContent,
  VALIDATION_ERROR
} from "../../../utils";
import { Toast, useToast } from "../../toast";
import { Fbpages, Information, Rules } from "../../../pages/interested-creators/information";
import { NextRouter } from "next/router";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import ConnectedAccountsService from "../../../src/api/services/ConnectedAccountsService";
import { useAppContext } from "@src/context";
import { Layout } from "../content-submission/ContentDeliverablesTab";
import CancelRegistrationModal from "./CancelRegistrationModal";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";

export const YOUTUBE_NO_CHANNEL_ERROR = "save-you-tube-account-unknown-connected-account";
export const INSTAGRAM_STEPS_LINK =
  "https://business.instagram.com/getting-started?fbclid=IwAR2RCQ_lweAva29YXvt7Nfa2wDshHe9oT5LjbHXbGpSKzhIs56G5gfQlrUk";
export const INSTA_WARNING_ERROR = "save-instagram-account-unknown-instagram-business-account";
export const INSTA_CANNOT_CONNECT = "save-instagram-account-cannot-connect-account";

export type Country = {
  value: string;
  label: string;
  name: string;
};
export type Language = {
  value: string;
  label: string;
  name?: string;
};
export type Locale = {
  value: string;
  label: string;
  id?: string;
};
export type PreferredLanguage = {
  code: string;
  name: string;
};
export type Countries = Array<Country>;
export type Languages = Array<Language>;
export type Locales = Array<Locale>;
export type CloseHandler = {
  onClose?: MouseEventHandler<HTMLButtonElement>;
};

export type PageLabels = {
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  interestedCreatorTitle: string;
  yes: string;
  no: string;
  interestedUserDescription1: string;
  interestedUserDescription2: string;
};

export type ConnectAccountLabels = {
  title: string;
  message: string;
  subTitle: string;
  myAccount: string;
  addAccount: string;
  description: string;
  accounts: {
    youTube: string;
    facebook: string;
    twitch: string;
    instagram: string;
    tiktok: string;
  };
  modalTitle: string;
  modalMessage: string;
  modalConfirmationTitle: string;
  modalConfirmationTitleFB: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  messages: {
    removeAccountTitle: string;
    removeAccountDescription: string;
    cannotConnectInstaAccount: string;
    cannotConnectInstaAccountHeader: string;
    actionTitle: string;
    actionDescription1: string;
    actionDescription2: string;
    actionDescription3: string;
    actionDescription4: string;
    youtubeNoChannelError: string;
  };
  modal: {
    removeAccountTitle: string;
    removeAccountDescription1: string;
    removeAccountDescription2: string;
  };
  buttons: {
    cancel: string;
    remove: string;
  };
  subscribers: string;
  removeAccount: string;
  expireAccount: string;
  or: string;
  reconnectAccount: string;
  connectNewAccount: string;
  connectNewAccountDescription: string;
  connectNewAccountDescriptionWithTikTok: string;
  myProfile: string;
  information: string;
  gamePreferences: string;
  creatorType: string;
  connectedAccounts: string;
  communicationSettings: string;
  legalDocuments: string;
  paymentInformation: string;
  pointOfContact: string;
  discord: string;
  email: string;
  verificationPending: string;
  reVerifyAccount: string;
};

export type InterestedCreatorsInformationPageProps = {
  formLabels: FormLabels;
  pageLabels: PageLabels;
  showConfirmation?: boolean;
  children?: ReactNode;
  interestedCreator?: InterestedCreator & Information;
  rules: Rules;
  stableDispatch: (action) => void;
  setShowConfirmation: (a: boolean) => void;
  router: NextRouter;
  locale: string;
  analytics: BrowserAnalytics;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  layout: Layout;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  accessToken: string;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  FLAG_COUNTRIES_BY_TYPE: boolean;
} & CloseHandler &
  Fbpages;

export type ErrorType = {
  code: string;
  message: string;
};

const REMOVE_ONLY_ACCOUNT = "disconnect-account-conflicting-action";
const REMOVE_ONLY_ACCOUNT_INVALID = "disconnect-account-invalid-input";

export default memo(function InterestedCreatorsInformationPage({
  formLabels,
  pageLabels,
  onClose,
  showConfirmation = false,
  interestedCreator,
  rules,
  stableDispatch,
  setShowConfirmation,
  router,
  locale,
  analytics,
  showAddConfirmation,
  setShowAddConfirmation,
  layout,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  pages,
  accessToken,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  FLAG_COUNTRIES_BY_TYPE
}: // error
InterestedCreatorsInformationPageProps) {
  const { metadataClient, errorHandler } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const {
    confirmationDesc1,
    confirmationDesc2,
    modalConfirmationTitle,
    interestedCreatorTitle,
    interestedUserDescription1,
    interestedUserDescription2
  } = pageLabels;
  const {
    state: {
      isError = false,
      isValidationError = false,
      reloadInterestedCreatorAccounts = false,
      getFbPages = false,
      domainError = false
    }
  } = useAppContext();
  const { selectCountry } = formLabels;
  const [countries, setCountries] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [locales, setLocales] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [facebookPages, setFacebookPages] = useState(pages);
  const { error: errorToast } = useToast();
  const [error, setError] = useState<ErrorType>();
  const {
    main: { unhandledError }
  } = layout;

  const {
    actionTitle,
    actionDescription1,
    actionDescription2,
    actionDescription3,
    actionDescription4,
    cannotConnectInstaAccount,
    cannotConnectInstaAccountHeader,
    removeAccountDescription,
    youtubeNoChannelError
  } = connectAccountLabels.messages;

  if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
    connectAccountLabels.expireAccount = connectAccountLabels.verificationPending;
    connectAccountLabels.reconnectAccount = connectAccountLabels.reVerifyAccount;
  }

  const instaWarningContent = useMemo(() => {
    return (
      <div className="connect-account-insta-warning">
        <p>{actionDescription1}</p>
        <br />
        <p key="actionDescription2">
          {actionDescription2}{" "}
          <a className="connect-account-insta-steps" target="_blank" rel="noreferrer" href={INSTAGRAM_STEPS_LINK}>
            {actionDescription3}
          </a>
          {actionDescription4}
        </p>
      </div>
    );
  }, [actionDescription1, actionDescription2, actionDescription3, actionDescription4]);

  const { warning } = useToast();

  const clearError = useCallback(async () => {
    try {
      // Unset error from session.
      stableDispatch({ type: LOADING, data: true });
      await ConnectedAccountsService.deleteConnectedAccountErrors();
      stableDispatch({ type: LOADING, data: false });
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, e);
    }
  }, [stableDispatch]);

  const errorMap = useMemo(
    () =>
      new Map([
        [REMOVE_ONLY_ACCOUNT, removeAccountDescription],
        [REMOVE_ONLY_ACCOUNT_INVALID, removeAccountDescription],
        [INSTA_CANNOT_CONNECT, cannotConnectInstaAccount],
        [YOUTUBE_NO_CHANNEL_ERROR, youtubeNoChannelError]
      ]),
    [removeAccountDescription, cannotConnectInstaAccount, youtubeNoChannelError]
  );

  useEffect(() => {
    if (error?.code === INSTA_WARNING_ERROR) {
      warning(
        <Toast header={actionTitle} content={instaWarningContent} closeButtonAriaLabel={layout.buttons.close} />,
        {
          onClose: () => clearError()
        }
      );
    } else if (error && errorMap) {
      const header = error?.code === INSTA_CANNOT_CONNECT ? cannotConnectInstaAccountHeader : unhandledError;
      const errorMessage = getExtractedErrorMessage(errorMap, error, unhandledError);
      errorToast(<Toast header={header} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />, {
        onClose: () => clearError()
      });
    }
  }, [error, actionTitle, instaWarningContent, warning, stableDispatch, errorToast, unhandledError, errorMap, cannotConnectInstaAccountHeader]);

  const fetchFacebookPages = async () => {
    try {
      const response = await ConnectedAccountsService.getFacebookPages();
      setFacebookPages(response.pages);
    } catch {
      // pages were removed from the session when creator cancel from the pages modal
      setFacebookPages([]);
    }
  };

  const fetchConnectedAccounts = async () => {
    try {
      const connectedAccounts = INTERESTED_CREATOR_REAPPLY_PERIOD
        ? await ConnectedAccountsService.getAllConnectedAccountsWithExpirationStatus(interestedCreator.nucleusId)
        : await ConnectedAccountsService.getConnectedAccounts(interestedCreator.nucleusId);
      setAccounts(connectedAccounts);
      if (getFbPages) await fetchFacebookPages();
      stableDispatch({ type: LOADING, data: false });
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, e);
    }
  };

  useEffect(() => {
    if (!accountToRemove) {
      stableDispatch({ type: LOADING, data: true });
      fetchConnectedAccounts();
    }
  }, [accountToRemove]);

  useEffect(() => {
    stableDispatch({ type: LOADING, data: true });
    if (reloadInterestedCreatorAccounts) {
      setShowAddConfirmation(false); // re-setting showAddConfirmation value as we are not reloads the page.
      fetchConnectedAccounts();
      fetchInstaErrors();
    }
    stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: false });
    stableDispatch({ type: GET_FB_PAGES, data: false });
  }, [reloadInterestedCreatorAccounts]);

  const fetchInstaErrors = async () => {
    const error = await ConnectedAccountsService.getConnectAccountErrors();
    setError(error);
    stableDispatch({ type: LOADING, data: false });
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const countries = FLAG_COUNTRIES_BY_TYPE
          ? await metadataService.getCountriesMatching()
          : await metadataService.getCountries();
        if (countries) setCountries([{ value: "", label: selectCountry }, ...countries]);
        const languages = await metadataService.getLanguages();
        if (languages) setLanguages(languages);
        const locales = await metadataService.getLocales();
        if (locales) setLocales(locales);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    fetchData();
  }, [selectCountry, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={formLabels.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  useEffect(() => {
    if (domainError) {
      const errorMessage = getExtractedErrorMessage(errorMap, domainError, unhandledError);
      errorToast(<Toast header={unhandledError} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />, {
        onClose: () => onToastClose(DOMAIN_ERROR, stableDispatch)
      });
    }
  }, [domainError, unhandledError, stableDispatch, errorMap, errorToast, layout.buttons.close]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);

  const handleCancelRegistration = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [locale, router]);

  useEffect(() => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      if (!interestedCreator.country && interestedCreator.countryCode) {
        interestedCreator.country = countries.find(({ value }) => value === interestedCreator.countryCode);
      }
      if (!interestedCreator.contentUrls && interestedCreator.contentAccounts?.length > 0) {
        interestedCreator.contentUrls = interestedCreator.contentAccounts;
      }
    }
  }, [countries, interestedCreator]);

  const modalLabels = {
    title: modalConfirmationTitle,
    yes: pageLabels.yes,
    no: pageLabels.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <>
      <div className="mg-intro">
        <h3 className="mg-intro-title">{interestedCreatorTitle}</h3>
      </div>
      <div className="intrested-creator-information-description">
        <p className="intrested-creator-information-sub-description">
          {interestedUserDescription1} <strong className="gamer-tag">{interestedCreator.defaultGamerTag}!</strong>{" "}
          {interestedUserDescription2}
        </p>
      </div>
      <InterestedCreatorInformationForm
        {...{
          formLabels,
          onClose,
          languages,
          locales,
          countries,
          interestedCreator,
          rules,
          router,
          locale,
          stableDispatch,
          analytics,
          showAddConfirmation,
          setShowAddConfirmation,
          layout,
          connectAccountLabels,
          accountToRemove,
          setAccountToRemove,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          accounts,
          pages: facebookPages,
          accessToken,
          INTERESTED_CREATOR_REAPPLY_PERIOD
        }}
      />
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: modalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </>
  );
});
