---
currentMenu: howto
---

# Implement page components

A page component should only be doing the following:

- Initial API calls to populate forms or other components
- API calls to update information
- Provide feedback when an API call fails

For instance

<pre data-line="6-14,16-26"><code class="language-tsx line-numbers">
// imports...

export default CommunicationPreferencesPage = ({ translations, buttons, labels, layout }) {
  // state...

  useEffect(() => {
    MetadataService.getLanguages()
      .then((res) => setLanguages(res.data))
      .catch((e) => errorHandling(stableDispatch, e));
    MetadataService.getLocales()
      .then((res) => setLocales(res.data))
      .catch((e) => errorHandling(stableDispatch, e));
    // More API calls
  }, []);

  const updateCreator = useCallback(
    async (data) => {
      const formData = { ...communicationPreferences, ...data };
      return CreatorsService.update({ communicationPreferences: formData })
        .then((res) => {
            // on sucessful update logic
        })
        .catch((e) => errorHandling(stableDispatch, e));
    },
    [communicationPreferences, stableDispatch]
  );

  return &lt;CommunicationPreferencesForm languages={languages} locales={locales} onSubmit={updateCreator} /&gt;
</code></pre>

This will make it easier to test the page, since:

- You can focus your testing on making sure the API calls worked

```js
it("updates preferred email address");
it("displays toast error message if email address couldn't be saved");
```

## Examples

- [components/pages/InterestedCreatorsInformationPage](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/components/pages/InterestedCreatorsInformationPage.tsx)
