import React, { useCallback, useEffect, useMemo, useState } from "react";
import FranchiseYouPlayForm from "./forms/FranchiseYouPlayForm";
import PlatformPreferencesForm from "./forms/PlatformPreferencesForm";
import Form from "../Form";
import CreatorsService from "../../src/api/services/CreatorsService";
import { Toast, useToast } from "../toast";
import { useAppContext } from "../../src/context";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import { useDependency } from "../../src/context/DependencyContext";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const GamePreferences = ({
  franchisesYouPlayLabels,
  infoLabels,
  buttons,
  creator,
  updateCreator,
  franchises,
  platforms,
  layout,
  analytics
}) => {
  const {
    errorHandler,
    configuration: { FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, DEFAULT_AVATAR_IMAGE },
    creatorsClient
  } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const { dispatch, state: { isValidationError, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const [isFranchiseSaved, setIsFranchiseSaved] = useState(false);
  const [isPlatformSaved, setIsPlatformSaved] = useState(false);
  const [preferredPrimaryFranchises, setPreferredPrimaryFranchises] = useState(null);
  const [preferredPrimaryPlatforms, setPreferredPrimaryPlatforms] = useState(null);
  const [preferredSecondaryFranchises, setPreferredSecondaryFranchises] = useState(null);
  const [preferredSecondaryPlatforms, setPreferredSecondaryPlatforms] = useState(null);
  const router = useRouter();
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  useEffect(() => {
    if (creator && franchises) {
      let preferredPrimaryFranchises = FLAG_PER_PROGRAM_PROFILE
        ? creator.preferredPrimaryFranchise
        : creator.preferredPrimaryFranchises;
      franchises.forEach((item) => {
        if (preferredPrimaryFranchises && item.value === preferredPrimaryFranchises.value)
          preferredPrimaryFranchises.image = item.image;
      });
      setPreferredPrimaryFranchises(preferredPrimaryFranchises);
      let preferredSecondaryFranchises = creator.preferredSecondaryFranchises;
      preferredSecondaryFranchises.forEach((item) => {
        franchises.forEach((obj) => {
          if (item.value === obj.value) item.image = obj.image;
        });
      });
      setPreferredSecondaryFranchises(preferredSecondaryFranchises);
    }
    if (creator && platforms) {
      let preferredPrimaryPlatforms = FLAG_PER_PROGRAM_PROFILE
        ? creator.preferredPrimaryPlatform
        : creator.preferredPrimaryPlatforms;
      platforms.forEach((item) => {
        if (preferredPrimaryPlatforms && item.value === preferredPrimaryPlatforms.value)
          preferredPrimaryPlatforms.imageAsIcon = item.imageAsIcon;
      });
      setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
      setPreferredSecondaryPlatforms(creator.preferredSecondaryPlatforms);
    }
  }, [creator, franchises, platforms]);

  const submitFranchise = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const primaryFranchiseValue = { id: data.primaryFranchise.value, type: "PRIMARY" };
        const values = data.secondaryFranchise.map((secondaryFranchise) => ({
          id: secondaryFranchise.value,
          type: "SECONDARY"
        }));
        values.push(primaryFranchiseValue);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              preferredFranchises: values,
              program: { code: PROGRAM_CODE }
            })
          : await CreatorsService.update({ franchisesYouPlay: data });
        let preferredPrimaryFranchise = data.primaryFranchise;
        franchises?.forEach((franchise) => {
          if (franchise.value === preferredPrimaryFranchise.value) {
            preferredPrimaryFranchise.value = franchise.value;
            preferredPrimaryFranchise.image = franchise.image;
            preferredPrimaryFranchise.label = franchise.label;
            preferredPrimaryFranchise.value = franchise.value;
          }
        });
        setPreferredPrimaryFranchises(preferredPrimaryFranchise);
        let preferredSecondaryFranchises = data.secondaryFranchise;
        preferredSecondaryFranchises.forEach((item) => {
          franchises?.forEach((franchise) => {
            if (item.id === franchise.value) {
              item.value = franchise.value;
              item.image = franchise.image;
              item.label = franchise.label;
            }
          });
        });
        setPreferredSecondaryFranchises(preferredSecondaryFranchises);
        if (creator.updatedPrimaryFranchise(preferredPrimaryFranchise.label)) {
          creator.preferredPrimaryFranchises = preferredPrimaryFranchise;
          analytics.updatedPrimaryFranchise({ locale: router.locale, creator });
        }
        if (creator.updatedSecondaryFranchises(preferredSecondaryFranchises.map((franchise) => franchise.label))) {
          creator.preferredSecondaryFranchises = preferredSecondaryFranchises;
          analytics.updatedSecondaryFranchises({ locale: router.locale, creator });
        }
        updateCreator(creator);
        setIsFranchiseSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, franchises]
  );

  const submitPlatform = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const primaryPlatformValue = { id: data.primaryPlatform.value, type: "PRIMARY" };
        const values = data.secondaryPlatforms.map((secondaryPlatform) => {
          return { id: secondaryPlatform.value, type: "SECONDARY" };
        });
        values.push(primaryPlatformValue);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              preferredPlatforms: values,
              program: { code: PROGRAM_CODE }
            })
          : await CreatorsService.update({ platformPreferences: data });

        let preferredPrimaryPlatforms = data.primaryPlatform;
        platforms.forEach((item) => {
          if (item.value === preferredPrimaryPlatforms.value) {
            preferredPrimaryPlatforms.label = item.label;
            preferredPrimaryPlatforms.value = item.value;
            preferredPrimaryPlatforms.imageAsIcon = item.imageAsIcon;
          }
        });
        setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
        let preferredSecondaryPlatformsResponse = data.secondaryPlatforms;
        preferredSecondaryPlatformsResponse.forEach((item) => {
          platforms.forEach((obj) => {
            if (item.value === obj.value) {
              item.imageAsIcon = obj.imageAsIcon;
              item.label = obj.label;
              item.value = obj.value;
            }
          });
        });
        setPreferredSecondaryPlatforms(preferredSecondaryPlatformsResponse);
        if (creator.updatedPrimaryPlatform(preferredPrimaryPlatforms.label)) {
          creator.preferredPrimaryPlatforms = preferredPrimaryPlatforms;
          analytics.updatedPrimaryPlatformInProfile({ locale: router.locale, creator });
        }
        if (creator.updatedSecondaryPlatforms(preferredSecondaryPlatformsResponse.map((platform) => platform.label))) {
          analytics.updatedSecondaryPlatformsInProfile({
            locale: router.locale,
            creator,
            selectedPlatforms: preferredSecondaryPlatformsResponse
          });
          creator.preferredSecondaryPlatforms = preferredSecondaryPlatformsResponse;
        }
        updateCreator(creator);
        setIsPlatformSaved(!isPlatformSaved);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, platforms]
  );

  const franchiseOnChange = useCallback(() => setIsFranchiseSaved(false), []);
  const platformOnChange = useCallback(() => setIsPlatformSaved(false), []);
  const { pending: pendingFranchiseUpd, execute: onSubmitFranchise } = useAsync(submitFranchise, false);
  const { pending: pendingPlatformUpd, execute: onSubmitPlatform } = useAsync(submitPlatform, false);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  return (
    <div className="profile-game-preferences">
      {preferredSecondaryFranchises && (
        <Form key="franchise" mode="onChange" onSubmit={onSubmitFranchise}>
          <FranchiseYouPlayForm
            {...{
              franchisesYouPlayLabels,
              franchises,
              preferredPrimaryFranchises,
              preferredSecondaryFranchises,
              buttons,
              onChange: franchiseOnChange,
              isSaved: isFranchiseSaved,
              isLoader: pendingFranchiseUpd
            }}
          />
        </Form>
      )}
      {preferredSecondaryPlatforms && (
        <Form key="platform" mode="onChange" onSubmit={onSubmitPlatform}>
          <PlatformPreferencesForm
            {...{
              infoLabels,
              platforms,
              preferredPrimaryPlatforms,
              preferredSecondaryPlatforms,
              buttons,
              onChange: platformOnChange,
              isSaved: isPlatformSaved,
              isLoader: pendingPlatformUpd
            }}
          />
        </Form>
      )}
    </div>
  );
};
export default GamePreferences;
