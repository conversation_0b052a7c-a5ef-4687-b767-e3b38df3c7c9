import "reflect-metadata";
import { render, screen, waitFor, within } from "@testing-library/react";
import InterestedCreatorsInformationForm, {
  InterestedCreatorInformationFormProps
} from "@components/forms/InterestedCreatorInformationForm";
import userEvent from "@testing-library/user-event";
import {
  commonTranslations,
  communicationPreferencesTranslations,
  connectAccountTranslations,
  informationTranslations,
  labels,
  profileLabels
} from "../../translations";
import CreatorForm from "@components/FormRules/CreatorForm";
import { Rules } from "../../../pages/interested-creators/information";
import {
  clearDateFor,
  clearValueFor,
  clearValueForUrl,
  enterDateFor,
  enterValueFor,
  selectMultipleOptions,
  selectOption
} from "../../helpers/forms";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { NextRouter } from "next/router";
import { getComputedStyle } from "../../helpers/window";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import { WINDOW_PARAMS } from "../../../utils";
import { aConnectedAccount } from "../../factories/creators/ConnectedAccounts";
import "next/config";
import Random from "../../factories/Random";
import { aLocalizedDate } from "__tests__/factories/LocalizedDateBuilder";
import InterestedCreatorsServices from "@src/api/services/InterestedCreatorsServices";
import SubmittedContentService from "@src/api/services/SubmittedContentService";
import ConnectedAccountsService from "@src/api/services/ConnectedAccountsService";
import { aCountry, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("next/config");
jest.mock("../../../src/api/services/InterestedCreatorsServices");
jest.mock("../../../src/api/services/SubmittedContentService");
jest.mock("../../../src/api/services/ConnectedAccountsService");
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorInformationForm", () => {
  const { formLabels } = labels;
  const onClose = jest.fn();
  const locale = "en-us";
  const allRules = {
    ...CreatorForm.rules(informationTranslations),
    ...CreatorForm.communicationRules(communicationPreferencesTranslations)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url, followers } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url, followers };
  const stableDispatch = jest.fn();
  const analytics = {} as unknown as BrowserAnalytics;
  const router = { locale, push: jest.fn() } as unknown as NextRouter;
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const creatorFormValues = {
    firstName: "Jane",
    lastName: "Doe",
    country: aCountry({ label: "Canada", value: "CA" }),
    contentUrls: [{ url: "https://www.google.com", followers: "" }],
    contentLanguages: [
      {
        value: "en",
        label: "English",
        id: "a0LK0000008epjzMAA"
      }
    ]
  };
  const interestedCreator = {
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    dateOfBirth: dateOfBirthAfter18years,
    contentUrls: [
      {
        url: "https://www.google.com",
        followers: "20"
      }
    ],
    preferredLanguage: {
      code: "en_US",
      name: "English"
    }
  };
  const interestedCreatorInformationProps: InterestedCreatorInformationFormProps = {
    formLabels,
    rules,
    locale,
    router,
    stableDispatch,
    onClose,
    analytics,
    countries: [aCountry({ label: "Canada", value: "CA" }), aCountry({ label: "Mexico", value: "MX" })],
    locales,
    languages: [aLanguage({ value: "en", label: "English" })],
    interestedCreator,
    showAddConfirmation: false,
    setShowAddConfirmation: jest.fn(),
    layout: commonTranslations,
    connectAccountLabels: { ...connectAccountTranslations, ...profileLabels },
    accountToRemove: null,
    setAccountToRemove: jest.fn(),
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    accounts: [],
    pages: [],
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };
  const interestedCreatorInformationPropsWithFlagEnabled = {
    ...interestedCreatorInformationProps,
    rules
  };
  const scanResult = {
    url: interestedCreator.contentUrls[0].url,
    isSecure: true
  };
  const urlScanResult = { results: [scanResult] };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  let windowSpy;
  const addAccountsWithTiktok = [
    ["YOUTUBE", "/api/youtube-login"],
    ["FACEBOOK", "/api/facebook-login"],
    ["INSTAGRAM", "/api/instagram-login"],
    ["TWITCH", "/api/twitch-login"],
    ["TIKTOK", "/api/tiktok-login"]
  ];
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    windowSpy = jest.spyOn(window, "getComputedStyle");
    windowSpy.mockImplementation(getComputedStyle);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps,
        showFacebookPages: true
      }
    });
    (useDependency as jest.Mock).mockReturnValue({ errorHandler, configuration: { FLAG_PER_PROGRAM_PROFILE: false } });
  });

  it("enables next button on populating required values", async () => {
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }]
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: `"${dateOfBirthAfter18years}"`,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      submittedUrl: "https://www.google.com"
    };
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();

    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await clearDateFor(/Date of Birth/i);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    await enterValueFor(/^Website URL/i, input.submittedUrl);

    await waitFor(() => expect(nextButton).toBeEnabled());
  }, 10000);

  it("disables next button on populating date of birth under 18 years", async () => {
    const dateOfBirthBefore18Years = aLocalizedDate().minusYears(18).plusDays(1).build().formatWithEpoch("MM/DD/YYYY");
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }]
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: dateOfBirthAfter18years,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      submittedUrl: "https://www.google.com"
    };
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();
    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await clearDateFor(/Date of Birth/i);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    await enterValueFor(/^Website URL/i, input.submittedUrl);
    await waitFor(() => expect(nextButton).toBeEnabled());
    await clearDateFor(/Date of Birth/i);

    await enterDateFor(/Date of Birth/i, dateOfBirthBefore18Years);

    expect(await screen.findByText(labels.formLabels.ageMustBe18OrOlder)).toBeInTheDocument();
    await waitFor(() => expect(nextButton).toBeDisabled());
  }, 10000);

  it("is populated with interested creator information", async () => {
    const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY");
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years
    };

    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await waitFor(() => {
      expect(screen.getByLabelText(/^Website URL/i)).toHaveValue(interestedCreator.contentUrls[0].url);
      expect(screen.getByLabelText(/^First Name/i)).toHaveValue(interestedCreator.firstName);
      expect(screen.getByLabelText(/^Last Name/i)).toHaveValue(interestedCreator.lastName);
      expect(screen.getByLabelText(/^Preferred Email/i)).toHaveValue(interestedCreator.originEmail);
      expect(screen.getByPlaceholderText(/Date of Birth/i)).toHaveValue(interestedCreator.dateOfBirth);
      expect(screen.queryByText(interestedCreator.country.label)).toBeInTheDocument();
    });
  });

  it("shows error messages when mandatory fields are cleared ", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await clearValueFor(/^First Name/i);
    await clearValueFor(/^Last Name/i);
    await clearValueFor(/^Preferred Email/i);
    await clearValueFor(/Date of Birth/i);
    await clearValueForUrl(/^Website URL/i);

    await waitFor(() => {
      expect(screen.getByText(/First Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Last Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Preferred Email Address is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Date of Birth is required/i)).toBeInTheDocument();
    });
  });

  it("allows adding or removing more urls  ", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      contentUrls: [{ url: "", followers: "" }],
      preferredEmail: ""
    };
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(2));

    await userEvent.click(screen.getByLabelText("Remove"));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(1));
  });

  it("calls its submit handler and redirect to the next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanResult
    });
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    await selectOption({ option: "English", container, label: "Language" });
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        { urls: [interestedCreator.contentUrls[0].url] },
        "INTERESTED_CREATORS"
      );
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith({
        ...interestedCreator,
        country: { ...interestedCreator.country },
        preferredEmail: "<EMAIL>",
        preferredLanguage: {
          code: "en_US",
          name: "English"
        }
      });
    });
  });

  it("restrict to adding only 10 URLs", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await addAnotherUrl(2);
    await addAnotherUrl(3);
    await addAnotherUrl(4);
    await addAnotherUrl(5);
    await addAnotherUrl(6);
    await addAnotherUrl(7);
    await addAnotherUrl(8);
    await addAnotherUrl(9);
    await addAnotherUrl(10);

    await waitFor(() => expect(screen.queryByRole("button", { name: "Add another URL" })).not.toBeInTheDocument());
  }, 10000);

  it("shows error message if a duplicate URL is entered", async () => {
    const duplicateUrl = "https://www.google.com";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );
    // Enter valid URL
    await enterValueFor(/^Website URL/i, duplicateUrl);
    // Add one more URL
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByLabelText(/^Website URL/i)).toHaveLength(2));

    // provide duplicate URL
    await userEvent.type(screen.getAllByLabelText(/^Website URL/i)[1], duplicateUrl);

    await waitFor(() => {
      expect(screen.queryByText(/Duplicate URLs not allowed/i)).toBeInTheDocument();
    });
  });

  it("shows error message if we enter space between the URL", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "ea. com", followers: "" }]
    };
    const urlScanError = {
      status: 422,
      code: "validate-content-urls-invalid-input",
      errors: {
        "urls[0]": ["value must be a valid URL. Invalid domain -creatornetwork.ea.com"]
      }
    };
    (SubmittedContentService.validateContent as jest.Mock).mockRejectedValue({
      response: {
        ...urlScanError,
        data: urlScanError
      }
    });

    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        { urls: [interestedCreator.contentUrls[0].url] },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/URL provided is invalid/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
    });
  });

  it("shows error message if we enter empty space instead of a URL", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "   ", followers: "" }]
    };
    const urlScanError = {
      status: 422,
      code: "validate-content-urls-invalid-input",
      errors: {
        "urls[0]": ["value must be a valid URL. Invalid domain -creatornetwork.ea.com"]
      }
    };
    (SubmittedContentService.validateContent as jest.Mock).mockRejectedValue({
      response: {
        ...urlScanError,
        data: urlScanError
      }
    });

    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        { urls: [interestedCreator.contentUrls[0].url] },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/URL provided is invalid/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
    });
  });

  it("shows error message when API content scanning failed and doesn't redirect to next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    const urlScanError = { ...urlScanResult, results: [{ ...scanResult, isSecure: false }] };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanError
    });
    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("shows error message when API content scanning failed for one url and doesn't redirect to next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [
        { url: "https://ea.com", followers: "" },
        { url: "https://test.com", followers: "" }
      ]
    };
    const urlScanError = { ...urlScanResult, results: [scanResult, { url: "https://test.com", isSecure: false }] };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanError
    });
    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url, interestedCreator.contentUrls[1].url]
        },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("saves interested creator information", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years,
      preferredEmail: updatedPreferredEmail
    };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanResult
    });
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    await selectOption({ option: "English", container, label: "Language" });
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);

      // Verify content url call
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      // Verify update application call

      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith(interestedCreator);
      //Validate the preferredEmail is same as updatedPreferredEmail in the response
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
    });
  });

  it("defaults url field to 'https://' on click of clear button", async () => {
    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationProps} />);
    expect(await screen.findByLabelText(/^Website URL/i)).toHaveValue(
      interestedCreatorInformationProps.interestedCreator.contentUrls[0].url
    );

    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));

    expect(await screen.findByLabelText(/^Website URL/i)).toHaveValue("https://");
  });

  it("should not display duplicate url error message for two or more url fields with https:// value", async () => {
    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationProps} />);
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    const urlInputs = screen.getAllByLabelText(/^Website URL/i);
    await waitFor(() => expect(urlInputs).toHaveLength(2));

    await userEvent.clear(urlInputs[0]);
    await waitFor(() => expect(urlInputs[0]).toHaveValue("https://"));
    await userEvent.clear(urlInputs[1]);
    await waitFor(() => expect(urlInputs[1]).toHaveValue("https://"));

    await waitFor(() => expect(screen.queryByText(/Duplicate URLs not allowed/i)).not.toBeInTheDocument());
  });

  it("displays 'Language for Communication' section", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };

    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    expect(await screen.findByText("Language for Communication")).toBeInTheDocument();
    expect(await screen.findByLabelText("Language")).toBeInTheDocument();
  });

  it("handles API error while submit information form", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      preferredEmail: updatedPreferredEmail
    };
    (SubmittedContentService.validateContent as jest.Mock).mockRejectedValue({
      response: {
        ...urlScanResult,
        data: urlScanResult
      }
    });
    (InterestedCreatorsServices.saveApplication as jest.Mock).mockRejectedValue({
      data: {
        statusCode: 401,
        message: "Unauthorized"
      }
    });

    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    await selectOption({ option: "English", container, label: "Language" });
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      expect(errorHandler).toHaveBeenCalledTimes(1);
    });
  });

  it("displays 'Add accounts' section", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount()]
    });

    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} />);

    expect(await screen.findByRole("heading", { name: "Connect your Social Media Accounts" })).toBeInTheDocument();
    expect(
      await screen.findByText(
        "Please connect at least one of your social media accounts with your content. You may also connect multiple accounts on each social media. This will help our team assess our compatibility."
      )
    ).toBeInTheDocument();
    expect(await screen.findByRole("heading", { name: "Add Accounts" })).toBeInTheDocument();
    expect(await screen.findByTestId("add-account-button-YOUTUBE")).toBeInTheDocument();
    expect(await screen.findByTestId("add-account-button-INSTAGRAM")).toBeInTheDocument();
    expect(await screen.findByTestId("add-account-button-FACEBOOK")).toBeInTheDocument();
    expect(await screen.findByTestId("add-account-button-TWITCH")).toBeInTheDocument();
    expect(await screen.findByTestId("add-account-button-TIKTOK")).toBeInTheDocument();
  });

  it.each(addAccountsWithTiktok)("opens consent screen to connect a %s account", async (account, link) => {
    window.open = jest.fn().mockReturnValue({ closed: true });
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());

    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} />);

    await userEvent.click(await screen.findByTestId(`add-account-button-${account}`));

    await waitFor(() => {
      expect(window.open).toHaveBeenCalledWith(link, "_blank", WINDOW_PARAMS);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
    });
  });

  it("shows modal with Facebook pages if present", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount()]
    });
    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());

    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Hariea Test"
      }
    ];

    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} pages={facebookPages} />
    );

    // Check for modal headers
    expect(await screen.findByText("Please select a Facebook page")).toBeInTheDocument();
    //Check for fb pages radio button.
    expect(await screen.findByRole("radio", { name: "Hariea Test" })).toBeInTheDocument();
  });

  it("clears facebook pages in session on closing the modal", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount()]
    });
    window.open = jest.fn().mockReturnValue({ closed: true });
    (ConnectedAccountsService.clearFbPages as jest.Mock).mockImplementation(() => Promise.resolve());
    const facebookPages = [
      {
        accessToken: "asdhgkfdgh",
        id: "s1556",
        name: "Hariea Test"
      }
    ];
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} pages={facebookPages} />
    );

    await userEvent.click(await screen.findByRole("button", { name: /Close$/i }));

    await waitFor(() => {
      expect(ConnectedAccountsService.clearFbPages).toHaveBeenCalledTimes(1);
    });
  });

  it("clear the account type in session after selecting a Facebook page", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount()]
    });
    const selectedPage = [{ id: Random.uuid(), accessToken: Random.accessToken(), name: "hari70aTest" }];
    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} pages={selectedPage} />
    );
    const { getByRole } = within(await screen.findByRole("dialog"));
    // Select a Facebook page
    await userEvent.click(await screen.findByRole("radio"));

    await userEvent.click(getByRole("button", { name: "Connect" }));

    await waitFor(() => {
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
    });
  });

  it("disables cancel button in modal, when connecting a facebook page", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount()]
    });
    const selectedPage = [{ id: Random.uuid(), accessToken: Random.accessToken(), name: "hari70aTest" }];
    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} pages={selectedPage} />
    );
    const { getByRole } = within(await screen.findByRole("dialog"));
    expect(getByRole("button", { name: "Connect" })).toBeDisabled();
    // Select a Facebook page
    await userEvent.click(await screen.findByRole("radio"));
    expect(getByRole("button", { name: "Connect" })).toBeEnabled();

    await userEvent.click(getByRole("button", { name: "Connect" }));

    expect(getByRole("button", { name: "Cancel" })).toBeDisabled();
    expect(getByRole("button", { name: /Close$/ })).toBeDisabled();
    expect(getByRole("button", { name: "Connect" })).toBeDisabled();
    await waitFor(() => {
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
    });
  });

  it("disables next button when at least one account is not connected", async () => {
    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} accounts={[]} />);

    await waitFor(() => expect(screen.getByRole("button", { name: "Next" })).toBeDisabled());
  });

  it("enables next button on populating required values without additional links", async () => {
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }],
      preferredEmail: "",
      contentLanguage: "",
      firstName: "",
      lastName: ""
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: `"${dateOfBirthAfter18years}"`,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      preferredLanguage: "English",
      submittedUrl: ""
    };
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={[aConnectedAccount()]}
      />
    );
    await clearDateFor(/Date of Birth/i);
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();
    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    //Select preferred language
    await waitFor(
      async () => {
        await selectOption({ option: input.preferredLanguage, container, label: "Language" });
      },
      { timeout: 1_400 }
    );

    await waitFor(() => expect(nextButton).toBeEnabled());
  }, 12_000);

  it("occupies full width for the first website url", async () => {
    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} accounts={[]} />);

    await waitFor(() => {
      const { getAllByRole } = within(
        screen.getByRole("list", {
          name: /^Additional content and website links/i
        })
      );
      expect(getAllByRole("listitem")[0]).toHaveClass(
        "interested-creator-information-additional-content-url-without-delete"
      );
    });
  });

  it("saves interested creator information with additional links entered and validated the url", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years,
      preferredEmail: updatedPreferredEmail
    };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanResult
    });
    (InterestedCreatorsServices.saveApplication as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    await userEvent.type(await screen.findByLabelText(/^Website URL/i), "https://www.google.com");
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      // Verify content url call
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      // Verify update application call
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith(interestedCreator);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
    });
  });

  it("saves interested creator information with additional links not entered and not validated the url", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      preferredEmail: updatedPreferredEmail,
      dateOfBirth: dateOfBirthAfter18years,
      contentUrls: [{ url: "https://", followers: "" }] // whenever user clears the url(as per current implementation, `https://` added all the time), the value will be `https://` and while sending to api, we are passing it as empty string.
    };
    (InterestedCreatorsServices.saveApplication as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith(interestedCreator);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
    });
  });

  it("shows error message when API content scanning failed, when additional links we entered is invalid", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    const urlScanError = { ...urlScanResult, results: [{ ...scanResult, isSecure: false }] };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanError
    });
    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("shows error message when content scanning API failed with error code 422", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "https://-creatornetwork.ea.com", followers: "" }]
    };
    const urlScanError = {
      status: 422,
      code: "validate-content-urls-invalid-input",
      errors: {
        "urls[0]": ["value must be a valid URL. Invalid domain -creatornetwork.ea.com"]
      }
    };
    (SubmittedContentService.validateContent as jest.Mock).mockRejectedValue({
      response: {
        ...urlScanError,
        data: urlScanError
      }
    });
    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={[aConnectedAccount()]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith(
        {
          urls: [interestedCreator.contentUrls[0].url]
        },
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/URL provided is invalid/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("aligns the connected accounts card to center for tablet view when only one is connected", async () => {
    render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        accounts={[aConnectedAccount()]}
      />
    );

    expect(await screen.findByTestId("add-account-card")).toHaveClass("add-account-card");
  });

  it("shows the additional links section", async () => {
    render(<InterestedCreatorsInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} />);

    expect(await screen.findByRole("heading", { name: "Additional Content and Website Links" })).toBeInTheDocument();
    expect(
      await screen.findByText(
        "If you want to share specific content and other website links to be reviewed by our team you can add as many as you’d like below!"
      )
    ).toBeInTheDocument();
    expect(await screen.findByLabelText(/^Website URL/i)).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "Add another URL" })).toBeInTheDocument();
  });

  it("handles API error while submit information form with content URL's", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      preferredEmail: updatedPreferredEmail,
      contentUrls: [{ url: "https://", followers: "" }] // whenever user clears the url(as per current implementation, `https://` added all the time), the value will be `https://` and while sending to api, we are passing it as empty string.
    };
    (InterestedCreatorsServices.saveApplication as jest.Mock).mockRejectedValue({
      data: {
        statusCode: 401,
        message: "Unauthorized"
      }
    });

    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorsInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={[aConnectedAccount()]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledTimes(1);
      expect(InterestedCreatorsServices.saveApplication).toHaveBeenCalledWith(interestedCreator);
      expect(errorHandler).toHaveBeenCalledTimes(1);
    });
  });

  it("displays error message for duplicate URL with space", async () => {
    const duplicateUrl = "https://www.google.com";
    const duplicateUrlwithSpace = "https://www.google.com ";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorsInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );
    // Enter valid URL
    await enterValueFor(/^Website URL/i, duplicateUrl);
    // Add one more URL
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByLabelText(/^Website URL/i)).toHaveLength(2));

    // provide duplicate URL with space
    await userEvent.type(screen.getAllByLabelText(/^Website URL/i)[1], duplicateUrlwithSpace);

    await waitFor(() => {
      expect(screen.queryByText(formLabels.duplicateUrl)).toBeInTheDocument();
    });
  });

  const addAnotherUrl = async (count: number) => {
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(count));
  };

  afterEach(() => {
    windowSpy.mockRestore();
  });
});
