import "reflect-metadata";
import React, { memo, useCallback, useEffect, useMemo } from "react";
import withAuthenticatedUser from "../../../src/utils/WithAuthenticatedUser";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "../../../config";
import { useRouter } from "next/router";
import { useAppContext } from "../../../src/context";
import { useTranslation } from "react-i18next";
import labelsCommon from "../../../config/translations/common";
import labelsCriteriaOpportunity from "../../../config/translations/criteria-opportunity";
import labelsGameCodeOpportunity from "../../../config/translations/game-code";
import labelsOpportunities from "../../../config/translations/opportunities";
import labelsThanksOpportunity from "../../../config/translations/thanks-opportunity";
import OpportunityService from "../../../src/api/services/OpportunityService";
import {
  DOMAIN_ERROR,
  getExtractedErrorMessage,
  HAS_EXCEPTION,
  JOIN_OPPORTUNITY_STEPS,
  LOADING,
  onToastClose,
  OPPORTUNITY,
  OPPORTUNITY_STATUS,
  PARTICIPATION_STATUS,
  SESSION_USER,
  useDetectScreen,
  useIsMounted
} from "../../../utils";
import Error from "../../_error";
import OpportunityCriteria from "../../../components/joinOpportunitySteps/OpportunityCriteria";
import GameCode from "../../../components/joinOpportunitySteps/GameCode";
import ContentGuidelines from "../../../components/joinOpportunitySteps/ContentGuidelines";
import ThanksOpportunity from "../../../components/joinOpportunitySteps/ThanksOpportunity";
import withRegisteredUser from "../../../src/utils/WithRegisteredUser";
import withTermsAndConditionsUpToDate from "../../../src/utils/WithTermsAndConditionsUpToDate";
import Loading from "../../../components/Loading";
import RedirectException from "../../../src/utils/RedirectException";
import labelsContentSubmission from "../../../config/translations/content-submission";
import { AuthenticatedUserFactory } from "../../../src/analytics/BrowserAnalytics";
import { close, contentGuidelines, franchisesYouPlay, Icon, information } from "@eait-playerexp-cn/core-ui-kit";
import { Toast, useToast } from "../../../components/toast";
import Layout, { LayoutBody, LayoutHeader } from "../../../components/Layout";
import JoinOpportunityHeader from "../../../components/opportunities/JoinOpportunityHeader";
import { BackButton } from "../../../components/migrations/Header";
import runtimeConfiguration from "../../../src/configuration/runtimeConfiguration";
import flags from "../../../utils/feature-flags";
import featureFlags from "../../../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../../../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../../../src/serverprops/middleware/SaveInitialPage";
import errorLogger from "../../../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import verifyAccessToProgram from "../../../src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "../../../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import opportunityRegistrationProps from "../../../src/serverprops/OpportunityRegistrationProps";
import { useDependency } from "../../../src/context/DependencyContext";

export default memo(function Registrations({ user, WATERMARKS_URL, FLAG_NEW_NAVIGATION_ENABLED }) {
  const { analytics } = useDependency();
  const router = useRouter();
  const {
    dispatch,
    state: { opportunity, opportunityStatus, participationStatus, exceptionCode, sessionUser, isLoading, domainError }
  } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const isMounted = useIsMounted();
  const opportunityId = useMemo(() => router.query.id, [router.query.id]);
  const { error: errorToast } = useToast();
  const errorMap = new Map([]);
  const isMobileOrTab = useDetectScreen(1279);
  const backNavLinks = {
    registrations: `/opportunities/${opportunityId}`,
    "game-code": `/opportunities/${opportunityId}/registrations`,
    "content-guidelines": opportunity?.hasGameCodes
      ? `/opportunities/${opportunityId}/registrations?step=game-code`
      : `/opportunities/${opportunityId}/registrations`
  };

  const { t } = useTranslation([
    "common",
    "opportunities",
    "criteria-opportunity",
    "game-code",
    "thanks-opportunity",
    "content-submission"
  ]);
  const {
    layout,
    criteriaLabels,
    gameCodeLabels,
    opportunitiesLabels,
    thanksOpportunityLabels,
    contentSubmissionLabels
  } = useMemo(() => {
    return {
      layout: labelsCommon(t),
      criteriaLabels: labelsCriteriaOpportunity(t),
      gameCodeLabels: labelsGameCodeOpportunity(t),
      opportunitiesLabels: labelsOpportunities(t),
      contentSubmissionLabels: labelsContentSubmission(t),
      thanksOpportunityLabels: labelsThanksOpportunity(t)
    };
  }, [t]);

  const {
    main: { unhandledError }
  } = layout;
  useEffect(() => {
    if (opportunity) {
      const steps = [
        {
          icon: information,
          href: "/opportunities/[id]/registrations",
          title: opportunitiesLabels?.information
        },
        {
          icon: franchisesYouPlay,
          href: "/opportunities/[id]/registrations?step=game-code",
          title: opportunitiesLabels?.franchisesYouPlay
        },
        {
          icon: contentGuidelines,
          href: "/opportunities/[id]/registrations?step=content-guidelines",
          title: opportunitiesLabels?.contentGuidelinesTitle
        }
      ].filter(
        (item) =>
          (item.href === `/opportunities/[id]/registrations?step=game-code` && opportunity.hasGameCodes) ||
          (item.href === `/opportunities/[id]/registrations?step=content-guidelines` && opportunity.hasDeliverables) ||
          item.href === router.pathname
      );
      stableDispatch({ type: JOIN_OPPORTUNITY_STEPS, data: steps });
    }
  }, [opportunity]);

  useEffect(() => {
    if (opportunityStatus && opportunityStatus.id === opportunityId && opportunityStatus.status === "JOINED") {
      return router.push(`/opportunities/${opportunityId}`);
    } else {
      OpportunityService.getParticipationStatusWithSubmissionInformation([opportunityId])
        .then((res) => {
          if (isMounted()) {
            const status = res.data.length ? res.data[0] : { id: opportunityId, participationId: null, status: "NA" };
            stableDispatch({ type: OPPORTUNITY_STATUS, data: status });
            if (status && status.participationId !== null) {
              return router.push(`/opportunities/${opportunityId}`);
            }
          }
        })
        .catch((e) => {
          if (isMounted()) {
            stableDispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
          }
        });
    }
  }, [stableDispatch]);

  useEffect(() => {
    if (opportunity && !participationStatus) {
      stableDispatch({ type: LOADING, data: true });
      OpportunityService.getParticipationStatusWithSubmissionInformation([opportunityId])
        .then((res) => {
          if (res?.data?.[0]?.status) {
            stableDispatch({ type: PARTICIPATION_STATUS, data: res.data[0] });
          }
          stableDispatch({ type: LOADING, data: false });
        })
        .catch(() => {
          stableDispatch({ type: LOADING, data: false });
        });
    }
  }, [opportunity, opportunityId, stableDispatch, participationStatus]);

  useEffect(() => {
    stableDispatch({ type: OPPORTUNITY, data: null });
    stableDispatch({ type: LOADING, data: true });
    OpportunityService.getOpportunityWithDeliverables(opportunityId)
      .then((res) => {
        if (isMounted()) {
          stableDispatch({ type: OPPORTUNITY, data: res.data.opportunity });
          stableDispatch({ type: LOADING, data: false });
        }
      })
      .catch((e) => {
        if (isMounted()) {
          stableDispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
          stableDispatch({ type: LOADING, data: false });
        }
      });
  }, [opportunityId, stableDispatch]);

  useEffect(() => {
    if (isMounted()) {
      user && stableDispatch({ type: SESSION_USER, data: user });
    }
  }, [user, stableDispatch]);

  useEffect(() => {
    if (domainError) {
      const errorMessage = getExtractedErrorMessage(errorMap, domainError, unhandledError);
      errorToast(<Toast header={unhandledError} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />, {
        onClose: () => onToastClose(DOMAIN_ERROR, stableDispatch, unhandledError)
      });
    }
    return () => onToastClose(DOMAIN_ERROR, stableDispatch, unhandledError);
  }, [domainError]);

  const onGoBack = () => router.push(backNavLinks[router.query.step ? router.query.step : "registrations"]);

  const exitJoinFlow = useCallback(() => {
    analytics.cancelledJoinOpportunityFlow({
      locale: router.locale,
      opportunity,
      pageAbandoned: window.location.pathname + window.location.search
    });
    router.push(`/opportunities/${opportunity.id}`);
  }, [opportunity]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <div className="join-opportunity-container">
        <LayoutHeader pageTitle={opportunitiesLabels.joinOpportunity} />
        <LayoutBody>
          <div className="join-opportunity-header-container">
            <div className="join-opportunity-header" role="heading" aria-level={6}>
              {router.query.step !== "thanks" && (
                <>
                  {isMobileOrTab && (
                    <BackButton back={opportunitiesLabels.back} isFirstStep={false} onGoBack={onGoBack} />
                  )}
                  <div className="join-opportunity-header-close">
                    <button aria-label={opportunitiesLabels.exitJoinOpportunityFlow} onClick={exitJoinFlow}>
                      <Icon className="exit-join-flow" icon={close} />
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>

          {router.query.step !== "thanks" && (
            <JoinOpportunityHeader
              opportunitiesLabels={opportunitiesLabels}
              opportunity={opportunity}
              onGoBack={onGoBack}
            />
          )}

          <div className="join-opportunity-bg"> </div>
          <div className="join-opportunity-page">
            {isLoading ? (
              <Loading />
            ) : (
              <>
                {opportunity &&
                  opportunityStatus &&
                  opportunityStatus.participationId === null &&
                  opportunityStatus.status !== "JOINED" &&
                  (() => {
                    switch (router.query.step) {
                      case "criteria":
                        return <OpportunityCriteria {...{ opportunity, analytics, layout, criteriaLabels }} />;
                      case "game-code":
                        return <GameCode {...{ opportunity, analytics, layout, gameCodeLabels, t }} />;
                      case "content-guidelines":
                        return (
                          <ContentGuidelines
                            {...{
                              opportunity,
                              WATERMARKS_URL,
                              layout,
                              opportunitiesLabels,
                              contentSubmissionLabels,
                              analytics
                            }}
                          />
                        );
                      case "thanks":
                        return (
                          <ThanksOpportunity
                            {...{
                              opportunity,
                              opportunitiesLabels,
                              thanksOpportunityLabels,
                              layout,
                              FLAG_NEW_NAVIGATION_ENABLED
                            }}
                          />
                        );
                      default:
                        return (
                          <OpportunityCriteria
                            {...{ opportunity, analytics, layout, criteriaLabels, participationStatus }}
                          />
                        );
                    }
                  })()}
              </>
            )}
          </div>
        </LayoutBody>
      </div>
    </Layout>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(opportunityRegistrationProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      WATERMARKS_URL: config.WATERMARKS_URL,
      ...(await serverSideTranslations(locale, [
        "common",
        "opportunities",
        "criteria-opportunity",
        "game-code",
        "thanks-opportunity",
        "content-submission"
      ])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled()
    }
  };
};
