import { useTranslation } from "next-i18next";
import React, { useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsAgeRestriction from "@config/translations/age-restriction";
import labelsC<PERSON>mon from "@config/translations/common";
import { useRouter } from "next/router";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody } from "@components/Layout";
import AgeRestrictionPage from "@components/pages/interested-creators/age-restriction/AgeRestrictionPage";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "utils/feature-flags";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import ageRestrictionProps from "@src/serverprops/AgeRestrictionProps";
import { GetServerSidePropsResult } from "next";

export type AgeRestrictionProps = {
  runtimeConfiguration?: Record<string, unknown>;
};

export default function AgeRestriction(): JSX.Element {
  const { t } = useTranslation(["common", "age-restriction"]);
  const router = useRouter();
  const { ageRestrictionLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);
    return {
      ageRestrictionLabels: {
        close,
        creatorNetwork,
        ...labelsAgeRestriction(t)
      }
    };
  }, [t]);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <AgeRestrictionPage {...{ router, ageRestrictionLabels }} />
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(ageRestrictionProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<AgeRestrictionProps>;
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      ...(await serverSideTranslations(locale, ["common", "age-restriction"]))
    }
  };
};
