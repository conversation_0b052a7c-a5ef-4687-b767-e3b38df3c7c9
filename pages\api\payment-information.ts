import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import { createRouter } from "next-connect";
import GetPaymentInformationAction from "../../src/actions/paymentInformation/GetPaymentInformation";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(GetPaymentInformationAction);
    const paymentInformation = await action.execute(req.body);

    res.status(200).json(paymentInformation);
  });

export default router.handler({ onError });
