import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsComplete from "../../config/translations/complete";
import labelsCommon from "../../config/translations/common";
import flags from "../../utils/feature-flags";
import { ApplicationCompletedPage } from "@components/pages/interested-creators/ApplicationCompletedPage";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Layout, { LayoutBody } from "../../components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationCompletePageProps from "@src/serverprops/ApplicationCompletePageProps";

export type CompletePageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  locale: string;
  interestedCreator: InterestedCreator;
};

export default memo(function Complete({ interestedCreator, INTERESTED_CREATOR_REAPPLY_PERIOD }: CompletePageProps) {
  const { t } = useTranslation(["common", "complete"]);
  const { completeLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);

    return {
      completeLabels: { creatorNetwork, close, ...labelsComplete(t) }
    };
  }, [t]);
  const { creatorNetwork, close } = completeLabels;

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <ApplicationCompletedPage
            completeLabels={completeLabels}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            emailId={interestedCreator.originEmail}
            defaultGamerTag={interestedCreator.defaultGamerTag}
            submittedDate={interestedCreator.createdDate}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
});

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationCompletePageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<CompletePageProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, null, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      locale,
      ...(await serverSideTranslations(locale, ["common", "complete"])),
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
