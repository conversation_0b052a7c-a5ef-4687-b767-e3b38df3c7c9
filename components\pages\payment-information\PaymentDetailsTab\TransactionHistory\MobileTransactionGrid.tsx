import { clock, Icon, info, MobileTransactionHistory, outlineCheck } from "@eait-playerexp-cn/core-ui-kit";
import Pagination from "../../../../dashboard/Pagination";
import React, { FC } from "react";
import {
  mapOpportunityTypeForAPI,
  PaymentsHistory,
  PaymentsHistoryWithCreatorCode,
  Transaction,
  TransactionWithCreatorCode
} from "@src/api/services/PaymentsService";
import ToolTip from "../../../../ToolTip";
import { TransactionHistoryLabelProps } from "../PaymentDetailsTab";
import classNames from "classnames";
import { PaginationProps } from "../PaymentDetailsTab";
import Link from "next/link";
import CreatorCodeTooltip from "./CreatorCodeTooltip";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";

type MobileTransactionGridProps = {
  paymentsHistory: PaymentsHistory | PaymentsHistoryWithCreatorCode;
  labels: TransactionHistoryLabelProps;
  isShowingPagination: boolean;
  paginationProps: PaginationProps;
  analytics: BrowserAnalytics;
};

const MobileTransactionGrid: FC<MobileTransactionGridProps> = ({
  isShowingPagination,
  paymentsHistory,
  labels,
  paginationProps,
  analytics
}) => {
  const router = useRouter();
  return (
    <div
      className={classNames("payment-details-transaction-history-wrapper", { "with-pagination": isShowingPagination })}
    >
      {paymentsHistory.details.map((transaction: Transaction | TransactionWithCreatorCode, index) => {
        return (
          <MobileTransactionHistory
            key={index}
            {...{
              opportunityImage: transaction.opportunityImage,
              opportunityTitle: (function () {
                const { opportunityTitle, opportunityId, opportunityType } = transaction;
                return opportunityType === mapOpportunityTypeForAPI("sims_ugx_opportunity") ? (
                  <div title={opportunityTitle} className="record-opportunity-title">
                    {opportunityTitle.length > 20 ? `${opportunityTitle.slice(0, 20)}...` : opportunityTitle}
                  </div>
                ) : (
                  <Link
                    href={`/opportunities/${opportunityId}`}
                    title={opportunityTitle}
                    onClick={() => {
                      analytics.clickedOpportunityDescription({ locale: router.locale });
                    }}
                  >
                    {opportunityTitle.length > 20 ? `${opportunityTitle.slice(0, 20)}...` : opportunityTitle}
                  </Link>
                );
              })(),
              contractLink: transaction.contractLink,
              opportunityType: (
                <CreatorCodeTooltip
                  transaction={transaction}
                  labels={{
                    typeCreatorCode: labels.typeCreatorCode,
                    typeOpportunity: labels.typeOpportunity,
                    simsMakerProgram: labels.simsMakerProgram
                  }}
                />
              ),
              status: (function () {
                const { status } = transaction;
                const icon = (status as string).toLowerCase() === "pending" ? clock : outlineCheck;
                const classes =
                  (status as string).toLowerCase() !== "pending" ? "transaction-history-success-text" : "";
                const statusText =
                  (status as string).toLowerCase() === "pending" ? labels.statusPending : labels.statusProcessed;
                return (
                  <div className={classNames("transaction-grid-icontext-wrapper", classes)}>
                    <Icon icon={icon} className="transaction-grid-icontext-icon" />
                    <span className="transaction-grid-icontext-text">{statusText as string}</span>
                  </div>
                );
              })(),
              processedDate: transaction.processedDateLabel(),
              amount: transaction.amount.abbreviateOn("M").toString(),
              labels: {
                opportunityTypeLabel: labels.paymentGridType,
                statusLabel: labels.paymentGridStatus,
                processedDateLabel: (
                  <div className="transaction-history-grid-header-date-icon">
                    <ToolTip overlay={labels.paymentGridDateHelp}>
                      <Icon icon={info} className="transaction-history-tooltip-icon" />
                    </ToolTip>
                    <span>{labels.paymentGridDate}</span>
                  </div>
                ),
                contractLabel: labels.downloadContractLabel,
                amountLabel: labels.paymentGridAmountDue,
                linkAriaText: labels.downloadContract,
                opportunityImageAltText: "Opportunity image thumbnail"
              },
              onDownloadLinkClickHandler: () => {
                analytics.downloadedPaymentContract({ locale: router.locale });
              }
            }}
          />
        );
      })}
      {isShowingPagination && <Pagination {...paginationProps} />}
    </div>
  );
};
export default MobileTransactionGrid;
