import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import labelsBreadCrumb from "../config/translations/breadcrumb";
import labelsConnectAccounts from "../config/translations/connect-accounts";
import labelsCommon from "../config/translations/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import MigrationLayout from "../components/MigrationLayout";
import labelsCommunicationPreferences from "../config/translations/communication-preferences";
import Form from "../components/Form";
import CommunicationPreferencesInput from "../components/migrations/CommunicationPreferencesInput";
import Footer from "../components/migrations/Footer";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import CreatorsService from "../src/api/services/CreatorsService";
import { useRouter } from "next/router";
import CreatorForm from "../components/FormRules/CreatorForm";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  onToastClose,
  SESSION_USER,
  toastContent,
  useAsync,
  useIsMounted,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import { useAppContext } from "../src/context";
import Error from "./_error";
import withUnregisteredUser from "../src/utils/WithUnregisteredUser";
import Loading from "../components/Loading";
import { Toast, useToast } from "../components/toast";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import RedirectException from "../src/utils/RedirectException";
import MigrationModal from "../components/migrations/MigrationModal";
import { useFormContext } from "react-hook-form";
import labelsOpportunities from "../config/translations/opportunities";
import CancelRegistrationModal from "../components/pages/interested-creators/CancelRegistrationModal";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import communicationPreferencesProps from "../src/serverprops/CommunicationPreferencesProps";
import verifyIncompleteRegistration from "../src/serverprops/middleware/VerifyIncompleteRegistration";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const CommunicationPreferencesForm = ({
  translation,
  rules,
  languages,
  locales,
  communications,
  showAddConfirmation,
  setShowAddConfirmation,
  labels,
  layout,
  removeAccount,
  setRemoveAccount,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  onClose,
  pending,
  showMigration,
  setShowMigration,
  submitHandle,
  stableDispatch,
  router,
  navigateToPage
}) => {
  const { getValues, formState } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!formState.isDirty, [formState]);
  const {
    state: { userNavigated },
    dispatch
  } = useAppContext();

  const onSave = () => submitHandle(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    navigateToPage ? router.push(navigateToPage) : router.push("/connect-accounts");
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      navigateToPage ? router.push(navigateToPage) : router.push("/connect-accounts");
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  return (
    <>
      <CommunicationPreferencesInput
        {...{
          translation,
          rules,
          languages,
          locales,
          communications,
          showAddConfirmation,
          setShowAddConfirmation,
          labels,
          layout,
          removeAccount,
          setRemoveAccount,
          accountToRemove,
          setAccountToRemove,
          showRemoveAccountModal,
          setShowRemoveAccountModal
        }}
      />
      <Footer
        {...{
          buttons: layout.buttons,
          onCancel: onClose,
          disableSubmit: pending,
          isPending: pending
        }}
      />
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard }} />
    </>
  );
};

export default function CommunicationPreferences({ user }) {
  const {
    analytics,
    metadataClient,
    creatorsClient,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE },
    errorHandler
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(
    () => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE),
    [creatorsClient, DEFAULT_AVATAR_IMAGE]
  );
  const router = useRouter();
  const isMounted = useIsMounted();
  const {
    dispatch,
    state: {
      popupOpened = false,
      exceptionCode = null,
      sessionUser = null,
      isValidationError,
      isError,
      onboardingSteps
    } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { t } = useTranslation(["common", "breadcrumb", "communication-preferences", "opportunities"]);
  const { error: errorToast } = useToast();
  const { layout, translation, labels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      translation: labelsCommunicationPreferences(t),
      labels: labelsConnectAccounts(t)
    };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = translation;
  const [removeAccount, setRemoveAccount] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [communications, setCommunications] = useState(null);
  const [languages, setLanguages] = useState(null);
  const [locales, setLocales] = useState(null);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);
  const rules = useMemo(() => CreatorForm.communicationRules(translation), [translation]);
  const [accountToRemove, setAccountToRemove] = useState(false);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  // Save form data and navigate to next page
  const submitHandle = useCallback(
    async (data, navigateBack, navigateToPage) => {
      stableDispatch({ type: USER_NAVIGATED, data: false });
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      // Creators BFF PUT
      try {
        let preferredLanguage = null;
        if (data.preferredLanguage.value && data.preferredLanguage.label) {
          preferredLanguage = { code: data.preferredLanguage.value, name: data.preferredLanguage.label };
        }
        const contentLanguages = data.contentLanguages.map((language) => ({
          code: language.value,
          name: language.label
        }));
        const communicationPreferencesData = {
          ...data,
          contentLanguages
        };
        delete communicationPreferencesData.preferredLanguage;

        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              communicationPreferences: communicationPreferencesData,
              program: { code: PROGRAM_CODE, preferredLanguage }
            })
          : await CreatorsService.update({ communicationPreferences: data });

        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        analytics.confirmedCommunicationPreferences({
          locale: router.locale,
          contentLanguages: data.contentLanguages?.map((language) => language.label).join(",") || ""
        });
        navigateToPage
          ? router.push(navigateToPage)
          : navigateBack
          ? router.push("/connect-accounts")
          : router.push("/terms-and-conditions");
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, router, onboardingSteps]
  );
  const { pending, execute: submitHandleClb } = useAsync(submitHandle, false);
  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  useEffect(() => {
    async function fetchData() {
      // Creators BFF GET
      try {
        const creator = FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.getCreator(PROGRAM_CODE)
          : (await CreatorsService.getCreatorWithPayableStatus()).data;
        if (FLAG_PER_PROGRAM_PROFILE) {
          creator.communicationPreferences.contentLanguages = creator?.communicationPreferences.contentLanguages.map(
            (language) => ({
              ...language,
              value: language.code,
              label: language.name
            })
          );
          creator.communicationPreferences.preferredLanguage = {
            ...creator.program.preferredLanguage,
            value: creator?.program?.preferredLanguage?.code,
            label: creator?.program?.preferredLanguage?.name
          };
        }
        setCommunications(creator.communicationPreferences);
        const languages = await metadataService.getLanguages();
        languages && setLanguages(languages);
        const locales = await metadataService.getLocales();
        locales && setLocales(locales);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    fetchData();
  }, [stableDispatch]);

  /** ReFetch Creators on window closed */
  useEffect(() => {
    async function refreshCreatorInformation() {
      if ((!popupOpened && showAddConfirmation) || !accountToRemove) {
        try {
          if (isMounted()) {
            // Creators BFF GET
            const creator = FLAG_PER_PROGRAM_PROFILE
              ? await creatorService.getCreator(PROGRAM_CODE)
              : (await CreatorsService.getCreatorWithPayableStatus()).data;
            if (FLAG_PER_PROGRAM_PROFILE) {
              creator.communicationPreferences.contentLanguages =
                creator?.communicationPreferences.contentLanguages.map((language) => ({
                  ...language,
                  value: language.code,
                  label: language.name
                }));
              creator.communicationPreferences.preferredLanguage = {
                ...creator.program.preferredLanguage,
                value: creator?.program?.preferredLanguage?.code,
                label: creator?.program?.preferredLanguage?.name
              };
            }
            setCommunications(creator.communicationPreferences);
            setShowAddConfirmation(false);
          }
        } catch (error) {
          errorHandler(stableDispatch, error);
        }
      }
    }
    refreshCreatorInformation();
  }, [popupOpened, showAddConfirmation, accountToRemove, stableDispatch]);

  useEffect(() => {
    if (user) stableDispatch({ type: SESSION_USER, data: user });
  }, [user, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <MigrationLayout
      pageTitle={translation.title}
      {...{
        ...layout,
        onClose,
        isRegistrationFlow: true,
        isOnboardingFlow: true,
        stableDispatch,
        onGoBack,
        setShowMigration,
        setNavigateToPage,
        completed: layout.completed
      }}
      translate={t}
    >
      <div className="communication-preferences-container">
        <div className="mg-intro">
          <h3 className="mg-intro-title">{translation.title}</h3>
          <div className="mg-intro-description">{translation.description}</div>
        </div>
        {!communications && (
          <div className="loader">
            <Loading />
          </div>
        )}
        {communications && languages && locales && rules && (
          <Form mode="onChange" onSubmit={submitHandleClb}>
            <CommunicationPreferencesForm
              {...{
                translation,
                rules,
                languages,
                locales,
                communications,
                showAddConfirmation,
                setShowAddConfirmation,
                labels,
                layout,
                removeAccount,
                setRemoveAccount,
                accountToRemove,
                setAccountToRemove,
                showRemoveAccountModal,
                setShowRemoveAccountModal,
                onClose,
                pending,
                showMigration,
                setShowMigration,
                submitHandle,
                stableDispatch,
                router,
                navigateToPage
              }}
            />
          </Form>
        )}
      </div>
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyIncompleteRegistration)
      .use(addLocaleCookie(locale))
      .get(communicationPreferencesProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      ...(await serverSideTranslations(locale, [
        "common",
        "breadcrumb",
        "communication-preferences",
        "connect-accounts",
        "opportunities"
      ]))
    }
  };
};
