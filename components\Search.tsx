import { forwardRef, Ref, useEffect, useRef, useState } from "react";
import classNames from "classnames";

type SearchOptionsProps = {
  value: string;
  label: string;
  image: string;
};

type SearchProps = {
  value: SearchOptionsProps & {
    type?: "PRIMARY";
  };
  options: SearchOptionsProps[];
  onChange: (item) => void;
  placeholder: string;
  label?: string;
  disabled?: boolean;
  errorMessage?: string;
  ariaLabel?: string;
};

const Search = forwardRef(function Search(props: SearchProps, ref: Ref<HTMLInputElement>) {
  const innerRef = useRef<HTMLInputElement>(null);
  const inputRef = ref || innerRef;
  const { value, options, onChange, placeholder, label, disabled, errorMessage, ariaLabel = "" } = props;

  const [isListOpen, setIsListOpen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [optionsToDisplay, setOptionsToDisplay] = useState(options);
  const [selectedItem, setSelectedItem] = useState({ label: "", value: "" });
  const toggleList = () => {
    setIsListOpen(!isListOpen);
  };

  const selectItem = (item) => {
    const { label } = item;
    setIsListOpen(false);
    setSelectedItem(item);
    setSearchText(label);
    onChange?.(item);
  };

  const onChangeText = (e) => {
    const tempOptions = options.filter((item) => item.label.toLowerCase().includes(e.target.value.toLowerCase()));
    setOptionsToDisplay(tempOptions);
    setSearchText(e.target.value);
    if (!isListOpen) {
      setIsListOpen(true);
    }
  };

  useEffect(() => {
    if (value) {
      const selected = options.filter((element) => element.value === (typeof value === "object" ? value.value : value));
      if (selected.length === 1) {
        setOptionsToDisplay(options);
        setSearchText(selected[0].label);
      }
    }
  }, [value]);

  return (
    <>
      <div className="search-box">
        {label && (
          <label htmlFor="search" className="search-label">
            {label}
          </label>
        )}
        <input
          ref={inputRef}
          type="text"
          id="search"
          className="search-text-field"
          placeholder={placeholder}
          autoComplete="off"
          value={searchText}
          name={"search-input"}
          onClick={toggleList}
          onBlur={() => {
            setIsListOpen(false);
            setSearchText(selectedItem.label ? selectedItem.label : label);
          }}
          disabled={disabled}
          onChange={onChangeText}
          aria-label={ariaLabel || label || placeholder}
        />
        {isListOpen && (
          <div role="list" className="search-list">
            <div className="search-scroll-list">
              {optionsToDisplay.map((item) => (
                <button
                  type="button"
                  className={classNames(
                    {
                      selected: selectedItem.value == item.value
                    },
                    "search-list-item"
                  )}
                  key={item.value}
                  onMouseDown={() => selectItem(item)}
                >
                  <span className={classNames("search-option-label")}>{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
        {errorMessage && !isListOpen && <div className="form-error-message">{errorMessage}</div>}
      </div>
    </>
  );
});

export default Search;
