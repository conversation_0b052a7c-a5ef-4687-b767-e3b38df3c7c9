import {
  dollarPayment,
  faqs,
  grid,
  logout,
  MenuItems,
  message,
  myContent,
  opportunities,
  Option,
  TopNavButtonProps,
  TopNavigation,
  user as userIcon
} from "@eait-playerexp-cn/core-ui-kit";
import React, { ComponentType, memo, useCallback, useMemo } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import labelsCommon from "../config/translations/common";
import ProfileDropdown from "./header/ProfileDropdown";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useDetectScreen } from "utils";
import Loading from "./Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";

const NotificationsBell: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationsBell"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

type ProgramTopNavigationProps = {
  analytics: BrowserAnalytics;
  notificationsLabels?: Record<string, string>;
};

export default memo(function ProgramTopNavigation({ analytics, notificationsLabels }: ProgramTopNavigationProps) {
  const router = useRouter();
  const isMobile = useDetectScreen(767);
  const {
    notificationsClient,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      PROGRAM_CODE,
      DEFAULT_NOTIFICATION_PROGRAM,
      FLAG_NEW_NAVIGATION_ENABLED,
      MENU_ITEMS,
      FLAG_CREATORS_API_WITH_PROGRAM,
      user
    }
  } = useDependency();
  const programCode = PROGRAM_CODE;
  const isMobileOrTab = useDetectScreen(1279);
  const { t } = useTranslation(["common", "opportunities", "notifications"]);
  const defaultMenuItem: MenuItems[] = [
    {
      label: MENU_ITEMS[programCode].label,
      value: programCode,
      gradients: MENU_ITEMS[programCode].gradients
    }
  ];
  const menuItems =
    FLAG_CREATORS_API_WITH_PROGRAM && user?.programs
      ? user.programs.map((program) => ({
          label: MENU_ITEMS[program].label,
          value: program,
          gradients: MENU_ITEMS[program].gradients
        }))
      : defaultMenuItem;
  const { header } = useMemo(() => {
    const labels = {
      header: labelsCommon(t).header
    };
    return labels;
  }, [t]);

  const onMenuSelect = useCallback((item: Option) => {
    if (item.value) {
      localStorage.setItem("programCode", item.value);
    }
  }, []);

  const onNotficationBellClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    router.push("/notifications");
  };

  const NotificationBellContianer = () => {
    return isMobile ? (
      <div className="notificaion-bell-parent-container" onClick={onNotficationBellClick}>
        <NotificationsBell
          labels={notificationsLabels}
          locale={router.locale}
          configuration={{
            client: notificationsClient,
            programHosts: NOTIFICATION_BASE_URLS,
            program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
            defaultProgram: DEFAULT_NOTIFICATION_PROGRAM,
            enableNewNavigation: FLAG_NEW_NAVIGATION_ENABLED
          }}
        />
        <span className="notification-bell-text">{notificationsLabels.notification}</span>
      </div>
    ) : (
      <NotificationsBell
        labels={notificationsLabels}
        locale={router.locale}
        configuration={{
          client: notificationsClient,
          programHosts: NOTIFICATION_BASE_URLS,
          program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
          defaultProgram: DEFAULT_NOTIFICATION_PROGRAM,
          enableNewNavigation: FLAG_NEW_NAVIGATION_ENABLED
        }}
      />
    );
  };

  const AvatarContainer = () => {
    return !isMobileOrTab ? (
      <div className="profile-avatar-container">
        <ProfileDropdown labels={header} analytics={analytics}>
          <img
            alt=""
            src={"https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"}
            className="navigation-button-image"
          />
        </ProfileDropdown>
      </div>
    ) : null;
  };

  const topNavButtons = [
    {
      button: {
        title: header.messages,
        asset: message,
        onClick: undefined // will be imlemented in future
      },
      active: false
    },
    {
      button: {
        title: "Payment",
        onClick: () => router.push("/payment-information"),
        asset: dollarPayment
      },
      active: router.pathname === "/profile" && router.query.section === "payment-information"
    },
    {
      children: <NotificationBellContianer />,
      active: router.pathname === "/notifications"
    },
    {
      children: <AvatarContainer />,
      active: router.pathname === "/profile"
    }
  ] as TopNavButtonProps[];

  const creatorhubButtons = [
    {
      id: "dashboard",
      label: header.dashboard,
      icon: grid,
      onClick: () => router.push("/dashboard"),
      active: router.pathname === "/dashboard"
    },
    {
      id: "opporunities",
      label: header.opportunities,
      icon: opportunities,
      onClick: () => router.push("/opportunities"),
      active: router.pathname === "/opportunities"
    },
    {
      id: "my_content",
      label: header.myContent,
      icon: myContent,
      onClick: () => router.push("/my-content"),
      active: router.pathname === "/my-content"
    },
    {
      id: "faq",
      label: header.faqs,
      icon: faqs,
      onClick: () => router.push("/faq"),
      active: router.pathname === "/faq"
    },
    {
      id: "myProfile",
      label: header.myProfile,
      icon: userIcon,
      onClick: () => router.push("/profile"),
      active: router.pathname === "/profile"
    },
    {
      id: "logout",
      label: header.signout,
      icon: logout,
      onClick: () => router.push("/api/logout"),
      active: false
    }
  ];

  return (
    <div className="header-topnav-parent-container">
      <TopNavigation
        dropdownMenu={{
          dropdownLabel: menuItems.find((item) => item.value === programCode)?.label,
          onMenuItemSelect: onMenuSelect,
          menuItems
        }}
        rightNavigationButtons={topNavButtons}
        sideNavButtons={creatorhubButtons}
      />
    </div>
  );
});
