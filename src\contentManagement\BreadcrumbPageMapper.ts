import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type BreadcrumbPageLabels = {
  breadcrumbPageLabels: {
    modalTitle: string;
    communicationPreferences: string;
    termsAndConditions: string;
    connectAccounts: string;
    franchisesYouPlay: string;
    modalMessage: string;
  };
};

export class BreadcrumbPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): BreadcrumbPageLabels {
    const microCopy = new MicroCopy(microCopies);
    return {
      breadcrumbPageLabels: {
        modalTitle: microCopy.get("breadcrumb.modalTitle"),
        communicationPreferences: microCopy.get("breadcrumb.communicationPreferences"),
        termsAndConditions: microCopy.get("breadcrumb.termsAndConditions"),
        connectAccounts: microCopy.get("breadcrumb.connectAccounts"),
        franchisesYouPlay: microCopy.get("breadcrumb.franchisesYouPlay"),
        modalMessage: microCopy.get("breadcrumb.modalMessage")
      }
    };
  }
}
