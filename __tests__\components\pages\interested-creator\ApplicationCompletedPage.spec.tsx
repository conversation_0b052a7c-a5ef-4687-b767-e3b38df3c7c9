import { act, render, screen } from "@testing-library/react";
import {
  ApplicationCompletedPage,
  CompleteLabels
} from "../../../../components/pages/interested-creators/ApplicationCompletedPage";
import { axe, toHaveNoViolations } from "jest-axe";
import { applicationCompletedTranslations } from "../../../translations/index";
import { commonTranslations } from "../../../translations";

expect.extend(toHaveNoViolations);

const completeLabels: CompleteLabels = {
  ...applicationCompletedTranslations.completeLabels,
  ...commonTranslations.applicantLabels
};

describe("ApplicationCompleted", () => {
  it("shows interested creator application completed labels", () => {
    render(<ApplicationCompletedPage completeLabels={completeLabels} />);
    const titleElements = screen.getByText(completeLabels.title);
    const subTitleElements = screen.getByText(completeLabels.subTitle);
    const description = screen.getByText(completeLabels.description);
    const backHome = screen.getByText(completeLabels.backHome);

    expect(titleElements).toBeInTheDocument();
    expect(subTitleElements).toBeInTheDocument();
    expect(description).toBeInTheDocument();
    expect(backHome).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows interested creator application completed page labels and table", () => {
      render(
        <ApplicationCompletedPage
          completeLabels={completeLabels}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 02, 2024"
          emailId="<EMAIL>"
          defaultGamerTag="Test"
        />
      );

      expect(screen.getByText(completeLabels.submissionCompleteSubTitle)).toBeInTheDocument();
      expect(screen.getByText(completeLabels.submissionCompleteDescription)).toBeInTheDocument();
      expect(screen.getByText(completeLabels.gamerTag)).toBeInTheDocument();
      expect(screen.getByText(completeLabels.email)).toBeInTheDocument();
      expect(screen.getByText(completeLabels.status)).toBeInTheDocument();
      expect(screen.getByText(completeLabels.submissionDate)).toBeInTheDocument();
      expect(screen.getByText("Jan 02, 2024")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationCompletedPage completeLabels={completeLabels} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
