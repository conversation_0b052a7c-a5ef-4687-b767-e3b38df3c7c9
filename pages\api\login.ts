import "reflect-metadata";
import { NextApiResponse } from "next";
import config from "../../config";
import { createRouter } from "next-connect";
import onError from "../../src/api/AuthErrorHandler";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import LegacyRedirectToLoginController from "@src/controllers/LegacyRedirectToLoginController";
import { RedirectToLoginController } from "@eait-playerexp-cn/authentication";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(
    config.FLAG_PER_PROGRAM_PROFILE ? RedirectToLoginController : LegacyRedirectToLoginController
  );
  await controller.handle(req, res);
});

export default router.handler({ onError });
