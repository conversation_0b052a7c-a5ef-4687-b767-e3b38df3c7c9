import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ApplicationRejectedPageLabels = {
  applicationRejectedLabels: {
    email: string;
    reApplyDescription: string;
    closed: string;
    descriptionPara4: string;
    descriptionPara1: string;
    submissionDate: string;
    descriptionPara3: string;
    descriptionPara2: string;
    submissionReviewedDescription: string;
    gamerTag: string;
    status: string;
    title: string;
    reviewAndResubmit: string;
    returnToCreatorNetwork: string;
    submissionReviewed: string;
    reApplyTitle: string;
    programLabel: string;
    programName: string;
    backHome: string;
    returnToHomePage: string;
  };
};

export class ApplicationRejectedPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ApplicationRejectedPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      applicationRejectedLabels: {
        email: microCopy.get("applicationRejected.email"),
        reApplyDescription: microCopy.get("applicationRejected.reApplyDescription"),
        closed: microCopy.get("applicationRejected.closed"),
        descriptionPara4: microCopy.get("applicationRejected.descriptionPara4"),
        descriptionPara1: microCopy.get("applicationRejected.descriptionPara1"),
        submissionDate: microCopy.get("applicationRejected.submissionDate"),
        descriptionPara3: microCopy.get("applicationRejected.descriptionPara3"),
        descriptionPara2: microCopy.get("applicationRejected.descriptionPara2"),
        submissionReviewedDescription: microCopy.get("applicationRejected.submissionReviewedDescription"),
        gamerTag: microCopy.get("applicationRejected.gamerTag"),
        status: microCopy.get("applicationRejected.status"),
        title: microCopy.get("applicationRejected.title"),
        reviewAndResubmit: microCopy.get("applicationRejected.reviewAndResubmit"),
        returnToCreatorNetwork: microCopy.get("applicationRejected.returnToCreatorNetwork"),
        submissionReviewed: microCopy.get("applicationRejected.submissionReviewed"),
        reApplyTitle: microCopy.get("applicationRejected.reApplyTitle"),
        programLabel: microCopy.get("applicationRejected.programLabel"),
        programName: microCopy.get("applicationRejected.programName"),
        backHome: microCopy.get("applicationRejected.backHome"),
        returnToHomePage: microCopy.get("applicationRejected.backHome")
      }
    };
  }
}
