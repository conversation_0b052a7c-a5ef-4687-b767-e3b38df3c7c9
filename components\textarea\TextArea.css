.form-textarea-field {
  @apply box-border flex min-h-[4.5rem] appearance-none rounded-sm border border-gray-90 px-meas4 py-meas6 font-text-regular text-gray-90 hover:bg-gray-10 disabled:text-gray-50 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.form-textarea-input-box-disabled {
  @apply border-gray-30;
}
.form-textarea-label-box {
  @apply flex items-center justify-between;
}
.form-textarea-character-count {
  @apply font-text-regular text-caption text-gray-50;
}
.form-textarea-input-box-focused {
  @apply border-2 text-gray-50 outline-none;
}

.form-textarea-input-box-error {
  @apply border-error-50 text-gray-60;
}

.form-textarea-field {
  @apply font-text-regular text-body-default text-gray-90 disabled:text-gray-30;
}

.form-textarea-label {
  @apply flex cursor-auto font-text-regular text-caption font-bold text-gray-90;
}
.form-textarea-box {
  @apply flex flex-col gap-y-meas2 pt-meas12;
}
.form-error-message {
  @apply flex items-start;
}
