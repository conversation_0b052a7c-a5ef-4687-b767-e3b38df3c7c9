import "reflect-metadata";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import { memo, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import labelsCommon from "../config/translations/common";
import LabelsFAQs from "../config/translations/faq";
import withUserSession from "../src/utils/WithUserSession";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import flags from "../utils/feature-flags";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import Footer from "../components/footer/ProgramFooter";
import FaqsPage from "../components/pages/faq/FaqsPage";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import faqProps from "@src/serverprops/FaqProps";
import { GetServerSidePropsResult } from "next";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import { useDependency } from "@src/context/DependencyContext";

export type FrequentlyAskedQuestionsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  interestedCreator: boolean;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
};

export default memo(function FrequentlyAskedQuestions({
  user,
  interestedCreator,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED
}: FrequentlyAskedQuestionsProps) {
  const { analytics } = useDependency();
  const { locale, pathname: page } = useRouter();
  const { t } = useTranslation(["common", "faq", "notifications", "connect-accounts", "opportunities"]);
  const { layout, faqLabels, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    return { faqLabels: LabelsFAQs(t), layout: labelsCommon(t), notificationsLabels: notificationBellLabels };
  }, [t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const labels = { ...faqLabels, layout };

  useEffect(() => {
    if (user) {
      analytics.viewedMarketingPage({ locale, page });
    }
  }, [analytics, locale, page, user]);

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.faq}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={interestedCreator}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        <FaqsPage labels={labels} />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={locale}
          labels={layout.footer}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
});

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(faqProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<FrequentlyAskedQuestionsProps>;
  }

  const user = await withUserSession(req, res);
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      ...(await serverSideTranslations(locale, [
        "common",
        "faq",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled()
    }
  };
};
