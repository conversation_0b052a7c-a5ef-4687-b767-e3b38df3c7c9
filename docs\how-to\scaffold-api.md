---
currentMenu: howto
---

## Scaffold a new API route

Creating a new [API route](https://nextjs.org/docs/api-routes/introduction) is a repetitive task that generates a known
set of files.

- An API route file
- A controller file
- An HTTP client file
- 2 test files (for the HTTP client and the controller)

Use the following command instead of creating the above files manually.

```bash
npm run generate:api
```

This will prompt you for more information as shown below

- An [API version](https://restfulapi.net/versioning/) (optional)
- A folder name where the API route should reside (optional)
- An API [resource name](https://restfulapi.net/resource-naming/)
- An API endpoint URL (the Java API URL to consume the backend data)
