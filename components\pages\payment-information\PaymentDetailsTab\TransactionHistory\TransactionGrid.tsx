import { clock, ContentGrid, ContentGridRow, download, Icon, info, outlineCheck } from "@eait-playerexp-cn/core-ui-kit";
import ToolTip from "../../../../ToolTip";
import NoTransactionFound from "./NoTransactionFound";
import React, { FC, ReactNode } from "react";
import {
  CreatorCode,
  DollarAmount,
  mapOpportunityTypeForAPI,
  PaymentsHistory,
  PaymentsHistoryWithCreatorCode,
  Transaction,
  TransactionWithCreatorCode
} from "@src/api/services/PaymentsService";
import Pagination from "../../../../dashboard/Pagination";
import classNames from "classnames";
import { TransactionHistoryLabelProps } from "../PaymentDetailsTab";
import { PaginationProps } from "../PaymentDetailsTab";
import Link from "next/link";
import CreatorCodeTooltip from "./CreatorCodeTooltip";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";

export type TransactionGridType = {
  paymentsHistory: PaymentsHistory | PaymentsHistoryWithCreatorCode;
  labels: TransactionHistoryLabelProps;
  isShowingPagination: boolean;
  paginationProps: PaginationProps | null;
  analytics: BrowserAnalytics;
};

export type TransactionHistoryDataProps = {
  opportunityImage: string;
  opportunityId: string;
  opportunityTitle: string;
  amount: string;
  contractLink: string;
  opportunityType: string;
  status: string;
  processedDate: Date | string;
  creatorCode?: CreatorCode;
  analytics: BrowserAnalytics;
};

const TransactionGrid: FC<TransactionGridType> = ({
  paymentsHistory,
  labels,
  isShowingPagination,
  paginationProps,
  analytics
}) => {
  const router = useRouter();

  return (
    <div
      className={classNames("payment-details-transaction-history-grid", {
        "no-records": !paymentsHistory.total,
        "with-pagination": isShowingPagination
      })}
    >
      <ContentGrid
        {...{
          headers: [
            {
              id: "description",
              title: labels.paymentGridDescription,
              style: { minWidth: "180px" },
              renderer: function TransactionDescription(currentRecord: ContentGridRow): ReactNode {
                const {
                  opportunityImage: imageLink,
                  opportunityTitle,
                  opportunityId,
                  opportunityType
                } = currentRecord as unknown as TransactionHistoryDataProps;
                return (
                  <div className="transaction-history-desc">
                    {imageLink && (
                      <img
                        className="transaction-history-desc-image"
                        src={imageLink as string}
                        alt={labels.opportunityImageLabel}
                      />
                    )}

                    {opportunityType === "support_a_creator" && opportunityTitle === "Affiliate Opportunity" ? (
                      <div className="transaction-history-desc-title"> {labels.opportunityTitle} </div>
                    ) : opportunityType === mapOpportunityTypeForAPI("sims_ugx_opportunity") ? (
                      <div title={opportunityTitle} className="transaction-history-desc-title">
                        {opportunityTitle.length > 20 ? `${opportunityTitle.slice(0, 20)}...` : opportunityTitle}
                      </div>
                    ) : (
                      <div className="transaction-history-desc-title transaction-history-desc-marketing-title">
                        <Link
                          href={`/opportunities/${opportunityId}`}
                          title={opportunityTitle}
                          onClick={() => {
                            analytics.clickedOpportunityDescription({ locale: router.locale });
                          }}
                        >
                          {opportunityTitle.length > 20 ? `${opportunityTitle.slice(0, 20)}...` : opportunityTitle}
                        </Link>
                      </div>
                    )}
                  </div>
                );
              }
            },
            {
              id: "opportunityType",
              title: labels.paymentGridType,
              style: { minWidth: "140px" },
              renderer: function TransactionType(currentRecord: ContentGridRow) {
                return (
                  <CreatorCodeTooltip
                    transaction={currentRecord as unknown as TransactionWithCreatorCode}
                    labels={{
                      typeCreatorCode: labels.typeCreatorCode,
                      typeOpportunity: labels.typeOpportunity,
                      simsMakerProgram: labels.simsMakerProgram
                    }}
                  />
                );
              }
            },
            {
              id: "status",
              title: labels.paymentGridStatus,
              style: { minWidth: "120px" },
              renderer: function TransactionStatus(currentRecord: ContentGridRow) {
                const { status } = currentRecord;
                const icon = (status as string).toLowerCase() === "pending" ? clock : outlineCheck;
                const classes =
                  (status as string).toLowerCase() !== "pending" ? "transaction-history-success-text" : "";
                const statusText =
                  (status as string).toLowerCase() === "pending" ? labels.statusPending : labels.statusProcessed;
                return (
                  <div className={classNames("transaction-grid-icontext-wrapper", classes)}>
                    <Icon icon={icon} className="transaction-grid-icontext-icon" />
                    <span className="transaction-grid-icontext-text">{statusText as string}</span>
                  </div>
                );
              }
            },
            {
              id: "amount",
              title: labels.paymentGridAmountDue,
              style: { minWidth: "130px", paddingRight: "0px" },
              renderer: (row: ContentGridRow) => (row.amount as unknown as DollarAmount).abbreviateOn("M").toString()
            },
            {
              id: "processedDate",
              title: (
                <div className="transaction-history-grid-header-date-icon">
                  <ToolTip {...{ overlay: labels.paymentGridDateHelp, directionForDesktop: "bottom" }}>
                    <Icon icon={info} />
                  </ToolTip>
                  <span>{labels.paymentGridDate}</span>
                </div>
              ),
              style: { minWidth: "100px" },
              renderer: function InvoiceDate(row: ContentGridRow) {
                return (
                  <span className="transaction-history-date-blank">
                    {(row as unknown as Transaction).processedDateLabel()}
                  </span>
                );
              }
            },
            {
              id: "contract",
              title: labels.paymentGridContract,
              align: "rtl",
              style: { minWidth: "76px" },
              renderer: function ContractLink(currentRecord: ContentGridRow) {
                const {
                  opportunityTitle,
                  contractLink = "",
                  opportunityType
                } = currentRecord as unknown as TransactionHistoryDataProps;
                return (
                  <>
                    {opportunityType === "support_a_creator" && opportunityTitle === "Affiliate Opportunity" ? (
                      <div data-testid="affiliate-contract" className="transaction-history-affiliate-contract">
                        -
                      </div>
                    ) : (
                      contractLink && (
                        <div className="transaction-history-contract">
                          <a
                            href={contractLink}
                            aria-label={labels?.downloadContractLabel || "-"}
                            className="transaction-history-contract-link"
                            download
                            target="_self"
                            onClick={() => {
                              analytics.downloadedPaymentContract({ locale: router.locale });
                            }}
                          >
                            <Icon icon={download} className="transaction-history-contract-icon" />
                          </a>
                        </div>
                      )
                    )}
                  </>
                );
              }
            }
          ],
          customSettings: {
            gridWidth: "",
            noDataFound: (
              <NoTransactionFound message={labels.noPayments}>
                <div className="no-data-found">
                  <span key="key-needed">{labels.noPaymentsDescription}</span>
                  <Link
                    href={`/opportunities`}
                    key="key-needed-2"
                    className="payment-details-link"
                    onClick={() => {
                      analytics.clickedPaidOpportunitiesWhenThereIsNoTransaction({ locale: router.locale });
                    }}
                  >
                    {labels.noPaymentsLink}
                  </Link>
                  .
                </div>
              </NoTransactionFound>
            )
          },
          rows: paymentsHistory.details as unknown as ContentGridRow[]
        }}
      />
      {isShowingPagination && <Pagination {...paginationProps} />}
    </div>
  );
};

export default TransactionGrid;
