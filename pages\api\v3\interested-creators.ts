import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import ApiContainer from "../../../src/ApiContainer";
import AddRequestToJoinController from "../../../src/controllers/AddRequestToJoinController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(AddRequestToJoinController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
