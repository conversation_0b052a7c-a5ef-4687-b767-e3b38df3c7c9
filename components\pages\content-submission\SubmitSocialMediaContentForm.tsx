import {
  Button,
  ConnectedAccountCard,
  facebookRounded,
  Input,
  instagram,
  MediaType,
  SocialMediaCard,
  tiktok,
  twitch,
  youTubeSocialIcon
} from "@eait-playerexp-cn/core-ui-kit";
import React, { FC, memo, useCallback, useEffect, useState } from "react";
import { Controller, Message, useFormContext, ValidationRule } from "react-hook-form";
import Form from "../../Form";
import {
  ButtonLabels,
  FormLabels,
  MediaLabelType,
  SocialMediaErrorLabels,
  SocialMediaSuccessLabels
} from "./SubmitSocialMediaContentModal";
import { onToastClose, SUCCESS, useAsync } from "../../../utils";
import SubmittedContentService from "../../../src/api/services/SubmittedContentService";
import { useAppContext } from "@src/context";
import { Toast, useToast } from "../../toast";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { AddContentFormLabels, SocialMediaGuideModalLabels } from "./ContentDeliverablesTab";
import SocialMediaGuideModal, { SocialMediaGuideModalProps } from "./SocialMediaGuideModal";
import { AxiosResponse } from "axios";
import { useDependency } from "@src/context/DependencyContext";

export type FormButtonsProps = {
  buttons: ButtonLabels;
  close: () => void;
  isPending: boolean;
  isSubmitClicked: boolean;
  updateCancelStatus: (e: boolean) => void;
};

export const FormButtons = memo(function Footer({
  buttons: { cancel, submit },
  close,
  isPending,
  isSubmitClicked,
  updateCancelStatus
}: FormButtonsProps) {
  const { formState } = useFormContext();
  const [isCancelDisabled, setCancelDisabled] = useState(false);

  useEffect(() => {
    if (Object.keys(formState.errors).length == 0 && formState.isValid === true && isSubmitClicked) {
      setCancelDisabled(true);
      updateCancelStatus(true);
    } else {
      setCancelDisabled(false);
      updateCancelStatus(false);
    }
  }, [formState, isSubmitClicked]);
  return (
    <div className="content-submission-social-media-footer">
      <Button variant="tertiary" onClick={close} size="md" dark disabled={isCancelDisabled}>
        {cancel}
      </Button>
      <Button
        type="submit"
        spinner={isPending}
        size="md"
        disabled={isPending || Object.keys(formState.errors).length !== 0 || formState.isValid === false}
      >
        {submit}
      </Button>
    </div>
  );
});

export type RequiredValidate = Required & {
  validate: (value: string) => string | boolean;
};

export type Required = {
  required: Message | ValidationRule<boolean>;
};

export type ContentSubmissionFormRules = {
  contentUrl: Required;
};

export type SocialMediaContentInputProps = {
  formLabels: {
    contentUrl: string;
    contentUrlPlaceholder: string;
  };
  infoLabel: string;
  mediaType: string;
  name: string;
  errorMessage: string;
  errorLabels: SocialMediaErrorLabels;
};

export const SocialMediaContentInput: FC<SocialMediaContentInputProps> = ({
  infoLabel,
  mediaType,
  name,
  formLabels,
  errorMessage,
  errorLabels
}) => {
  const validationLabels = {
    contentUrlRequired: errorLabels.contentUrlRequired
  };

  const rules: ContentSubmissionFormRules = {
    contentUrl: {
      required: validationLabels.contentUrlRequired
    }
  };

  const { control, setError, clearErrors } = useFormContext();

  useEffect(() => {
    if (errorMessage) setError(`url`, { type: "manual", message: errorMessage });
    else clearErrors(`url`);
  }, [errorMessage, setError]);

  return (
    <>
      <p className="content-submission-infolabel">{infoLabel}</p>
      <ConnectedAccountCard type={mediaType.toUpperCase() as MediaType} name={name} />
      <Controller
        name="url"
        control={control}
        rules={rules.contentUrl}
        render={({ field, fieldState: { error } }) => {
          return (
            <Input
              errorMessage={error?.message || ""}
              {...field}
              label={formLabels.contentUrl}
              placeholder={formLabels.contentUrlPlaceholder}
              id="url"
              type="url"
            />
          );
        }}
      />
    </>
  );
};

export type SubmitSocialMediaContentFormProps = {
  setUpdatedContent: (boolean) => void;
  mediaType: MediaLabelType;
  formLabels: FormLabels;
  infoLabel: string;
  name: string;
  buttonLabels: ButtonLabels;
  onClose: () => void;
  errorLabels: SocialMediaErrorLabels;
  participationId: string;
  creatorId: string;
  successLabels: SocialMediaSuccessLabels;
  analytics: BrowserAnalytics;
  locale: string;
  opportunity: OpportunityWithDeliverables;
  updateCancelStatus: (e: boolean) => void;
  addContentFormLabels: AddContentFormLabels;
  facebookGuideModalLabels: SocialMediaGuideModalLabels;
  youtubeGuideModalLabels: SocialMediaGuideModalLabels;
  instagramGuideModalLabels: SocialMediaGuideModalLabels;
  twitchGuideModalLabels: SocialMediaGuideModalLabels;
  tiktokGuideModalLabels: SocialMediaGuideModalLabels;
  accountId: string;
  deliverableId?: string;
  deliverableTitle?: string;
  deliverableType?: string;
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED?: boolean;
};

const SubmitSocialMediaContentForm: FC<SubmitSocialMediaContentFormProps> = ({
  setUpdatedContent,
  mediaType,
  formLabels,
  buttonLabels,
  onClose,
  infoLabel,
  name,
  errorLabels,
  participationId,
  creatorId,
  successLabels,
  analytics,
  locale,
  opportunity,
  updateCancelStatus,
  addContentFormLabels,
  facebookGuideModalLabels,
  youtubeGuideModalLabels,
  instagramGuideModalLabels,
  twitchGuideModalLabels,
  tiktokGuideModalLabels,
  accountId,
  deliverableId,
  deliverableTitle,
  deliverableType,
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED
}) => {
  const { errorHandler } = useDependency();
  //Added this for showing error messages
  const [errorMessage, setErrorMessage] = useState("");
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [socialLabels, setSocialLabels] = useState<SocialMediaGuideModalProps>(null);

  const { dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const { success: successToast } = useToast();

  const modalLabels = {
    facebook: facebookGuideModalLabels,
    instagram: instagramGuideModalLabels,
    twitch: twitchGuideModalLabels,
    youtube: youtubeGuideModalLabels,
    tiktok: tiktokGuideModalLabels
  };

  const handleSocialMediaIcon = (type: string) => {
    setShowHelpModal(true);
    setSocialLabels({ ...modalLabels[type], closeLabel: buttonLabels.close });
  };

  const socialHelpBanner = {
    Facebook: {
      label: addContentFormLabels.facebook,
      icon: facebookRounded,
      onClick: () => handleSocialMediaIcon("facebook")
    },
    Instagram: {
      label: addContentFormLabels.instagram,
      icon: instagram,
      onClick: () => handleSocialMediaIcon("instagram")
    },
    YouTube: {
      label: addContentFormLabels.youTube,
      icon: youTubeSocialIcon,
      onClick: () => handleSocialMediaIcon("youtube")
    },
    Twitch: {
      label: addContentFormLabels.twitch,
      icon: twitch,
      onClick: () => handleSocialMediaIcon("twitch")
    },
    TikTok: {
      label: addContentFormLabels.tiktok,
      icon: tiktok,
      onClick: () => handleSocialMediaIcon("tiktok")
    }
  };

  const getAPIErrors = (response) => {
    let errorMessage = errorLabels.genericContentError;
    switch (response.code) {
      case "submit-social-media-content-unrelated-account":
      case "submit-social-media-content-unknown-connected-account":
        errorMessage = errorLabels.urlNotFromConnectedChannel;
        break;
      case "submit-social-media-content-unknown-submitted-video":
        errorMessage = errorLabels.videoNotFromChannel;
        break;
      /** After confirmation with Sky reg error message, will update it.
       * Commenting temporarily and show generic error message "Please enter a valid URL" for this
       */
      // case "submit-social-media-content-unsupported-content":
      //   errorMessage = errorLabels.unsupportedContentError;
      //   break;
      case "submit-social-media-content-unknown-instagram-video":
      case "submit-social-media-content-unknown-instagram-media":
        errorMessage = errorLabels.instagramErrorUrl;
        break;
      case "submit-social-media-content-duplicate-submitted-content":
        errorMessage = errorLabels.duplicateUrl;
        break;
      case "submit-social-media-content-unknown-tik-tok-video":
        errorMessage = errorLabels.unknownTikTokVideo;
        break;
      case "submit-social-media-content-invalid-tik-tok-submission":
      case "submit-social-media-content-invalid-facebook-submission":
        errorMessage = errorLabels.invalidSocialSubmission;
        break;
      case "submit-social-media-content-account-authorization-failure":
        errorMessage = errorLabels.accountAuthorizationFailure;
        break;
      case "submit-social-media-content-cannot-expand-url":
        errorMessage = errorLabels.cannotExpandUrl;
        break;
      case "submit-social-media-content-invalid-facebook-page":
        errorMessage = errorLabels.invalidFacebookPage;
        break;
      case "submit-social-media-content-unsupported-instagram-media-type":
        errorMessage = FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED
          ? errorLabels.unSupportedContentTypeForMedia
          : errorLabels.unSupportedContentType;
        break;
      case "validate-content-urls-invalid-input":
      case "submit-social-media-content-invalid-input":
        errorMessage = errorLabels.cannotSubmitContentInvalidInput;
        break;
      case "submit-social-media-content-duplicate-scanned-content":
        errorMessage = errorLabels.duplicateScannedContentUrl;
        break;
      case "submit-social-media-content-unsupported-content":
      default:
        break;
    }
    return errorMessage;
  };

  const submitContent = async (socialMediaContentPayload) => {
    try {
      // calls V5 when FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED enabled.
      // calls V4 when FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED disabled.
      const submitSocialContentService = FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED
        ? SubmittedContentService.submitSocialMediaContentWithInstagramMeadia
        : SubmittedContentService.submitSocialMediaContents;
      await submitSocialContentService(socialMediaContentPayload);
      analytics.submittedSocialContent({
        locale,
        opportunity,
        accountType: mediaType,
        deliverableTitle,
        deliverableType
      });
      setUpdatedContent(true);
      onClose();
      successToast(
        <Toast
          header={successLabels.title}
          content={successLabels.content}
          closeButtonAriaLabel={buttonLabels.close}
        />,
        {
          onClose: () => onToastClose(SUCCESS, dispatch)
        }
      );
    } catch (e) {
      if (e.response?.data?.status === 409 || e.response?.data?.status === 422) {
        setErrorMessage(getAPIErrors(e.response?.data));
        setSubmitClicked(false);
        analytics.receivedContentSubmissionErrorMessage({
          locale,
          opportunity,
          deliverableTitle,
          deliverableType,
          errorMessage: e?.response?.data?.message || e?.response?.data?.detail || e?.message || "No message",
          errorCode: e?.response?.status || "No code",
          submissionType: "SOCIAL"
        });
        return;
      }
      errorHandler(stableDispatch, e);
    }
  };

  const scanUrl = async (url) => {
    try {
      const res = (await SubmittedContentService.validateContent({ urls: [url] }, "CREATORS")) as AxiosResponse;
      const result = res.data.results[0];
      if (result.isSecure === false) {
        setErrorMessage(errorLabels.unsafeUrlError);
        setSubmitClicked(false);
        return;
      } else {
        /** payload for v3 of submit social media content api with deliverableId added in the socialMediaTiktokContentPayload's(v2) payload */
        const socialMediaTiktokContentPayload = {
          participationId,
          socialMediaContent: {
            creatorId,
            contentUrl: result.url,
            accountId,
            deliverableId
          }
        };

        await submitContent(socialMediaTiktokContentPayload);
      }
    } catch (e) {
      if (e.response?.data?.status === 422) {
        const validationError = getAPIErrors(e.response?.data);
        setErrorMessage(validationError);
        setSubmitClicked(false);
        analytics.receivedContentSubmissionErrorMessage({
          locale,
          opportunity,
          deliverableTitle,
          deliverableType,
          errorMessage: validationError,
          errorCode: e?.response?.status || "No code",
          submissionType: "SOCIAL"
        });
        return;
      }
      errorHandler(stableDispatch, e);
    }
  };
  const [isSubmitClicked, setSubmitClicked] = useState(false);
  const onSubmit = useCallback(async (data) => {
    setErrorMessage("");
    setSubmitClicked(true);
    const { url } = data || {};
    await scanUrl(url);
  }, []);
  const { pending: isPending, execute: onSubmitSocialMediaContent } = useAsync(onSubmit, false);

  const closeSocialMediaGuideModal = useCallback(() => {
    setShowHelpModal(false);
  }, []);

  return (
    <Form key="social-media" onSubmit={onSubmitSocialMediaContent} defaultValues={{}} mode="onChange">
      <SocialMediaContentInput
        {...{
          infoLabel,
          mediaType,
          name,
          formLabels,
          errorMessage,
          setErrorMessage,
          errorLabels,
          FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED
        }}
      />
      <FormButtons {...{ buttons: buttonLabels, close: onClose, isPending, isSubmitClicked, updateCancelStatus }} />
      <SocialMediaCard
        helpMessage={addContentFormLabels.accountInformation1}
        information={addContentFormLabels.clickTheIcon}
        buttons={[socialHelpBanner[mediaType]]}
      />
      {showHelpModal && <SocialMediaGuideModal {...{ ...socialLabels, onClose: closeSocialMediaGuideModal }} />}
    </Form>
  );
};

export default SubmitSocialMediaContentForm;
