import React, { memo, useMemo } from "react";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { close, Icon, leftArrow, Stepper } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames/bind";
import { useRouter } from "next/router";
import labelsCommon from "../../config/translations/common";
import EaLogo from "../icons/EaLogo";
import { useDetectScreen } from "../../utils";
import { USER_NAVIGATED } from "../../utils";

export const BackButton = ({ back, isFirstStep, onGoBack }) => {
  return (
    <>
      {!isFirstStep && (
        <button
          className={classNames({ "display-back-bt": !isFirstStep, "hide-back-bt": isFirstStep })}
          onClick={onGoBack}
        >
          <Icon icon={leftArrow} className="breadcrumb-nav" />
          <span>{back}</span>
        </button>
      )}
    </>
  );
};

export default memo(function Header({
  onClose,
  steps,
  currentPage,
  buttons,
  isRegistrationFlow,
  onGoBack,
  stableDispatch,
  setShowMigration,
  setNavigateToPage,
  completedLabel
}) {
  const { t } = useTranslation(["common"]);
  const { layout } = useMemo(() => {
    return {
      layout: labelsCommon(t)
    };
  }, [t]);
  const router = useRouter();

  const stepPos = steps?.findIndex(({ title }) => title === currentPage?.title);

  const handleStepperNavigation = (href) => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    if (setShowMigration) {
      /** Show the confirmation modal even with stepper navigation */
      setShowMigration(true);
      setNavigateToPage(href);
    } else {
      router.push(href);
      stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    }
  };

  const isMobileOrTab = useDetectScreen(1279);

  return (
    <>
      <div className="mg-header-container">
        <div className="mg-header">
          {/** This back button will be visible only in mobile & tablet views */}
          {isMobileOrTab && (
            <div className="mg-header-back">
              <BackButton back={buttons.back} isFirstStep={stepPos === 0} onGoBack={onGoBack} />
            </div>
          )}
          <div className="mg-header-logo">
            <Link href="/">
              <Icon icon={EaLogo} width="3rem" height="3rem" />
              <span>{layout.header.creatorNetwork}</span>
            </Link>
          </div>
          <div className="mg-header-close">
            <button onClick={onClose} aria-label={layout.buttons.closeHeader}>
              <Icon icon={close} />
            </button>
          </div>
        </div>
        <div className="stepper-back">
          <BackButton
            back={buttons.back}
            isFirstStep={stepPos === 0}
            isRegistrationFlow={isRegistrationFlow}
            onGoBack={onGoBack}
          />
        </div>
        <Stepper
          {...{
            steps,
            current: currentPage?.title,
            handleNavigate: handleStepperNavigation,
            completedLabel: completedLabel
          }}
        />
      </div>
    </>
  );
});

BackButton.defaultProps = {
  isRegistrationFlow: false,
  onGoBack: null
};
