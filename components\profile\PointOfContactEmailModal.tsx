import React, { FC, memo, Mouse<PERSON>vent<PERSON><PERSON><PERSON>, MutableRefObject, useC<PERSON>back, useMemo, useRef } from "react";
import {
  Button,
  Input,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import Form from "../Form";
import { Controller, useFormContext } from "react-hook-form";
import Textarea from "@components/textarea/TextArea";
import CreatorsService from "../../src/api/services/CreatorsService";
import { useAppContext } from "../../src/context";
import { onToastClose, SUCCESS, useAsync } from "../../utils";
import { Toast, useToast } from "../toast";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { CreatorProps, PocLabels } from "./PointOfContactDetails";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import OpportunityService from "../../src/api/services/OpportunityService";
import { useDependency } from "@src/context/DependencyContext";

type FooterProps = {
  pocLabels: PocLabels;
  pending: boolean;
  closeModal: MouseEventHandler<HTMLButtonElement>;
  cancelButtonRef?: MutableRefObject<HTMLButtonElement | null>;
};

type PointOfContactEmailModalProps = {
  pocLabels: PocLabels;
  pocName: string;
  analytics: BrowserAnalytics;
  buttons: { close: string };
  creator?: CreatorProps;
  opportunityId?: string;
  closeModal: () => void;
};

const SendEmailToPointOfContactInput: FC<PocLabels> = memo(function SendEmailToPointOfContactInput({ ...pocLabels }) {
  const { control } = useFormContext();
  const rules = useMemo(() => {
    return {
      subject: {
        required: pocLabels.messages.subject,
        maxLength: { value: 255, message: pocLabels.messages.subjectTooLong }
      },
      body: {
        required: pocLabels.messages.body,
        maxLength: { value: 32000, message: pocLabels.messages.bodyTooLong }
      }
    };
  }, [pocLabels]);

  return (
    <>
      <Controller
        control={control}
        name="subject"
        rules={rules.subject}
        render={({ field, fieldState: { error } }) => (
          <Input
            errorMessage={(error && error.message) || ""}
            {...field}
            type="text"
            label={pocLabels.labels.subject}
            placeholder={pocLabels.labels.subject}
            id="subject"
          />
        )}
      />

      <Controller
        control={control}
        name="body"
        rules={rules.body}
        render={({ field, fieldState: { error } }) => (
          <Textarea
            errorMessage={(error && error.message) || ""}
            {...field}
            label={pocLabels.labels.body}
            placeholder={pocLabels.labels.body}
          />
        )}
      />
    </>
  );
});

const FooterButtons: FC<FooterProps> = memo(function FooterButtons({
  closeModal,
  pocLabels,
  pending,
  cancelButtonRef
}) {
  const { formState } = useFormContext();
  return (
    <>
      <Button variant="tertiary" dark size="sm" onClick={closeModal} disabled={pending} ref={cancelButtonRef}>
        {pocLabels.buttons.cancel}
      </Button>
      <Button
        size="sm"
        type="submit"
        spinner={pending}
        disabled={Object.keys(formState.errors).length !== 0 || formState.isValid === false || pending}
      >
        {pocLabels.buttons.send}
      </Button>
    </>
  );
});

export const PointOfContactEmailModal: FC<PointOfContactEmailModalProps> = ({
  pocLabels,
  pocName = "",
  analytics,
  buttons,
  opportunityId,
  creator,
  closeModal
}) => {
  const {
    errorHandler,
    configuration: { FLAG_SEND_EMAIL_WITH_PROGRAM }
  } = useDependency();
  const { t } = useTranslation(["point-of-contact"]);
  const { dispatch } = useAppContext() || {};
  const stableDispatch: (state, action?) => void = useCallback(dispatch, []);
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  const router = useRouter();
  const { success: successToast } = useToast();

  const submitHandler = useCallback(
    async (data) => {
      try {
        if (opportunityId) {
          if (FLAG_SEND_EMAIL_WITH_PROGRAM) {
            await OpportunityService.sendEmailToOpportunityPocWithProgram({
              ...data,
              creatorId: creator.id,
              opportunityId
            });
          } else {
            await OpportunityService.sendEmailToOpportunityPOC({
              ...data,
              creatorId: creator.id,
              opportunityId
            });
          }
        } else {
          if (FLAG_SEND_EMAIL_WITH_PROGRAM) {
            await OpportunityService.sendEmailToOpportunityPocWithProgram({
              ...data,
              creatorId: creator.id
            });
          } else {
            await CreatorsService.sendEmailToPOC(data);
          }
        }
        analytics.emailSentToPoc({ locale: router.locale });
        closeModal();
        successToast(
          <Toast
            header={pocLabels.success.modalHeader}
            content={t("point-of-contact:success.modalMessage", {
              ...{ defaultGamerTag: pocName }
            })}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            onClose: () => onToastClose(SUCCESS, stableDispatch)
          }
        );
      } catch (e) {
        closeModal();
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch]
  );

  const { pending, execute: sendEmail } = useAsync(submitHandler, false);

  return (
    <ModalV2 closeButtonRef={cancelButtonRef}>
      <ModalHeader>
        <ModalTitle>{pocLabels.title}</ModalTitle>
        <ModalCloseButton
          ariaLabel={buttons.close}
          closeButtonRef={cancelButtonRef}
          disabled={pending}
        ></ModalCloseButton>
      </ModalHeader>
      <div className="poc-email-container">
        <Form mode="onChange" onSubmit={sendEmail}>
          <ModalBody>
            <SendEmailToPointOfContactInput {...pocLabels} />
          </ModalBody>
          <ModalFooter>
            <FooterButtons {...{ pocLabels, closeModal, pending, cancelButtonRef }} />
          </ModalFooter>
        </Form>
      </div>
    </ModalV2>
  );
};

export default PointOfContactEmailModal;
