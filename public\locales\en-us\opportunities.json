{"description": "Explore and join the different opportunities we have available right now. We’re always adding something new so be sure to check back often.", "franchisePreferences": "Franchise Preferences", "returnToOpportunities": "Back", "details": "Details", "gameTitle": "Game Title", "location": "Location", "eventDate": "Event Date", "registration": "Registration", "registrationStartDate": "Registration Start Date and Time", "registrationEndDate": "Registration End Date and Time", "signUp": "Sign Up", "whatIsIt": "What is it?", "opportunityDescription": "Description", "aboutThisOpportunity": "About this Opportunity", "viewAll": "View all", "thingsToDo": "Things to do", "freeGameCode": "Free Game Code", "freeGameCodeDesc": "This game code is available on the following platforms. Please note you will receive the code immediately after joining:", "thingsToDo1": "Sign up if you're interested", "thingsToDo2": "Review and agree to Opportunity terms", "thingsToDo3": "Submit content", "thingsToDo4": "Attend Event", "join": "Join", "joinOpportunity": "Join <PERSON>", "thanksMessage": "Thanks! You’re registered.", "criteria": "Criteria", "gameCode": "Game Code", "contentGuidelines": "Content Submission Requirements", "downloadEaLogo": "Download Electronic Arts Watermark", "joined": "Joined", "completed": "Completed", "past": "Past", "open": "Open", "invited": "Invited", "remote": "Online Event", "inPersonEvent": "In-Person Event", "grabACodeEvent": "Grab a code", "paid": "$ Paid", "contentSubmission": "Content Submission", "contentDeliverables": "Deliverables", "event": "Event", "goToDiscord": "Go to Discord", "getGameCode": "Your Game Code", "gameCodeNotReady": "Your game code is not ready just yet!", "gameCodeInfo": "Your code will appear on the opportunity page. Please check back soon", "contentSubmissionSubtitle": "Your Content Submissions", "submissionWindow": "Submission Window:", "submitContent": "Submit Content", "notSubmittedContent": "You haven't submitted content.", "additionalInfo": "Additional Information", "discordChannel": "Discord Channel", "resources": "Resources", "youAreRegistered": "You're registered for this opportunity.", "pointOfContact": "Point of Contact", "searchForOpportunites": "Search for Opportunities", "searchResults": "Search Results for", "apply": "Apply", "perks": "Perks", "filteredBy": "Filtered by", "submitYourContent": "Submit Your Content", "submitYourContentDesc": "As part of the agreement for EA Creator Network, you are required to provide all of the following disclaimers:", "guideLinePoint1": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.", "guideLinePoint2": "Prominently include “Sponsored by EA” at the beginning of the video description/More info tab and a link in the description", "guideLinePoint3": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to Electronic Arts for Sponsoring this video” or similar", "guideLinePoint4": "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion” and Instagram’s “Partnership” options.", "guideLinePoint5": "Include the ‘Presented by EA Creator Network’ watermark in the first 5 seconds of your video or stream.", "guideLinePoint6": "Prominently include “Presented by EA Creator Network” at the beginning of the video description/More info tab.", "guideLinePoint7": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to Electronic Arts for inviting me to this early access event” (or similar)", "contentGuideSubDesc": "If you have any questions please reach out to your community manager.", "addAnother": "Add Another", "title": "Submit Content", "urlPlaceholder": "Please enter the Content URL:", "duplicateUrl": "This link has already been submitted. Duplicate videos are not permitted.", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "This video is not from a social channel connected to your CN account.", "youtubeVideoError": "No Youtube video found with given id.", "genericContentError": "Please enter a valid url.", "noAvailableOpportunities": "No available opportunities", "noAvailableOpportunitiesDesc": "There are no opportunities available at this time.", "noAvailableSearchResults": "No Opportunities Found", "noAvailableSearchResultsDesc": "Check your spelling, try different keywords or adjust your filter options.", "Back": "Back", "overview": "Overview", "perksSubtitle": "What you'll get for joining", "registrationCloses": "Registration Closes:", "submissionCloses": "Submission Closes:", "submissionOpens": "Submission Opens:", "registrationEnds": "Registration Ends", "submissionClosedMessage": "The submission window for this opportunity has closed.", "gameCodePending": "Game Code Pending. Check Back Soon", "opportunityType": "Opportunity Type", "more": "More", "gameCodeTitle": "Your Game Code", "copyGameCode": "Copy Game Code", "copied": "<PERSON>pied", "submitContentText": "Use this section to submit content to this opportunity.", "submitWindowClosedText": "The content submission window for this opportunity is closed and no new content can be submitted.", "collab": "Collab", "dailyAllowance": "Daily Allowance", "designCouncil": "Design Council", "earlyAccess": "Early Access", "exclusivePreview": "Exclusive Preview", "food": "Food", "freeGameCodePerk": "Free Game Code", "hotel": "Hotel", "paidPerk": "Paid", "privateDiscordChannel": "Private Discord", "swag": "Swag", "travel": "Travel", "vipEvent": "VIP Event", "creatorCodePerk": "Creator Code", "supportACreator": "Support-a-Creator", "whatToDoTitle": "What to do", "toDoTitle": "To Do", "whatToDoDescription": "If you're interested in joining this opportunity, here is what you'll need to do:", "whatToDoLabels": {"joinOpportunity": "Join the Opportunity", "getGameCode": "Get Game Code", "signContract": "Sign Opportunity Legal Contract", "getPaid": "Get Paid", "attendInPersonEvent": "Attend Event", "attendOnlineEvent": "Attend Online Event", "makeContent": "Make Content", "submitContent": "Submit Content", "getCreatorCode": "Get Creator Code"}, "decline": "Decline", "declineModalTitle": "Decline Invitation", "declineModalContent": "Are you sure you want to decline the invitation? You won’t be able to join this opportunity if you change your mind.", "declinedText": "You’ve declined this opportunity.", "invitedText": "You’ve been invited to join", "declined": "Declined", "contentGuidelinesTitle": "Content Guidelines", "creatorCode": {"title": "Your Creator Code", "copyLabel": "Copy", "copyText": "Copy Creator Code", "textCopied": "<PERSON>pied", "codeNotYetActiveMessage": "Your Creator Code for this opportunity is not yet active.", "codeExpiredMessage": "Your Creator Code for this opportunity is no longer active.", "codeActivationStartTime": "Code Activation Start Time", "codeActivationEndTime": "Code Activation End Time"}, "remoteEvent": {"title": "Online Event Details", "eventTime": "Event Time", "description": "This event is over.", "joinEvent": "Join Event", "noPasswordRequired": "No Password Required", "noEventDetailsAdded": "No event details have been added yet", "NoMeetingLinkInfo": "Contact Community Manager for event details."}, "exitJoinOpportunityFlow": "Exit join opportunity flow", "supportACreatorDisclosure": {"title": "Disclosure", "subTitle": "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:", "description1": "Include audible and written disclosure to let viewers know that using your Creator Code directly financially supports you. “Using my Creator Code directly financially supports me.” (or similar).", "description2": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.", "description3": "Prominently include “Sponsored by EA” “Sponsored”, or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #SponsoredbyEA, #Sponsored, or #Ad are also acceptable.", "description4": "<strong>Creators in UK Only:</strong> Ad and #Ad are the only acceptable disclosures.", "description5": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for sponsoring this video.” (or similar).", "description6": "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion,” and Instagram’s “Partnership” options, and Twitch’s Branded Content options."}, "pocLabels": {"opportunityContact": "Opportunity Contact", "pointOfContact": "Point Of Contact", "email": "Email"}, "registrationWindow": "Registration Window", "contentSubmissionWindow": "Submission Window", "closed": "Closed", "activationWindow": "Code Window", "keyDates": "Key Dates", "inPersonStartTime": "In-Person Event Start Time:", "inPersonEndTime": "In-Person Event End Time:", "onlineEventStartTime": "Online Event Start Time:", "onlineEventEndTime": "Online Event End Time:", "contentSubmissionDeadline": "Content Submission Deadline:", "viewDeliverables": "View Deliverables", "codeActivationStartTime": "Code Activation Start Time:", "codeActivationEndTime": "Code Activation End Time:", "checkBackSoon": "Check back soon", "payment": "Payment", "sales": "% of Sales", "eventDetails": {"eventStartTime": "Event Start Time", "eventEndTime": "Event End Time", "eventLocation": "Event Location", "password": "Password", "info": "Info", "copyAddress": "Copy address", "copyPassword": "Copy password"}, "eventInformation": "Event Information"}