.payments-details {
  @apply border-t border-white border-opacity-30;
}

.payment-details-banner {
  @apply mt-meas12;
}
.payment-details-banner .banner-wrapper {
  @apply items-start;
}
.payment-details-heading {
  @apply font-text-bold leading-6 xs:text-mobile-body-default md:text-[24px] md:text-tablet-body-default md:leading-7 md:tracking-[1px] lg:text-desktop-body-default;
}

.payment-details-overview-heading {
  @apply mb-meas9 mt-meas16 md:mb-meas8 md:mt-[30px];
}

.payment-details-date-range {
  @apply xs:text-mobile-body-small md:text-tablet-body-default lg:text-desktop-body-default;
}

.payment-details-amounts-wrapper {
  @apply my-meas12 flex flex-col md:flex-row;
}

.payment-details-amounts-section {
  @apply flex-1;
}
.payment-details-amounts-section .amount-card-wrapper {
  @apply mb-meas0;
}
.payment-details-amounts-section .amount-card-wrapper.amount-card-paid {
  @apply mb-[15px] md:mb-meas0;
}
.payment-details-amounts-section .amount-card-wrapper.disabled {
  @apply opacity-50;
}
.payment-details-amounts-section .amount-card-medium-text {
  @apply leading-[26px] xl:leading-10;
}

.payment-details-amounts-section.pending-data {
  @apply md:ml-[25px];
}

.payment-details-transaction-heading {
  @apply mb-[10px] mt-meas12 md:mt-[30px] lg:mt-[34px];
}

.payment-details-transaction-history-text {
  @apply mb-meas12 xs:text-mobile-body-small md:text-tablet-body-default md:leading-6;
}

.payment-details-link {
  @apply cursor-pointer underline;
}

.payment-details-transaction-history-grid .content-grid-last-record {
  @apply border-b-[1px];
}

.payment-details-transaction-history-grid .content-grid-last-record td {
  @apply pb-meas8;
}

.payment-details-transaction-history-grid .pagination-container {
  @apply my-auto mb-[22px] mt-[6px];
}

.payment-details-transaction-history-grid.no-records .content-grid-head {
  @apply md:hidden;
}
.payment-details-transaction-history-grid.no-records .content-grid-wrapper {
  @apply md:w-full;
}
.payment-details-transaction-history-grid.no-records .transaction-grid-no-data-section {
  @apply md:pb-meas8;
}

.payment-details-transaction-history-wrapper .transaction-record-opportunityTitle {
  @apply mb-meas4;
}

.payment-details-transaction-history-wrapper .pagination-container {
  @apply my-auto mb-meas34 mt-meas24;
}

.payment-details-overview-header {
  @apply inline-flex w-full items-center justify-between;
}

.payments-filter-selected-item {
  @apply mr-[6px] mt-meas2 inline whitespace-nowrap rounded-[100px] bg-gray-10 pl-meas4 pr-meas4 text-gray-90 xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}

.payments-filter-selected-item-remove {
  @apply ml-meas6 cursor-pointer text-gray-60;
}

.payments-filter-selected-item-remove-icon {
  @apply mb-[2px] inline h-meas7 w-meas7 cursor-pointer;
}

.payment-details-filters {
  @apply mt-meas2 pb-[10px] xs:block md:inline-flex;
}

.payments-filter-title {
  @apply mr-meas2;
}

.transaction-record-opportunity-title:has(> .record-opportunity-title) {
  @apply no-underline;
}

/* Adding for bug fix. This will be removed once we implement new calendar component. This will go into core-ui-kit calendar css */
* {
  font-variant-ligatures: none;
}
