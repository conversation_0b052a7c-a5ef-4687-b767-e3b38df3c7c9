import "reflect-metadata";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import React, { useMemo } from "react";
import Footer from "@components/footer/ProgramFooter";
import Header from "@components/header/Header";
import RedirectException from "@src/utils/RedirectException";
import config from "../config";
import flags from "../utils/feature-flags";
import labelsCommon from "@config/translations/common";
import labelsIndex from "@config/translations/index";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import withRegisteredUser from "@src/utils/WithRegisteredUser";
import withUnregisteredUser from "@src/utils/WithUnregisteredUser";
import withUserSession from "@src/utils/WithUserSession";
import HomePage, { CreatorTypeFallBackProps } from "@components/pages/index/HomePage";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import { GetServerSidePropsResult } from "next";
import homeProps from "@src/serverprops/HomeProps";
import { useDependency } from "@src/context/DependencyContext";

export type HomeProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  interestedCreator: boolean;
  creatorTypesFallback: CreatorTypeFallBackProps[];
  FLAG_NEW_FOOTER_ENABLED?: boolean;
};

export default function Home({ user, creatorTypesFallback, interestedCreator, FLAG_NEW_FOOTER_ENABLED }: HomeProps) {
  const { analytics } = useDependency();
  const router = useRouter();
  const { t } = useTranslation(["common", "index", "notifications", "connect-accounts", "opportunities"]);
  const { hero, creator, howItWorks, startCreating, ebonix, layout, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    return { ...labelsIndex(t), layout: labelsCommon(t), notificationsLabels: notificationBellLabels };
  }, [t]);

  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const labels = {
    hero,
    creator,
    howItWorks,
    startCreating,
    ebonix,
    layout
  };

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.home}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={interestedCreator}
          FLAG_NEW_NAVIGATION_ENABLED={false}
        />
      </LayoutHeader>
      <LayoutBody>
        <HomePage interestedCreator creatorTypesFallback={creatorTypesFallback} labels={labels} />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={router.locale}
          labels={layout.footer}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(homeProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<HomeProps>;
  }

  let user;
  try {
    user = await withUserSession(req, res);
    withRegisteredUser(req, locale, user);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user ? AuthenticatedUserFactory.fromSession(user, flags.isCreatorsAPIWithProgram()) : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      creatorTypesFallback: config.FALLBACK_CREATOR_TYPES,
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      ...(await serverSideTranslations(locale, [
        "common",
        "index",
        "creator-type",
        "opportunities-rewards",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled()
    }
  };
};
