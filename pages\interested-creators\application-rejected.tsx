import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsApplicationRejected from "../../config/translations/application-rejected";
import labelsCommon from "../../config/translations/common";
import flags from "../../utils/feature-flags";
import { ApplicationRejectedPage } from "@components/pages/interested-creators/ApplicationRejectedPage";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import InterestedCreatorApplicationStatus from "../../src/interestedCreators/InterestedCreatorApplicationStatus";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationRejectedPageProps from "@src/serverprops/ApplicationRejectedPageProps";
import { useDependency } from "@src/context/DependencyContext";

export type ApplicationRejectedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  locale: string;
  showInitialMessage?: boolean;
  application: InterestedCreatorApplicationStatus;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

export default function Rejected({
  locale,
  application,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: ApplicationRejectedProps): JSX.Element {
  const { analytics } = useDependency();
  const { t } = useTranslation(["common", "application-rejected"]);
  const { applicationRejectedLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);

    return {
      applicationRejectedLabels: {
        creatorNetwork,
        close,
        ...labelsApplicationRejected(t, application.canResubmitRequestDate)
      }
    };
  }, [t]);

  const { creatorNetwork, close } = applicationRejectedLabels;

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <ApplicationRejectedPage
            applicationRejectedLabels={applicationRejectedLabels}
            locale={locale}
            analytics={analytics}
            defaultGamerTag={application?.gamerTag}
            emailId={application?.email}
            canApply={application?.canApply}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={application.createdDate as unknown as string}
            reSubmitRequestDate={application.canResubmitRequestDate as unknown as string}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationRejectedPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationRejectedProps>;
  }

  const application = await withCreatorApplication(req, res, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isRejected()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      ...(await serverSideTranslations(locale, ["common", "application-rejected"])),
      application: { ...application },
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
