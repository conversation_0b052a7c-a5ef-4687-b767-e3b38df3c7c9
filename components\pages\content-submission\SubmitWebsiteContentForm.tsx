import React, { <PERSON>, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import Form from "../../Form";
import { Controller, useFormContext } from "react-hook-form";
import { Content, Input, Select } from "@eait-playerexp-cn/core-ui-kit";
import { Message, ValidationRule } from "react-hook-form";
import { isObj, isString, onToastClose, SUCCESS, useAsync } from "../../../utils";
import SubmittedContentService from "../../../src/api/services/SubmittedContentService";
import { useAppContext } from "@src/context";
import { Toast, useToast } from "../../toast";
import { ButtonLabels, ContentSubmissionLabels, ContentTypes, FormLabels } from "./SubmitWebsiteContentModal";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { AxiosResponse } from "axios";
import { useDependency } from "@src/context/DependencyContext";

export type Required = {
  required: Message | ValidationRule<boolean>;
};
export type Validate = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  validate: (value: any) => any;
};
export type Pattern = {
  value: RegExp;
  message: string;
};
export type RequiredValidate = Required & Validate;

export type ContentSubmissionFormRules = {
  contentTitle: Validate;
  contentUrl: RequiredValidate;
  contentType: RequiredValidate;
};

export type SubmitWebsiteContentFormProps = {
  setUpdatedContent: (boolean) => void;
  onClose: () => void;
  formLabels: FormLabels;
  contentTypes: ContentTypes;
  buttonsLabels: ButtonLabels;
  contentSubmissionLabels: ContentSubmissionLabels;
  participationId: string;
  thumbnail: string;
  YOUTUBE_HOSTS: string[];
  TWITCH_HOSTS: string[];
  INSTAGRAM_HOSTS: string[];
  FACEBOOK_HOSTS: string[];
  TIKTOK_HOSTS: string[];
  analytics: BrowserAnalytics;
  opportunity: OpportunityWithDeliverables;
  locale: string;
  updateCancelStatus: (e: boolean) => void;
  enableUpdateWebsite?: boolean;
  content?: Content;
  deliverableId?: string;
  deliverableTitle?: string;
  deliverableType?: string;
};

export type FormButtonsProps = {
  buttons: {
    cancel: string;
    submit: string;
  };
  close: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  isSubmitClicked: boolean;
  updateCancelStatus: (e: boolean) => void;
};

type ScanResult = {
  url: string;
  isSecure: boolean;
};

export type WebsiteContentInputProps = {
  formLabels: FormLabels;
  contentTypes: ContentTypes;
  failedUrl: ScanResult | null;
  duplicateUrl: boolean;
  contentSubmissionLabels: ContentSubmissionLabels;
  YOUTUBE_HOSTS: string[];
  TWITCH_HOSTS: string[];
  INSTAGRAM_HOSTS: string[];
  FACEBOOK_HOSTS: string[];
  TIKTOK_HOSTS: string[];
  invalidUrl: boolean;
  enableUpdateWebsite: boolean;
  content: Content;
};

/**
 * Form buttons
 * @param {ButtonLabels} buttons - a form button labels(contains cancel & submit).
 * @param {Function} close - a handler to close the modal
 * @param {boolean} isPending - to determine when to show the spinner within a button
 * @param {boolean} isSubmitClicked - to determine whether the submit button is clicked.
 * @param {Function} updateCancelStatus - a callback handler to disable close or cancel modal(disable closing the modal)
 * @returns {JSX.Element}
 */
export const FormButtons = memo(function Footer({
  buttons: { cancel, submit },
  close,
  isPending,
  isSubmitClicked,
  updateCancelStatus
}: FormButtonsProps) {
  const { formState } = useFormContext();
  const [isCancelDisabled, setCancelDisabled] = useState(false);

  useEffect(() => {
    if (Object.keys(formState.errors).length == 0 && formState.isValid === true && isSubmitClicked) {
      setCancelDisabled(true);
      updateCancelStatus(true);
    } else {
      setCancelDisabled(false);
      updateCancelStatus(false);
    }
  }, [formState, isSubmitClicked]);

  return (
    <div className="content-submission-footer">
      <Button variant="tertiary" dark onClick={close} size="md" disabled={isCancelDisabled}>
        {cancel}
      </Button>

      <Button
        type="submit"
        spinner={isPending}
        size="md"
        disabled={Object.keys(formState.errors).length !== 0 || formState.isValid === false || isPending}
      >
        {submit}
      </Button>
    </div>
  );
});

/**
 * Form inputs for website content
 * @param {FormLabels} formLabels - labels required for the form
 * @param {ContentTypes} contentTypes - types of contents can be selected from Dropdown. It can be a list of [Blog, Image, Live Stream, Podcast, Social Post, Video]
 * @param {ScanResult} failedUrl - to determine the url is secure & safe
 * @param {boolean} duplicateUrl - to determine the url is duplicate or not.
 * @param {ContentSubmissionLabels} contentSubmissionLabels - content submission labels
 * @param {string} YOUTUBE_HOSTS - The valid hosts of YouTube
 * @param {string} TWITCH_HOSTS - The valid hosts of Twitch
 * @param {string} INSTAGRAM_HOSTS - The valid hosts of Instagram
 * @param {string} FACEBOOK_HOSTS - The valid hosts of Facebook
 * @param {string} TIKTOK_HOSTS - The valid hosts of TikTok
 * @param {boolean} invalidUrl - to determine th url is valid or not
 * @param {boolean} enableUpdateWebsite - an option to edit the submitted content
 * @param {Content} content - The details about a content
 * @returns {JSX.Element}
 */
export const WebsiteContentInput = memo(function WebsiteContentInput({
  formLabels,
  contentTypes,
  failedUrl,
  duplicateUrl,
  contentSubmissionLabels,
  YOUTUBE_HOSTS,
  TWITCH_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TIKTOK_HOSTS,
  invalidUrl,
  enableUpdateWebsite,
  content
}: WebsiteContentInputProps) {
  const TITLE_MAX_LENGTH = 3000;
  const rules: ContentSubmissionFormRules = {
    contentTitle: {
      validate: (value) => {
        if (value?.trim() === "") return contentSubmissionLabels.contentTitleRequired;
        if (value?.length > TITLE_MAX_LENGTH) return contentSubmissionLabels.contentTitleLongMessage;
      }
    },
    contentUrl: {
      required: contentSubmissionLabels.contentUrlRequired,
      validate: (value) => {
        if (value === "https://") return contentSubmissionLabels.contentUrlRequired;
        else if (isValidHost(value)) return contentSubmissionLabels.websiteUrlMessage;
      }
    },
    contentType: {
      required: contentSubmissionLabels.contentTypeRequired,
      validate: (countryVal) => {
        const { label, name, value } = countryVal;
        if (!label && !name && !value && isObj(countryVal)) {
          return contentSubmissionLabels.contentTypeRequired;
        } else if (isString(countryVal) && !countryVal) {
          return contentSubmissionLabels.contentTypeRequired;
        }
        return true;
      }
    }
  };

  const methods = useFormContext();
  const { control, setError, clearErrors } = methods;
  const onSelectChange = useCallback(
    (field) => (item) => {
      if (item.value) field.onChange(item);
      else field.onChange("");
    },
    []
  );
  const isValidHost = (url: string): boolean => {
    try {
      const { hostname } = new URL(url);
      const trimmedHostName = hostname.replace("www.", "");
      const validHostnames = [
        ...YOUTUBE_HOSTS,
        ...TWITCH_HOSTS,
        ...INSTAGRAM_HOSTS,
        ...FACEBOOK_HOSTS,
        ...TIKTOK_HOSTS
      ];
      return validHostnames.includes(trimmedHostName);
    } catch {
      // If URL is invalid, return false
      return false;
    }
  };

  useEffect(() => {
    if (failedUrl?.isSecure || !duplicateUrl) {
      clearErrors(`url`);
    }
    if (failedUrl?.isSecure === false)
      setError(`url`, { type: "manual", message: contentSubmissionLabels.unsafeUrlMessage });
    if (duplicateUrl) setError(`url`, { type: "manual", message: contentSubmissionLabels.duplicateUrlMessage });
    if (invalidUrl) setError(`url`, { type: "manual", message: contentSubmissionLabels.validUrlMessage });
  }, [failedUrl, duplicateUrl, invalidUrl, setError]);

  return (
    <div className="content-submission-form-elements">
      <Controller
        control={control}
        name="title"
        rules={!enableUpdateWebsite && rules.contentTitle}
        render={({ field, fieldState: { error } }) => (
          <Input
            errorMessage={error?.message || ""}
            {...field}
            label={formLabels.contentTitle}
            placeholder={formLabels.contentTitlePlaceholder}
            id="title"
            disabled={enableUpdateWebsite}
            value={enableUpdateWebsite ? content?.name : ""}
          />
        )}
      />

      <Controller
        control={control}
        name="url"
        rules={rules.contentUrl}
        render={({ field, fieldState: { error } }) => (
          <Input
            errorMessage={error?.message || ""}
            {...field}
            label={formLabels.contentUrl}
            placeholder={formLabels.contentUrlPlaceholder}
            type="url"
            id="url"
            value={enableUpdateWebsite ? content?.contentUri : ""}
          />
        )}
      />
      <Controller
        control={control}
        name="type"
        rules={!enableUpdateWebsite && rules?.contentType}
        render={({ field, fieldState: { error } }) => (
          <Select
            id="content-type"
            errorMessage={error?.message}
            options={contentTypes}
            label={formLabels.contentType}
            onChange={!enableUpdateWebsite ? onSelectChange(field) : null}
            disabled={enableUpdateWebsite}
            selectedOption={enableUpdateWebsite && { value: content?.contentType, label: content?.contentTypeLabel }}
          />
        )}
      />
    </div>
  );
});

/**
 * A form to submit/update a website content
 * @param {Function} setUpdatedContent - a handler to determine whether the content is updated or not.
 * @param {Function} onClose - a handler to close the modal
 * @param {FormLabels} formLabels - labels required for the form
 * @param {ContentTypes} contentTypes - types of contents can be selected from Dropdown. It can be a list of [Blog, Image, Live Stream, Podcast, Social Post, Video]
 * @param {ButtonLabels} buttonsLabels - button labels
 * @param {ContentSubmissionLabels} contentSubmissionLabels - content submission labels
 * @param {string} participationId - id of the participant
 * @param {string} thumbnail - The thumbnail url
 * @param {string} YOUTUBE_HOSTS - The valid hosts of YouTube
 * @param {string} TWITCH_HOSTS - The valid hosts of Twitch
 * @param {string} INSTAGRAM_HOSTS - The valid hosts of Instagram
 * @param {string} FACEBOOK_HOSTS - The valid hosts of Facebook
 * @param {string} TIKTOK_HOSTS - The valid hosts of Facebook
 * @param {BrowserAnalytics} analytics - The brower analytics
 * @param {OpportunityWithDeliverables} opportunity - The details about an opportunity
 * @param {string} locale - The current language locale.
 * @param {Function} updateCancelStatus - a callback handler to disable close or cancel modal(disable closing the modal)
 * @param {boolean} enableUpdateWebsite - an option to edit the submitted content
 * @param {Content} content - The details about a content
 * @returns {JSX.Element}
 */
const SubmitWebsiteContentForm: FC<SubmitWebsiteContentFormProps> = ({
  setUpdatedContent,
  onClose,
  formLabels,
  contentTypes,
  buttonsLabels,
  contentSubmissionLabels,
  participationId,
  thumbnail,
  YOUTUBE_HOSTS,
  TWITCH_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TIKTOK_HOSTS,
  analytics,
  opportunity,
  locale,
  updateCancelStatus,
  enableUpdateWebsite,
  content,
  deliverableId,
  deliverableTitle,
  deliverableType
}) => {
  const { errorHandler } = useDependency();
  const [failedUrl, setFailedUrl] = useState(null);
  const [showWebsiteContentForm, setShowWebsiteContentForm] = useState(true);
  const [duplicateUrl, setDuplicateUrl] = useState(false);
  const [invalidUrl, setInvalidUrl] = useState(false);
  const [isSubmitClicked, setSubmitClicked] = useState(false);
  const { dispatch } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { success: successToast } = useToast();
  const onSubmitWebsiteContent = async (data, websiteContentPayload) => {
    try {
      const submitContentService = enableUpdateWebsite
        ? SubmittedContentService.updateWebsiteContent
        : SubmittedContentService.submitWebsiteContentDeliverable;
      await submitContentService({
        participationId: participationId,
        websiteContent: websiteContentPayload
      });
      try {
        analytics.submittedWebsiteUrl({
          opportunity,
          locale,
          contentType: enableUpdateWebsite ? data.type : data.type?.value,
          websiteDomain: new URL(data.url).hostname,
          deliverableTitle,
          deliverableType
        });
      } catch {
        // Handle invalid URL gracefully
        analytics.submittedWebsiteUrl({
          opportunity,
          locale,
          contentType: enableUpdateWebsite ? data.type : data.type?.value,
          websiteDomain: "unknown",
          deliverableTitle,
          deliverableType
        });
      }
      setUpdatedContent(true);
      onClose();
      successToast(
        <Toast
          header={contentSubmissionLabels.success?.title}
          content={contentSubmissionLabels.success?.content}
          closeButtonAriaLabel={buttonsLabels.close}
        />,
        {
          onClose: () => onToastClose(SUCCESS, dispatch)
        }
      );
    } catch (e) {
      if (
        e.response?.data?.status === 409 &&
        (e.response?.data?.code === "submit-website-content-duplicate-submitted-content" ||
          e.response?.data?.code === "submit-website-content-conflicting-action")
      ) {
        setDuplicateUrl(true);
        setSubmitClicked(false);
        analytics.receivedContentSubmissionErrorMessage({
          locale,
          opportunity,
          deliverableTitle,
          deliverableType,
          errorMessage: e?.response?.data?.message || e?.response?.data?.detail || e?.message || "No message",
          errorCode: e?.response?.status || "No code",
          submissionType: "WEBSITE"
        });
        return;
      }
      errorHandler(stableDispatch, e);
    }
  };
  const onSubmit = useCallback(async (data) => {
    const urlInput = { urls: [data.url] };
    setInvalidUrl(false);
    setSubmitClicked(true);
    try {
      const res = (await SubmittedContentService.validateContent(urlInput, "CREATORS")) as AxiosResponse;
      const result = res.data?.results[0];
      setFailedUrl(result);
      setDuplicateUrl(false);
      setSubmitClicked(false);
      if (!result.isSecure) return;
      const contentDetails = {
        title: data.title.trim(),
        url: data.url,
        thumbnail: thumbnail,
        description: "",
        contentType: data.type?.value || data.type
      };
      const websiteContentPayload = enableUpdateWebsite
        ? {
            id: content.id,
            contentDetails: contentDetails
          }
        : {
            ...contentDetails,
            deliverableId: deliverableId
          };

      await onSubmitWebsiteContent(data, websiteContentPayload);
    } catch (e) {
      if (e.response?.data?.status === 422 && e.response?.data?.code === "validate-content-urls-invalid-input") {
        setInvalidUrl(true);
        setSubmitClicked(false);
        analytics.receivedContentSubmissionErrorMessage({
          locale,
          opportunity,
          deliverableTitle,
          deliverableType,
          errorMessage: e?.response?.data?.message || e?.response?.data?.detail || e?.message || "No message",
          errorCode: e?.response?.status || "No code",
          submissionType: "WEBSITE"
        });
        return;
      }
      errorHandler(stableDispatch, e);
    }
  }, []);

  useEffect(() => {
    return () => {
      setShowWebsiteContentForm(false);
    };
  }, []);

  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);

  if (!showWebsiteContentForm) return;

  return (
    <Form
      onSubmit={submitHandler}
      defaultValues={{
        title: enableUpdateWebsite ? content?.name : "",
        type: enableUpdateWebsite ? content?.contentType : "",
        url: enableUpdateWebsite ? content?.contentUri : ""
      }}
      mode="onChange"
    >
      <WebsiteContentInput
        {...{
          formLabels,
          contentTypes,
          failedUrl,
          duplicateUrl,
          contentSubmissionLabels,
          YOUTUBE_HOSTS,
          TWITCH_HOSTS,
          INSTAGRAM_HOSTS,
          FACEBOOK_HOSTS,
          TIKTOK_HOSTS,
          invalidUrl,
          enableUpdateWebsite,
          content
        }}
      />
      <FormButtons
        {...{
          buttons: { cancel: buttonsLabels.cancel, submit: buttonsLabels.submit },
          close: onClose,
          isPending,
          isSubmitClicked,
          updateCancelStatus
        }}
      />
    </Form>
  );
};

SubmitWebsiteContentForm.defaultProps = {
  enableUpdateWebsite: false,
  content: null
};

export default SubmitWebsiteContentForm;
