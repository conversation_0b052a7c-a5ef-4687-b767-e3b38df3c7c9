import "reflect-metadata";
import { createMocks } from "node-mocks-http";
import {{pascalCase ApiResourceName}}Controller from "../../../src/controllers/{{pascalCase ApiResourceName}}Controller";

describe("{{pascalCase ApiResourceName}}Controller", () => {
  let controller: {{pascalCase ApiResourceName}}Controller;

  beforeEach(() => jest.clearAllMocks());

  it("{{lowerCase (sentenceCase ApiResourceName)}} successfully", async () => {
    const {{camelCase ApiResourceName}}Id = "41b16e33";
    const { req, res } = createMocks({
      method: "GET",
      url: `/{{ApiEndpointUrl}}/${ {{camelCase ApiResourceName}}Id }`
    });
    const mock{{pascalCase ApiResourceName}} = jest.fn();

    controller = new {{pascalCase ApiResourceName}}Controller({ {{pascalCase ApiResourceName}}: mock{{camelCase ApiResourceName}} });

    await controller.execute(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mock{{pascalCase ApiResourceName}}).toHaveBeenCalledTimes(1);
  });
});
