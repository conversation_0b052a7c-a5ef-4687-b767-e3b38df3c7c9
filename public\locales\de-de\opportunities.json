{"titleSmall": "Vorteile und Belohnungen", "title": "Inhalte einreichen", "subTitle": "Unseren Mitgliedern stehen fantastische Opportunities zur Verfügung. Lies weiter, um mehr darüber zu erfahren.", "gameTitle": "Spieltitel", "gameSubTitle": "Sei bei exklusiven Events dabei. Mache Aufnahmen deines Lieblingsspiels vor dessen Veröffentlichung. Nimm an einem Electronic Arts-Livestream teil. Erhalte Zugriff auf besondere Studio-Events und noch vieles mehr.", "competitionTitle": "Schließe dich hochkarätigen Content-Creatorn an", "competitionSubTitle": "Schließe neue Freundschaften, zeige deine Talente und arbeite mit anderen Content-Creatorn aus der ganzen Welt zusammen.", "moreRewardsTitle": "<PERSON><PERSON>", "moreRewardsSubTitle": "Aber das ist noch nicht alles. Wir haben unseren Creatorn noch viel mehr zu bieten.", "startCreatingTitle": "Bist du bereit, krea<PERSON><PERSON> zu werden?", "startCreatingSubTitle": "Wir begrüßen Creator mit den unterschiedlichsten Talenten und Hintergründen, die das Creators Network zu einer vielfältigen und aufregenden Community machen.", "startCreatingConclusion": "Du glaubst also, du hast das Zeug zum Creator?", "joinNow": "Anmelden", "discountCode": "Rabatt-Codes", "gameCode": "Spielcode", "currency": "Ingame-Währung", "merchandise": "Exklusive Merchandise-Artikel", "gameAccessTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zu Spielen", "gameAccessDescription": "Du möchtest die neuesten Electronic Arts-Spiele vor allen anderen spielen? Wir fördern die Kreativität unserer Community, indem wir dir Early Access zu deinen Lieblingsspielen bieten.", "channelTitle": "Kanal-Partnerschaften", "channelDescription": "Kollaboriere mit uns und gründe langfristige Kanal-Partnerschaften, die dich unterstützen und dir dabei helfen, deine Community zu vergrößern.", "sponsorTitle": "Gesponserte Inhalte", "sponsorDescription": "Arbeite mit uns zusammen und erstelle fantastische Inhalte zu deinen Lieblingsspielen für deinen Kanal.", "ebonixTitle": "Creator", "ebonixSubTitle": "EBONIX", "ebonixDescription": "Ohne seine Stimme zu erheben, kann man keinen Wandel erzeugen. Ich hatte das große Glück mit meiner Stimme einen Generationswechsel bei Die Sims hervorzurufen, indem ich mich mit dem Team von Die Sims über ihr Hautton-Update ausgetauscht habe! Es ist ein großer Schritt hin zu mehr Inklusion und Vielfalt, und ich bin stolz darauf, ein <PERSON><PERSON> davon zu sein!", "description": "Ent<PERSON>cke und nutze die verschiedenen Opportunities, die im Moment zur Verfügung stehen. Wir fügen immer etwas Neues hinzu, also schau regel<PERSON>ß<PERSON> vorbei.", "franchisePreferences": "Franchise-Einstellungen", "returnToOpportunities": "zurück", "details": "Details", "location": "Ort", "eventDate": "Event-Datum", "registration": "Registrierung", "registrationStartDate": "Startdatum und -zeit zur Registrierung", "registrationEndDate": "Enddatum und -zeit zur Registrierung", "signUp": "Registrieren", "whatIsIt": "Worum geht es?", "opportunityDescription": "Beschreibung", "aboutThisOpportunity": "About this Opportunity", "viewAll": "Alle anzeigen", "thingsToDo": "Aufgaben", "freeGameCode": "Kostenloser Spielcode", "freeGameCodeDesc": "Dieser Spielcode ist auf den folgenden Plattformen verfügbar. <PERSON><PERSON> beachte, dass du den Code direkt nach dem Beitritt erhältst:", "thingsToDo1": "Registriere dich, wenn du Interesse hast", "thingsToDo2": "Überprüfe die Opportunity-Bedingungen und stimme zu", "thingsToDo3": "Reiche den Inhalt ein", "thingsToDo4": "<PERSON><PERSON><PERSON> das <PERSON>", "join": "Beitreten", "joinOpportunity": "Tritt der Opportunity bei", "thanksMessage": "Vielen Dank! Du bist registriert.", "criteria": "Kriterien", "contentGuidelines": "Voraussetzungen für das Einreichen von Inhalten", "downloadEaLogo": "Lade Electronic Arts-Wasserzeichen herunter", "joined": "Beigetreten", "completed": "Abgeschlossen", "past": "Past", "open": "Open", "invited": "Invited", "remote": "Online-Event", "inPersonEvent": "Vor-Ort-Event", "grabACodeEvent": "Code holen", "paid": "€ Bezahlt", "contentSubmission": "Einreichung von Inhalten", "event": "Event", "goToDiscord": "<PERSON><PERSON><PERSON> zu <PERSON>", "getGameCode": "<PERSON><PERSON>", "gameCodeNotReady": "Dein <PERSON>lcode ist noch nicht bereit!", "gameCodeInfo": "Dein Code wird auf der Opportunity-Seite angezeigt. <PERSON><PERSON> schau bald wieder vorbei", "contentSubmissionSubtitle": "De<PERSON> eingereichten Inhalte", "submissionWindow": "Zeitfenster für Einreichungen:", "submitContent": "Inhalte einreichen", "notSubmittedContent": "Du hast keine Inhalte eingereicht.", "additionalInfo": "Weitere Informationen", "discordChannel": "Discord-<PERSON><PERSON>", "resources": "Ressourcen", "youAreRegistered": "Du bist für diese Opportunity registriert.", "pointOfContact": "Kontaktstelle", "searchForOpportunites": "Nach Opportunities suchen", "searchResults": "Suchergebnisse für", "apply": "Bewerben", "perks": "<PERSON><PERSON><PERSON><PERSON>", "filteredBy": "Filtern nach", "submitYourContent": "Reiche deinen Inhalt ein", "submitYourContentDesc": "Im Rahmen der Vereinbarung für EA Creator Network bist du verpflichtet, alle folgenden Hinweise bereitzustellen:", "guideLinePoint1": "Zeige in den ersten fünf Sekunden deines Videos oder Streams das “Gesponsert von EA“-Wasserzeichen.", "guideLinePoint2": "<PERSON><PERSON> <PERSON>G<PERSON>po<PERSON><PERSON> von <PERSON>“ zu Beginn deiner Videobeschreibung/des “Mehr Informationen“-Tabs besonders hervor und füge einen Link in die Beschreibung ein.", "guideLinePoint3": "Sage in den ersten fünf Sekunden deines Videos/Streams deutlich hörbar “Ich danke Electronic Arts für das Sponsoring dieses Videos“ oder etwas Vergleichbares.", "guideLinePoint4": "<PERSON><PERSON> sic<PERSON>, dass du die Offenlegungs-<PERSON><PERSON> von Plattformen korrekt nutzt, wie zum Beispiel die “Beinhaltet bezahlte Werbung“-Option auf YouTube oder die “Bezahlte Partnerschaft“-Option auf Instagram.", "guideLinePoint5": "Zeige in den ersten fünf Sekunden deines Videos oder Streams das “Präsentiert vom EA Creator Network“-Wasserzeichen.", "guideLinePoint6": "<PERSON><PERSON> “Präsentiert vom EA Creator Network“ zu Beginn deiner Videobeschreibung/dem “Mehr Informationen“-Tab besonders hervor.", "guideLinePoint7": "Sage in den ersten fünf Sekunden deines Videos/Streams deutlich hörbar “Ich danke Electronic Arts für die Einladung zu diesem Early-Access-Event“ oder etwas Vergleichbares", "contentGuideSubDesc": "<PERSON>lt<PERSON> du Fragen haben, wende dich bitte an deinen Community-Manager.", "addAnother": "<PERSON><PERSON><PERSON> hinzufügen", "urlPlaceholder": "Bitte gib die URL des Inhalts ein:", "duplicateUrl": "Dieser Link wurde bereits eingereicht. Doppelte Videos sind nicht zulässig.", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "Dieses Video stammt von keinem Social-Media-Kanal, der mit deinem CN-Konto verbunden ist.", "youtubeVideoError": "<PERSON>s konnte kein YouTube-Video mit der angegebenen ID gefunden werden.", "genericContentError": "<PERSON>te gib eine gültige URL ein.", "noAvailableOpportunities": "<PERSON>ine verfügbaren Opportunities", "noAvailableOpportunitiesDesc": "Derzeit gibt es keine Opportunities.", "noAvailableSearchResults": "Keine Opportunities gefunden", "noAvailableSearchResultsDesc": "Überprüfe deine Schreibweise, probiere andere Suchbegriffe aus oder passe deine Filteroptionen an.", "Back": "Zurück", "overview": "Übersicht", "perksSubtitle": "Was du für deine Teilnahme erhältst", "registrationCloses": "Registrierung endet:", "registrationEnds": "Registration Ends", "submissionCloses": "Einreichung endet:", "submissionOpens": "Einreichung beginnt:", "submissionClosedMessage": "Das Zeitfenster für die Einreichung dieser Opportunity wurde geschlossen.", "gameCodePending": "Spielcode ausstehend. Schau in Kürze wieder vorbei.", "opportunityType": "Opportunity-Typ", "more": "<PERSON><PERSON>", "gameCodeTitle": "<PERSON><PERSON>", "copyGameCode": "Spielcode kopieren", "copied": "<PERSON><PERSON><PERSON>", "submitContentText": "Nutze diesen Abschnitt, um Inhalte für diese Opportunity einzureichen.", "submitWindowClosedText": "Das Zeitfenster für die Einreichung von Inhalten für diese Opportunity ist geschlossen, und es können keine neuen Inhalte mehr eingereicht werden.", "collab": "Kollaboration", "dailyAllowance": "Tägliche Vergütung", "designCouncil": "Design Council", "earlyAccess": "Early Access", "exclusivePreview": "Exklusive Vorschau", "food": "Essen", "freeGameCodePerk": "Kostenloser Spielcode", "hotel": "Hotel", "paidPerk": "Be<PERSON>hlt", "privateDiscordChannel": "Privater Discord", "swag": "Swag", "travel": "<PERSON><PERSON>", "vipEvent": "VIP-Event", "creatorCodePerk": "Creator-Code", "supportACreator": "Support-a-Creator", "whatToDoTitle": "Was ist zu tun", "toDoTitle": "Aufgaben", "whatToDoDescription": "<PERSON><PERSON> du dieser Opportunity beitreten möchtest, musst du Folgendes tun:", "whatToDoLabels": {"joinOpportunity": "Tritt der Opportunity bei", "getGameCode": "Ho<PERSON> dir den Spielcode", "signContract": "Unterschreibe den Opportunity-Vertrag", "getPaid": "<PERSON>rde bezahlt", "attendInPersonEvent": "<PERSON><PERSON><PERSON> das <PERSON>", "attendOnlineEvent": "Besuche das Online-Event", "makeContent": "<PERSON><PERSON><PERSON>", "submitContent": "Inhalte einreichen", "getCreatorCode": "Creator-<PERSON> holen"}, "decline": "<PERSON><PERSON><PERSON><PERSON>", "declineModalTitle": "<PERSON><PERSON><PERSON>", "declineModalContent": "Möchtest du die Einladung wirklich ablehnen? Falls du deine Meinung änderst, kannst du dieser Opportunity zu einem späteren Zeitpunkt nicht mehr beitreten.", "declinedText": "Du hast diese Opportunity abgelehnt.", "invitedText": "Dir wurde eine Beitrittseinladung geschickt", "declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentGuidelinesTitle": "Inhaltsrichtlinien", "creatorCode": {"title": "<PERSON><PERSON>-Code", "copyLabel": "<PERSON><PERSON><PERSON>", "copyText": "Creator-<PERSON> kopieren", "textCopied": "<PERSON><PERSON><PERSON>", "codeNotYetActiveMessage": "<PERSON><PERSON>-Code für diese Opportunity ist noch nicht aktiv.", "codeExpiredMessage": "<PERSON><PERSON>-Code für diese Opportunity ist nicht mehr aktiv.", "codeActivationStartTime": "Code Activation Start Time", "codeActivationEndTime": "Code Activation End Time"}, "remoteEvent": {"title": "Online-Event-Details", "eventTime": "Event-Zeitraum", "description": "Dieses Event ist beendet.", "joinEvent": "Am Event teilnehmen", "noPasswordRequired": "<PERSON><PERSON> er<PERSON>", "noEventDetailsAdded": "<PERSON>s wurden noch keine Event-Details hinzugefügt", "NoMeetingLinkInfo": "Contact Community Manager for event details."}, "supportACreatorDisclosure": {"title": "Disclosure", "subTitle": "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:", "description1": "Include audible and written disclosure to let viewers know that using your Creator Code directly supports you. “Using my Creator Code directly supports me.” (or similar).", "description2": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.", "description3": "Prominently include “Sponsored by EA” “Sponsored”, or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #SponsoredbyEA, #Sponsored, or #Ad are also acceptable.", "description4": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for sponsoring this video.” (or similar).", "description5": "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion” and Instagram’s “Partnership” options."}, "contentDeliverables": "Deliverables", "registrationWindow": "Registration Window", "contentSubmissionWindow": "Submission Window", "closed": "Closed", "activationWindow": "Code Window", "keyDates": "Key Dates", "inPersonStartTime": "In-Person Event Start Time:", "inPersonEndTime": "In-Person Event End Time:", "onlineEventStartTime": "Online Event Start Time:", "onlineEventEndTime": "Online Event End Time:", "contentSubmissionDeadline": "Content Submission Deadline:", "viewDeliverables": "View Deliverables", "codeActivationStartTime": "Code Activation Start Time:", "codeActivationEndTime": "Code Activation End Time:", "checkBackSoon": "Check back soon", "payment": "Payment", "sales": "% of Sales", "eventDetails": {"eventStartTime": "Event Start Time", "eventEndTime": "Event End Time", "eventLocation": "Event Location", "password": "Password", "info": "Info", "copyAddress": "Copy address", "copyPassword": "Copy password"}, "eventInformation": "Event Information"}