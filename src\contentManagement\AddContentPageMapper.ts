import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type AddContentPageLabels = {
  addContentPageLabels: {
    accountInformation2: string;
    accountInformation1: string;
    no: string;
    title: string;
    urlTitle: string;
    addMoreUrlLabel: string;
    addNewContent: string;
    description: string;
    addContent: string;
    contentSubmissionSucessDescription: string;
    reviewContent: string;
    opportunityHeading: string;
    modalTitle: string;
    yes: string;
    modalDescription: string;
    addContentInstruction: string;
    contentSubmissionSucessTitle: string;
    connectAnAccount: string;
    clickTheIcon: string;
    contentInformation3: string;
    urlPlaceholder: string;
    contentInformation2: string;
    contentInformation1: string;
  };
};

export class AddContentPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): AddContentPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      addContentPageLabels: {
        accountInformation2: microCopy.get("addContent.accountInformation2"),
        accountInformation1: microCopy.get("addContent.accountInformation1"),
        no: microCopy.get("addContent.no"),
        title: microCopy.get("addContent.title"),
        urlTitle: microCopy.get("addContent.urlTitle"),
        addMoreUrlLabel: microCopy.get("addContent.addMoreUrlLabel"),
        addNewContent: microCopy.get("addContent.addNewContent"),
        description: microCopy.get("addContent.description"),
        addContent: microCopy.get("addContent.addContent"),
        contentSubmissionSucessDescription: microCopy.get("addContent.contentSubmissionSucessDescription"),
        reviewContent: microCopy.get("addContent.reviewContent"),
        opportunityHeading: microCopy.get("addContent.opportunityHeading"),
        modalTitle: microCopy.get("addContent.modalTitle"),
        yes: microCopy.get("addContent.yes"),
        modalDescription: microCopy.get("addContent.modalDescription"),
        addContentInstruction: microCopy.get("addContent.addContentInstruction"),
        contentSubmissionSucessTitle: microCopy.get("addContent.contentSubmissionSucessTitle"),
        connectAnAccount: microCopy.get("addContent.connectAnAccount"),
        clickTheIcon: microCopy.get("addContent.clickTheIcon"),
        contentInformation3: microCopy.get("addContent.contentInformation3"),
        urlPlaceholder: microCopy.get("addContent.urlPlaceholder"),
        contentInformation2: microCopy.get("addContent.contentInformation2"),
        contentInformation1: microCopy.get("addContent.contentInformation1")
      }
    };
  }
}
