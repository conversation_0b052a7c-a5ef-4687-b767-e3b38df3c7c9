import "reflect-metadata";
import type { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { DEFAULT_LOCALE, getLocale } from "../../utils";
import config from "../../config";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import onError from "@src/middleware/JsonErrorHandler";
import ApiContainer from "@src/ApiContainer";
import { SaveRegistrationCodeToSessionController } from "@eait-playerexp-cn/onboarding-authentication-plugins";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  if (config.FLAG_PER_PROGRAM_PROFILE) {
    const controller = ApiContainer.get(SaveRegistrationCodeToSessionController);
    await controller.handle(req, res);
    return;
  }
  const code = req.query.code === undefined ? null : req.query.code;
  const opportunityId = req.query.opportunityId === undefined ? null : req.query.opportunityId;
  const locale = req.cookies.NEXT_LOCALE || getLocale(req.headers, config.SUPPORTED_LOCALES);
  const urlLocale = `${(locale !== DEFAULT_LOCALE && "/" + locale) || ""}`;

  if (!code) {
    res.setHeader("Location", encodeURI(`${urlLocale}/404`));
    res.status(302);
    res.end();
    return;
  }

  req.session.opportunityId = opportunityId;
  req.session.registrationCode = code;
  req.session.save();

  res.setHeader("Location", encodeURI("/api/login"));
  res.status(302);
  res.end();
});

export default router.handler({ onError });
