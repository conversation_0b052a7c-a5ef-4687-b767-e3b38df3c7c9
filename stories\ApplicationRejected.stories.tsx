import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { ApplicationRejectedPage } from "../components/pages/interested-creators/ApplicationRejectedPage";
import BrowserAnalytics from "../src/analytics/BrowserAnalytics";

/**
 * Page shown to creators whose request to join the program has been rejected
 */
const meta: Meta<typeof ApplicationRejectedPage> = {
  title: "Creator Network/Pages/Interested Creators/Application Rejected Page",
  component: ApplicationRejectedPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationRejectedPage>;

/**
 * Default copy shown to creators whose request to participate in the program has been rejected
 */
export const Page: Story = {
  args: {
    applicationRejectedLabels: {
      title: "Thank you for requesting to join the EA Creator Network",
      descriptionPara1:
        "Unfortunately, we can not offer you membership at this time. We receive a large number of submission requests on a regular basis, but we can only offer a small percentage of submissions access.",
      descriptionPara2:
        "This doesn’t rule you out from becoming a member in the future, so continue to grow your following and make great content.",
      descriptionPara3: "Sincerely,",
      descriptionPara4: "The Electronic Arts Team",
      returnToCreatorNetwork: "Return to Creator Network",
      submissionReviewed: "Submission Reviewed",
      submissionReviewedDescription:
        "Thank you for your interest in joining the EA Creator Network. Your submission has been reviewed and unfortunately, we can not offer you membership at this time. You will be eligible to resubmit your request on {{reSubmitRequestDate}}.",
      gamerTag: "Gamer Tag",
      email: "Email",
      status: "Status",
      submissionDate: "Submission Date",
      closed: "Closed",
      reApplyTitle: "Still interested in joining?",
      reApplyDescription:
        "If you are still interested in joining the EA Creator Network, please review and update your information to resubmit.",
      reviewAndResubmit: "Review & Resubmit"
    },
    locale: "en-us",
    analytics: { checkedApplicationStatus: () => {} } as unknown as BrowserAnalytics
  }
};
