{"title": "Añadir contenido de {{mediaType}}", "infoLabel": "Asegúrate de que la URL que vas a añadir proviene de esta cuenta {{mediaType}}:", "contentUrl": "Escribe la URL {{mediaType}}:", "contentUrlPlaceholder": "Ejemplo: https://www. {{mediaType}}.com/mienlace", "duplicateUrl": "Esta URL ya se ha enviado.", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "Este vídeo no proviene de un canal social conectado a tu cuenta de CN.", "youtubeVideoError": "No se ha encontrado ningún vídeo de YouTube con la ID proporcionada.", "genericContentError": "Escribe una URL válida.", "unsupportedContentError": "El contenido que intentas enviar no coincide con la opción seleccionada", "cancelButton": "<PERSON><PERSON><PERSON>", "submitButton": "Enviar contenido", "contentUrlRequired": "{{mediaType}} URL necesaria", "unsafeUrlError": "No es posible enviar contenido desde este dominio o página web", "urlNotFromConnectedAccount": "La URL que has introducido no proviene de esta cuenta de red social conectada.", "urlNotFromConnectedChannel": "La URL que intentas enviar no proviene de la cuenta {{accountName}} {{mediaType}}", "unknownTikTokVideo": "The URL you are trying to submit is either not from the {{accountName}} TikTok account or is not publicly available.", "invalidSocialSubmission": "No se puede guardar el contenido de redes sociales porque: El ID de cuenta no puede estar en blanco.", "accountAuthorizationFailure": "No se puede guardar el contenido de redes sociales porque: No se puede obtener información de la cuenta de TikTok (token caducado o no válido).", "cannotExpandUrl": "Expanding this short URL is taking longer than usual. Please try again later.", "cannotSubmitContentInvalidInput": "Submitted URL is invalid", "invalidFacebookPage": "Unable to submit. This content does not belong to the Facebook page selected.", "unSupportedContentType": "Only Instagram videos and reels are allowed for submission. The content type you're trying to upload is not supported.", "unSupportedContentTypeForMedia": "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.", "duplicateScannedContentUrl": "Your Community Manager has submitted this URL."}