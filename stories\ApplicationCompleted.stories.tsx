import React from "react";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ApplicationCompletedPage } from "../components/pages/interested-creators/ApplicationCompletedPage";

/**
 * Page shown to a creator who has completed their profile to be considered to join the program
 */
const meta: Meta<typeof ApplicationCompletedPage> = {
  title: "Creator Network/Pages/Interested Creators/Application Completed Page",
  component: ApplicationCompletedPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationCompletedPage>;

/**
 * Default copy shown to creators who have completed their profile to request to join the program
 */
export const Page: Story = {
  args: {
    completeLabels: {
      title: "Submission Complete",
      subTitle: "Thank you for completing your request to join the EA Creator Network.",
      description:
        "Your submission request to join will now be reviewed by a member of the EA Creator Network team. If you are accepted, you’ll receive an email with an invitation to join!",
      backHome: "Return to Creator Network",
      submissionCompleteSubTitle: "Thank you for submitting your request to join the EA Creator Network.",
      submissionCompleteDescription:
        "Your submission has been sent for review by a member of the EA Creator Network team. If you are accepted, you’ll receive an email with an invitation to join!",
      unReviewed: "Unreviewed",
      gamerTag: "Gamer Tag",
      email: "Email",
      status: "Status",
      submissionDate: "Submission Date"
    }
  }
};
