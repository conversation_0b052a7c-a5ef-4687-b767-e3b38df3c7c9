import { FC, useEffect, useState } from "react";
import { useRouter } from "next/router";
import {
  Button,
  ButtonSize,
  ButtonVariant,
  Icon,
  leftArrow,
  OpportunityHeader,
  OpportunityHeaderProps,
  OpportunityHeaderV2,
  OpportunityHeaderV2Props,
  PerksCardWithInvitation,
  Tabs
} from "@eait-playerexp-cn/core-ui-kit";
import { OpportunityWithDeliverables, OpportunityWithPerks } from "@src/api/services/OpportunityService";
import { useDetectScreen } from "../../utils";

export type ActionButtons = {
  disable: boolean;
  label: string;
  onClick: () => void;
  size: ButtonSize;
  variant: ButtonVariant;
};

export type Perks = {
  code: string;
  name: string;
};

export type OpportunityLabels = {
  returnToOpportunities: string;
  perks: string;
  registrationCloses: string;
  perksSubtitle: string;
  registrationEnds: string;
};

export type Tabs = {
  label: string;
  callback: () => void;
};

export type OpportunityWithPerksHeaderProps = {
  participationStatus: {
    invitationId: string;
  };
  participationId: string;
  opportunitiesLabels: OpportunityLabels;
  opportunityHeader: OpportunityHeaderProps | OpportunityHeaderV2Props;
  actions: Array<ActionButtons>;
  opportunity: OpportunityWithPerks | OpportunityWithDeliverables;
  locale: string;
  opportunityDeclined: boolean;
  invitedText: string;
  declinedText: string;
  overview: boolean;
  isJoined: boolean;
  tabsWithContentSubmission: Array<Tabs>;
  tabsWithoutContentSubmission: Array<Tabs>;
  referer?: URL;
  opportunityStatus: string;
  UPDATE_OPPORTUNITY_DETAILS: boolean;
  opportunityHeroImage: string;
};

const OpportunityWithPerksHeader: FC<OpportunityWithPerksHeaderProps> = ({
  participationStatus,
  participationId,
  opportunitiesLabels,
  opportunityHeader,
  actions,
  opportunity,
  locale,
  opportunityDeclined,
  invitedText,
  declinedText,
  overview,
  isJoined,
  tabsWithContentSubmission,
  tabsWithoutContentSubmission,
  referer,
  opportunityStatus,
  UPDATE_OPPORTUNITY_DETAILS,
  opportunityHeroImage
}) => {
  const OVERVIEW_TAB = 0;
  const CONTENT_SUBMISSION_TAB = 1;
  const router = useRouter();
  const isMobileAndTablet = useDetectScreen(1024);
  const [isTabSelect, setIsTabSelect] = useState(0);
  const [sticky, setSticky] = useState(false);

  useEffect(() => {
    window.addEventListener("scroll", handleScrollPos);
    return () => window.removeEventListener("scroll", handleScrollPos);
  }, [isMobileAndTablet]);

  useEffect(() => {
    setIsTabSelect(overview ? 0 : 1);
    const updateTabSelection = () => {
      const tabContainers: NodeList = document.querySelectorAll(".tab-container");
      tabContainers.forEach((container: HTMLElement) => {
        container.querySelectorAll(".tab-item").forEach((tab: HTMLElement, index: number) => {
          tab.classList.remove("tab-item-selected");
          if (index === (overview ? 0 : 1)) {
            tab.classList.add("tab-item-selected");
          }
        });
      });
    };
    updateTabSelection();
  }, [overview]);

  const handleScrollPos = () => {
    if (window.scrollY > 730 && !isMobileAndTablet) {
      setSticky(true);
    } else {
      setSticky(false);
    }
  };

  const backToReferer = () => {
    if (
      /dashboard$/.test(referer as unknown as string) ||
      /profile\?section=payment-information$/.test(referer as unknown as string) ||
      /my-content$/.test(referer as unknown as string) ||
      /notifications$/.test(referer as unknown as string)
    )
      router.back();
    else router.push("/opportunities");
  };
  const tabsToDisplay = () => {
    if (
      (isJoined && (opportunity as OpportunityWithDeliverables)?.hasDeliverables) ||
      (opportunityStatus !== undefined && !isJoined && (opportunity as OpportunityWithDeliverables)?.hasDeliverables)
    ) {
      return tabsWithContentSubmission;
    } else {
      return tabsWithoutContentSubmission;
    }
  };

  const updateButtonAndTimeVisibility = ["OPEN", "INVITED"].includes(
    opportunityHeader.opportunity.status.toLocaleUpperCase()
  );

  return (
    <>
      {!UPDATE_OPPORTUNITY_DETAILS ? (
        <div className="opportunity-perks-header-container">
          <div className="opportunity-perks-header">
            <button role="link" className="opportunity-perks-back-nav" onClick={backToReferer}>
              <Icon icon={leftArrow} />
              <span>{opportunitiesLabels.returnToOpportunities}</span>
            </button>
            <div className="opportunity-perks-header-detail-container">
              <OpportunityHeader {...(opportunityHeader as OpportunityHeaderProps)} />
            </div>
            <div className="opportunity-perks-details-mobile">
              {participationStatus && !participationId && (
                <PerksCardWithInvitation
                  actions={actions}
                  title={opportunitiesLabels.perks}
                  subtitle={opportunitiesLabels.perksSubtitle}
                  footNote={`${opportunitiesLabels.registrationCloses} ${opportunity.registrationPeriodEnd(locale)}`}
                  perks={opportunity.perks ? opportunity.perks : []}
                  invited={participationStatus?.invitationId !== null}
                  declined={opportunityDeclined}
                  invitedText={invitedText}
                  declinedText={declinedText}
                />
              )}
            </div>
            <Tabs selected={!overview ? CONTENT_SUBMISSION_TAB : OVERVIEW_TAB} tabs={tabsToDisplay()} />
          </div>
        </div>
      ) : (
        <>
          {!isMobileAndTablet && (
            <div
              data-testid="opportunity-header-sticky-for-web"
              className={
                sticky
                  ? "opportunity-perks-header-container-with-sticky"
                  : "opportunity-perks-header-container-with-sticky-slide-up"
              }
            >
              <div className="opportunity-tabs-action-buttons-container-with-sticky">
                <div className="opportunity-tabs-container-with-sticky">
                  <Tabs selected={isTabSelect} tabs={tabsToDisplay()} />
                </div>
                {updateButtonAndTimeVisibility && (
                  <div className="opportunity-action-buttons-container-with-sticky">
                    <span className="opportunity-card-registration-detail-with-sticky">
                      <div>{opportunitiesLabels.registrationEnds}</div>
                      <div>{opportunity.registrationPeriodEndWithDateAndTime(locale)}</div>
                    </span>
                    <div
                      className={
                        opportunityHeader.opportunity.status.toLocaleUpperCase() === "OPEN"
                          ? "opportunity-card-button-with-status-open-sticky"
                          : "opportunity-card-button-with-sticky"
                      }
                    >
                      {actions.map((action, key) => (
                        <Button key={key} variant={action.variant} size={"sm"} onClick={action.onClick}>
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="opportunity-perks-headerv2-container">
            <div className="opportunity-perks-header-mobile-or-tab-container">
              <img
                alt="opportunity header"
                src={opportunityHeroImage}
                className="opportunity-perks-header-mobile-or-tab"
              />
            </div>
            <div className="opportunity-perks-header-inner">
              <div className="opportunity-perks-header-detail-container-wrapper">
                <div className="opportunity-perks-header-v2-detail-container">
                  <OpportunityHeaderV2
                    {...(opportunityHeader as OpportunityHeaderV2Props)}
                    endDate={
                      updateButtonAndTimeVisibility &&
                      `${opportunitiesLabels.registrationEnds} ${opportunity.registrationPeriodEndWithDateAndTime(
                        locale
                      )}`
                    }
                  />
                </div>
                <div className="opportunity-tabs-action-buttons-container">
                  <div className="opportunity-tabs-container">
                    <Tabs selected={isTabSelect} tabs={tabsToDisplay()} />
                  </div>
                  {updateButtonAndTimeVisibility && (
                    <div className="opportunity-action-buttons-container">
                      <p className="opportunity-card-registration-detail">{`${
                        opportunitiesLabels.registrationEnds
                      } ${opportunity.registrationPeriodEndWithDateAndTime(locale)}`}</p>
                      <div
                        className={
                          opportunityHeader.opportunity.status.toLocaleUpperCase() === "OPEN"
                            ? "opportunity-card-button-with-status-open"
                            : "opportunity-card-button"
                        }
                      >
                        {actions.map((action, key) => (
                          <Button
                            key={key}
                            variant={action.variant}
                            size={"sm"}
                            onClick={action.onClick}
                            ariaLabel={`${action.label}`}
                          >
                            {action.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default OpportunityWithPerksHeader;
