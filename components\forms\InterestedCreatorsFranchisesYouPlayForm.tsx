import React, { <PERSON>, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Form from "../Form";
import Footer from "./Footer";
import { FranchiseItem } from "../pages/interested-creators/InterestedCreatorsFranchisesYouPlayPage";
import { useAsync } from "../../utils";
import { NextRouter } from "next/router";
import { FranchisesYouPlayFormLabels } from "../../pages/interested-creators/franchises-you-play";
import { Control, Controller, useFormContext } from "react-hook-form";
import Search from "../Search";
import { Required } from "../FormRules/CreatorForm";
import classNames from "classnames";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import CheckboxCards from "../cards/CheckboxCards";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import InterestedCreatorsService, { InterestedCreator } from "../../src/api/services/InterestedCreatorsServices";
import { useDependency } from "@src/context/DependencyContext";

const PAGE_SIZE = 8;

type FranchisesOptions = {
  value: string;
  label: string;
  image: string;
  checked: boolean;
};

type SecondaryFranchiseInputsProps = {
  name: string;
  items: FranchisesOptions[];
  values: FranchiseItem[];
  control: Control;
  disabled: boolean;
};

const SecondaryFranchiseInputs: FC<SecondaryFranchiseInputsProps> = ({
  name,
  items = [],
  values = [],
  control,
  disabled
}) => {
  return (
    <>
      {disabled ? (
        <img
          alt="Franchise image"
          className={"secondary-franchise-image-unselected"}
          src={"/img/franchises-you-play/sec-franchise-unselected.png"}
        />
      ) : (
        !!items.length && (
          <Controller
            control={control}
            name={name}
            defaultValue={values}
            render={({ field }) => (
              <CheckboxCards
                errorMessage={""}
                readOnly={false}
                selectAlternateItem={false}
                {...field}
                items={items}
                disabled={disabled}
              />
            )}
          />
        )
      )}
    </>
  );
};

type InterestedCreatorsFranchisesYouPlayInputsProps = {
  franchises: FranchiseItem[];
  interestedCreator: InterestedCreator;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  rules: FranchisesFormRules;
  setPrimaryFranchise: (a: FranchiseItem) => void;
  primaryFranchise: FranchiseItem;
  onClose: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

const InterestedCreatorFranchisesYouPlayInputs = memo(function InterestedCreatorFranchisesYouPlayInputs({
  franchises,
  interestedCreator,
  franchisesYouPlayFormLabels,
  rules,
  setPrimaryFranchise,
  primaryFranchise,
  onClose,
  isPending,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: InterestedCreatorsFranchisesYouPlayInputsProps) {
  const [showLoadMore, setShowLoadMore] = useState<boolean>(false);
  const [isDisabledSubmit, setDisableSubmit] = useState<boolean>(true);
  const [franchiseOptions, setFranchiseOptions] = useState([]);
  const [selectedFranchise, setSelectedFranchise] = useState(null);
  const methods = useFormContext();
  const { control } = methods;

  const initialFranchise = useMemo(() => {
    if (interestedCreator.preferredFranchises) {
      const primaryFranchise = interestedCreator.preferredFranchises.find(
        (preferredFranchise) => preferredFranchise.type === "PRIMARY"
      );
      const selectedFranchise = franchises.find((franchise) => franchise.value === primaryFranchise?.id);
      if (INTERESTED_CREATOR_REAPPLY_PERIOD && selectedFranchise) setSelectedFranchise(selectedFranchise);
      return selectedFranchise;
    }
  }, [interestedCreator, franchises]);

  useEffect(() => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD && selectedFranchise) updatePrimaryCard(selectedFranchise);
  }, [selectedFranchise]);

  const secondaryFranchises = useMemo(() => {
    if (interestedCreator.preferredFranchises) {
      const preferredSecondaryFranchise = [];
      interestedCreator.preferredFranchises.map((secondaryFranchise) => {
        if (secondaryFranchise.type === "SECONDARY") {
          franchiseOptions.find((franchise) => {
            if (franchise.value === secondaryFranchise.id) {
              preferredSecondaryFranchise.push({
                value: franchise.value,
                label: franchise.label,
                image: franchise.image
              });
            }
          });
        }
      });
      return preferredSecondaryFranchise;
    }
  }, [interestedCreator, franchiseOptions]);

  const secondaryProps = {
    name: "secondaryFranchise",
    control: control,
    values: secondaryFranchises,
    disabled: initialFranchise === undefined && primaryFranchise === null,
    items: franchiseOptions.map((item) => {
      return {
        ...item,
        checked: secondaryFranchises?.filter((franchiseItem) => franchiseItem.value === item.value).length > 0
      };
    })
  };

  const addFranchiseOptions = () => {
    if (franchises.length > franchiseOptions.length) {
      if (franchises.length - franchiseOptions.length > PAGE_SIZE) {
        setFranchiseOptions(
          franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchiseOptions.length + PAGE_SIZE))
        );
      } else {
        setFranchiseOptions(franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchises.length)));
      }
    }
  };

  useEffect(() => {
    if (franchises.length > PAGE_SIZE) {
      setFranchiseOptions(franchises.slice(0, PAGE_SIZE));
    } else {
      setFranchiseOptions(franchises);
    }
  }, [franchises]);

  useEffect(() => {
    if (franchises.length > franchiseOptions.length) {
      setShowLoadMore(true);
    } else {
      setShowLoadMore(false);
    }
  }, [franchiseOptions, franchises]);

  const updatePrimaryCard = (selectedItem) => {
    setPrimaryFranchise(selectedItem);
    setDisableSubmit(false);
  };

  const buttons = useMemo(
    () => ({ cancel: franchisesYouPlayFormLabels.buttons.cancel, next: franchisesYouPlayFormLabels.buttons.submit }),
    [franchisesYouPlayFormLabels]
  );

  return (
    <>
      <div className="franchises-you-play-form">
        <div className="franchises-you-play">
          <div className="mg-primary-franchise-container">
            <div className="mg-primary-franchise">
              <h4 className="mg-primary-franchise-title">{franchisesYouPlayFormLabels.primaryFranchiseTitle}</h4>
              <div className="mg-primary-franchise-subtitle">
                {franchisesYouPlayFormLabels.primaryFranchiseSubTitle}
              </div>
            </div>
            {franchises && !!franchises.length && (
              <Controller
                control={control}
                name="primaryFranchise"
                rules={rules.primaryFranchise}
                defaultValue={initialFranchise}
                render={({ field, fieldState: { error } }) => (
                  <Search
                    errorMessage={(error && error.message) || ""}
                    disabled={false}
                    label={INTERESTED_CREATOR_REAPPLY_PERIOD && initialFranchise ? initialFranchise.label : ""}
                    {...field}
                    onChange={(item) => {
                      updatePrimaryCard(item);
                      field.onChange(item);
                    }}
                    options={franchises}
                    placeholder={franchisesYouPlayFormLabels.labels.primaryFranchise}
                  />
                )}
              />
            )}

            <div className="primary-franchise-option">
              <img
                alt="Selected Franchise image"
                className={classNames(
                  {
                    "primary-franchise-selected": primaryFranchise
                  },
                  "primary-franchise-option-image"
                )}
                src={
                  (primaryFranchise && primaryFranchise.image) ||
                  (initialFranchise && initialFranchise.image) ||
                  "/img/franchises-you-play/franchise-unselected.png"
                }
              />
            </div>
          </div>
          <div className="mg-sc-franchise-container">
            <div
              className={classNames({
                "mg-secondary-franchise-disabled": !primaryFranchise && !initialFranchise
              })}
            >
              <h4 className="mg-primary-franchise-title">{franchisesYouPlayFormLabels.secondaryFranchiseTitle}</h4>
              <div className="mg-primary-franchise-subtitle">
                {franchisesYouPlayFormLabels.secondaryFranchiseSubTitle}
              </div>
            </div>
            {secondaryProps && <SecondaryFranchiseInputs {...secondaryProps} />}
            {primaryFranchise && showLoadMore && (
              <div className="secondary-franchise-load-more">
                <Button variant="secondary" size="md" onClick={addFranchiseOptions}>
                  {franchisesYouPlayFormLabels.labels.loadMore}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer
        {...{
          buttons,
          onCancel: onClose,
          disableSubmit: isDisabledSubmit || isPending,
          isPending
        }}
      />
    </>
  );
});

export type FranchisesFormRules = {
  primaryFranchise: Required;
};

const franchisesFormRules = (franchisesYouPlayFormLabels): FranchisesFormRules => {
  return {
    primaryFranchise: {
      required: franchisesYouPlayFormLabels.messages.primaryFranchise
    }
  };
};

type InterestedCreatorsFranchisesYouPlayFormProps = {
  franchises: FranchiseItem[];
  interestedCreator: InterestedCreator;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  stableDispatch: () => void;
  onClose: () => void;
  router: NextRouter;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

const InterestedCreatorsFranchisesYouPlayForm: FC<InterestedCreatorsFranchisesYouPlayFormProps> = ({
  franchises,
  interestedCreator,
  franchisesYouPlayFormLabels,
  stableDispatch,
  onClose,
  router,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}) => {
  const { errorHandler } = useDependency();
  const [primaryFranchise, setPrimaryFranchise] = useState<FranchiseItem>(null);
  const rules: FranchisesFormRules = franchisesFormRules(franchisesYouPlayFormLabels);

  const defaultValues = useMemo(() => {
    const { preferredFranchises = undefined } = interestedCreator;
    return { preferredFranchises };
  }, [interestedCreator]);

  const updateInterestedCreator = useCallback(
    async (data) => {
      const interestedCreatorPayload = {
        ...interestedCreator,
        preferredFranchises: []
      };

      if (data.primaryFranchise) {
        interestedCreatorPayload.preferredFranchises.push({ id: data.primaryFranchise.value, type: "PRIMARY" });
      }
      if (data.secondaryFranchise) {
        data.secondaryFranchise.map((secondaryFranchise) => {
          const franchise = { id: secondaryFranchise.value, type: "SECONDARY" };
          interestedCreatorPayload.preferredFranchises.push(franchise);
        });
      }
      try {
        if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
          await InterestedCreatorsService.addRequestToJoinFor(interestedCreatorPayload);
        } else {
          await InterestedCreatorsService.saveInterestedCreatorInformation(interestedCreatorPayload);
        }
        analytics.continuedCreatorApplication({ locale: router.locale, page: location.pathname, finalStep: true });
        router.push("/interested-creators/complete");
      } catch (error) {
        errorHandler(stableDispatch, error);
      }
    },
    [interestedCreator, stableDispatch, router]
  );

  const { pending: isPending, execute: submitHandler } = useAsync(updateInterestedCreator, false);

  return (
    <Form mode="onChange" onSubmit={submitHandler} defaultValues={defaultValues}>
      <InterestedCreatorFranchisesYouPlayInputs
        {...{
          franchises,
          interestedCreator,
          franchisesYouPlayFormLabels,
          rules,
          setPrimaryFranchise,
          primaryFranchise,
          onClose,
          isPending,
          INTERESTED_CREATOR_REAPPLY_PERIOD
        }}
      />
    </Form>
  );
};

export default InterestedCreatorsFranchisesYouPlayForm;
