import { act, render, screen, waitFor } from "@testing-library/react";
import { ApplicationPendingPage } from "../../../../components/pages/interested-creators/ApplicationPendingPage";
import { axe } from "jest-axe";
import { applicationPendingTranslations } from "../../../translations";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { commonTranslations } from "../../../translations";

describe("ApplicationPendingPage", () => {
  const labels = applicationPendingTranslations;
  const locale = "en-us";
  const router = { locale: locale, push: jest.fn() };
  const analytics = { checkedApplicationStatus: jest.fn() } as unknown as BrowserAnalytics;
  const applicationPendingPageProps = {
    applicationPendingLabels: { ...labels.applicationPendingLabels, ...commonTranslations.applicantLabels },
    emailId: labels.emailId,
    defaultGamerTag: labels.defaultGamerTag,
    locale,
    analytics,
    router
  };

  it("shows interested creator application pending labels", async () => {
    render(<ApplicationPendingPage {...applicationPendingPageProps} analytics={analytics} />);

    expect(screen.getByText(labels.applicationPendingLabels.title)).toBeInTheDocument();
    expect(screen.getByText(labels.applicationPendingLabels.description)).toBeInTheDocument();
    expect(screen.getByText(labels.applicationPendingLabels.returnToCreatorNetwork)).toBeInTheDocument();
    expect(screen.getByText(applicationPendingPageProps.applicationPendingLabels.gamerTag)).toBeInTheDocument();
    expect(screen.getByText(applicationPendingPageProps.applicationPendingLabels.email)).toBeInTheDocument();
    expect(screen.getByText(applicationPendingPageProps.applicationPendingLabels.status)).toBeInTheDocument();
    expect(screen.getByText(labels.applicationPendingLabels.pending)).toBeInTheDocument();
    expect(screen.getByText(labels.emailId)).toBeInTheDocument();
    expect(screen.getByText(labels.defaultGamerTag)).toBeInTheDocument();
    await waitFor(() => {
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledWith({ locale: "en-us", status: "Pending" });
    });
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows application pending page when interested creator cannot re-apply", async () => {
      render(
        <ApplicationPendingPage
          {...applicationPendingPageProps}
          analytics={analytics}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 02, 2024"
        />
      );

      expect(screen.getByText(labels.applicationPendingLabels.submissionReceived)).toBeInTheDocument();
      expect(screen.getByText(labels.applicationPendingLabels.submissionReceivedDescription)).toBeInTheDocument();
      expect(screen.getByText(applicationPendingPageProps.applicationPendingLabels.submissionDate)).toBeInTheDocument();
      expect(screen.getByText(labels.applicationPendingLabels.unReviewed)).toBeInTheDocument();
      expect(screen.getByText("Jan 02, 2024")).toBeInTheDocument();
    });

    it("shows application pending page when interested creator can re-apply", async () => {
      render(
        <ApplicationPendingPage
          {...applicationPendingPageProps}
          analytics={analytics}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 03, 2024"
          canApply
        />
      );

      expect(screen.getByText(labels.applicationPendingLabels.submissionUpdate)).toBeInTheDocument();
      expect(screen.getByText(labels.applicationPendingLabels.submissionUpdateDescription)).toBeInTheDocument();
      expect(screen.getByText(applicationPendingPageProps.applicationPendingLabels.submissionDate)).toBeInTheDocument();
      expect(screen.getByText(labels.applicationPendingLabels.unReviewed)).toBeInTheDocument();
      expect(screen.getByText("Jan 03, 2024")).toBeInTheDocument();
      expect(screen.getByRole("link", { name: labels.applicationPendingLabels.reviewAndResubmit })).toBeInTheDocument();
    });

    it("shows link to update the submission", async () => {
      render(
        <ApplicationPendingPage
          {...applicationPendingPageProps}
          analytics={analytics}
          INTERESTED_CREATOR_REAPPLY_PERIOD
          submittedDate="Jan 03, 2024"
          canApply
        />
      );

      expect(screen.getByRole("link", { name: labels.applicationPendingLabels.reviewAndResubmit })).toHaveAttribute(
        "href",
        "/interested-creators/information"
      );
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationPendingPage {...applicationPendingPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
