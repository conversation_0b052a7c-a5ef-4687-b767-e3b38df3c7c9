import React, { <PERSON>, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Button, Input, Select } from "@eait-playerexp-cn/core-ui-kit";
import Form from "../../Form";
import { Controller, Message, useFormContext, ValidationRule } from "react-hook-form";
import { ERROR, isObj, isString, onToastClose, SUCCESS, useAsync } from "../../../utils";
import {
  ButtonLabels,
  ContentSubmissionLabels,
  ContentTypes,
  FileTypes,
  FormLabels
} from "./UploadContentSubmissionModal";
import validationConfig from "../../../config/validations/validationConfig";
import SubmittedContentService, {
  CONTENT_SCAN_SOURCE_TYPE_FILE_UPLOAD
} from "../../../src/api/services/SubmittedContentService";
import axios from "axios";
import { useAppContext } from "@src/context";
import { Toast, useToast } from "../../toast";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { useDependency } from "@src/context/DependencyContext";
import { fileTypeFromBlob } from "file-type";
import Textarea from "@components/textarea/TextArea";
import { ContentDetails } from "./ContentDeliverablesTab";

export type Required = {
  required: Message | ValidationRule<boolean>;
};

export type Validate = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  validate: (value: any) => any;
};

export type RequiredValidate = Required & Validate;

export type ContentSubmissionFormRules = {
  contentTitle: RequiredValidate;
  contentType: RequiredValidate;
  contentDescription?: RequiredValidate;
};

export type UploadContentSubmissionFormProps = {
  cancelUploadProgress: () => void;
  resetUploadForm: () => void;
  formLabels: FormLabels;
  contentTypes: ContentTypes;
  buttonsLabels: ButtonLabels;
  contentSubmissionLabels: ContentSubmissionLabels;
  participationId: string;
  thumbnail: string;
  fileTypes: FileTypes;
  showProgressBar: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setShowProgressBar: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  source: any;
  uploadPercentage: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setUploadPercentage: any;
  analytics: BrowserAnalytics;
  locale: string;
  opportunity: OpportunityWithDeliverables;
  setUpdatedContent: (boolean) => void;
  isForUpdateUploadedFile?: boolean;
  content?: ContentDetails;
  deliverableId?: string;
  deliverableTitle?: string;
  deliverableType?: string;
  creatorId: string;
  nucleusId?: number;
};

export type FormButtonLabels = {
  cancel: string;
  upload: string;
};

export type FormButtonsProps = {
  buttons: FormButtonLabels;
  close: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  isFileSelected: boolean;
};

export type UploadContentInputProps = {
  formLabels: FormLabels;
  contentTypes: ContentTypes;
  contentSubmissionLabels: ContentSubmissionLabels;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setIsFileSelected: any;
  fileTypes: FileTypes;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  selectedFile: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setSelectedFile: any;
  uploadPercentage: number;
  showProgressBar: boolean;
  isForUpdateUploadedFile?: boolean;
  content?: ContentDetails;
};

export const FormButtons = memo(function Footer({
  buttons: { cancel, upload },
  close,
  isPending,
  isFileSelected
}: FormButtonsProps) {
  const { formState } = useFormContext();

  return (
    <div className="content-submission-footer">
      <Button variant="tertiary" dark onClick={close} size="md">
        {cancel}
      </Button>

      <Button
        type="submit"
        spinner={isPending}
        size="md"
        disabled={
          Object.keys(formState.errors).length !== 0 || formState.isValid === false || isPending || !isFileSelected
        }
      >
        {upload}
      </Button>
    </div>
  );
});

export const UploadContentInput = memo(function UploadContentInput({
  formLabels,
  contentTypes,
  contentSubmissionLabels,
  setIsFileSelected,
  fileTypes,
  selectedFile,
  setSelectedFile,
  uploadPercentage,
  showProgressBar,
  isForUpdateUploadedFile,
  content
}: UploadContentInputProps) {
  const [isTypeSelected, setTypeSelected] = useState(false);
  const [fileErrorMessage, setFileErrorMessage] = useState("");
  const [acceptFileTypes, setAcceptFileTypes] = useState("");
  const TITLE_MAX_LENGTH = 950;
  const DESCRIPTION_MAX_LENGTH = 800;
  const { configuration: config } = useDependency();

  useEffect(() => {
    const fileTypesForUpload = isForUpdateUploadedFile
      ? [fileTypes.find((type) => type.type === content.contentType)]
      : fileTypes;
    const extensions = fileTypesForUpload
      .map((type) => type?.extensions)
      .join(",")
      ?.split(",")
      .join(", ");
    setAcceptFileTypes(extensions);
  }, [fileTypes]);

  const rules: ContentSubmissionFormRules = {
    contentTitle: {
      required: contentSubmissionLabels.contentTitleRequired,
      validate: (value) => {
        if (value?.trim() === "") return contentSubmissionLabels.contentTitleRequired;
        if (value?.length > TITLE_MAX_LENGTH) return contentSubmissionLabels.contentTitleLongMessage;
      }
    },
    contentType: {
      required: contentSubmissionLabels.contentTypeRequired,
      validate: (countryVal) => {
        const { label, name, value } = countryVal;
        if (!label && !name && !value && isObj(countryVal)) {
          return contentSubmissionLabels.contentTypeRequired;
        } else if (isString(countryVal) && !countryVal) {
          return contentSubmissionLabels.contentTypeRequired;
        }
        return true;
      }
    },
    ...(config.FLAG_SIGNED_URL_V2_ENABLED && {
      contentDescription: {
        required: contentSubmissionLabels.contentDescriptionRequired,
        validate: (value) => {
          if (value?.trim() === "") return contentSubmissionLabels.contentDescriptionRequired;
          if (value?.length > DESCRIPTION_MAX_LENGTH) return contentSubmissionLabels.contentDescriptionLongMessage;
        }
      }
    })
  };

  const methods = useFormContext();
  const { control } = methods;
  const onSelectChange = useCallback(
    (field) => (item) => {
      setFileErrorMessage("");
      if (item.value) {
        field.onChange(item);
        setTypeSelected(true);
        setSelectedFile(null);
        setIsFileSelected(false);
        const acceptedFormats = fileTypes.find((file) => file.type === item.value).extensions;
        setAcceptFileTypes(acceptedFormats.join(", "));
      } else {
        field.onChange("");
        setTypeSelected(false);
      }
    },
    []
  );

  const changeHandler = (event) => {
    const file = event.target.files[0];
    const names = file?.name.split(".");
    const extension = names[names.length - 1];
    if (!acceptFileTypes.includes(extension.toUpperCase())) {
      setFileErrorMessage(contentSubmissionLabels.invalidFileTypeMessage);
      return;
    }
    if (file?.size > validationConfig.MAX_FILE_SIZE) {
      setFileErrorMessage(contentSubmissionLabels.maxLimitMessage);
      return;
    }
    setFileErrorMessage("");
    setSelectedFile(file);
    setIsFileSelected(true);
  };

  const removeSelectedFile = () => {
    setIsFileSelected(false);
    setSelectedFile(null);
  };

  return (
    <div className="content-submission-form-elements">
      <Controller
        control={control}
        name="title"
        rules={rules.contentTitle}
        defaultValue={isForUpdateUploadedFile && content?.name}
        render={({ field, fieldState: { error } }) => (
          <Input
            errorMessage={error?.message || ""}
            {...field}
            label={formLabels.contentTitle}
            placeholder={formLabels.contentTitlePlaceholder}
            id="title"
            disabled={showProgressBar}
          />
        )}
      />
      <Controller
        control={control}
        name="type"
        rules={rules?.contentType}
        render={({ field, fieldState: { error } }) => (
          <Select
            id="content-type"
            errorMessage={error?.message}
            options={contentTypes}
            label={formLabels.contentType}
            onChange={onSelectChange(field)}
            disabled={showProgressBar}
            selectedOption={contentTypes.find((option) => option.value === content?.contentType)}
          />
        )}
      />
      {config.FLAG_SIGNED_URL_V2_ENABLED && (
        <Controller
          control={control}
          name="contentDescription"
          rules={rules.contentDescription}
          defaultValue={isForUpdateUploadedFile ? content?.contentDescription : ""}
          render={({ field, fieldState: { error } }) => (
            <Textarea
              id="contentDescription"
              errorMessage={error?.message || ""}
              {...field}
              label={formLabels.contentDescription}
              placeholder={formLabels.contentDescriptionPlaceholder}
              disabled={showProgressBar}
              maxcharacterLimit={formLabels.maxcharacterLimit}
            />
          )}
        />
      )}
      <div>
        <h6 className="content-submission-upload-label">{formLabels.fileUpload}</h6>
        {!selectedFile ? (
          <div className={`content-submission-file-upload ${isTypeSelected ? "active" : "inactive"}`}>
            <div className="content-submission-file-upload-content">
              <div>
                <input
                  type="file"
                  id="upload"
                  data-testid="upload"
                  name="upload"
                  hidden
                  onChange={changeHandler}
                  size={validationConfig.MAX_FILE_SIZE}
                  disabled={!isTypeSelected}
                />
                <label htmlFor="upload">
                  <div className="content-submission-choose-file">{formLabels.chooseFile}</div>
                </label>
              </div>
              <div>
                <span className="content-submission-no-choosen">{formLabels.noFileChoosen}</span>
                <div className="content-submission-file-formats">
                  {formLabels.acceptedFormats}: {acceptFileTypes}. {formLabels.maxFileSize} : 500 MB
                </div>
              </div>
            </div>
            {fileErrorMessage && <span className="form-error-message">{fileErrorMessage}</span>}
          </div>
        ) : (
          <div className="content-submission-file-selection">
            <div className="content-submission-file-selection-content">
              <div>
                <span className="content-submission-file-name">{selectedFile?.name}</span>
                <div className="content-submission-file">
                  <div className="content-submission-file-image">
                    <img src="../img/content-submission/uploadIcon.png" alt="" />
                  </div>
                  <span className="content-submission-file-selected">
                    {showProgressBar ? formLabels.fileUploading : formLabels.fileSelected}
                  </span>
                </div>
              </div>
              <button
                type="button"
                aria-label={formLabels.removeSelectedFile}
                className="content-submission-upload-delete"
                onClick={!showProgressBar ? removeSelectedFile : undefined}
              >
                <img
                  className={`content-submission-delete-image ${
                    showProgressBar ? "content-submission-delete-image-disabled" : ""
                  }`}
                  src="../img/content-submission/trashIcon.png"
                  alt=""
                />
              </button>
            </div>

            {showProgressBar && (
              <progress
                aria-label={formLabels.uploadFileProgress}
                className={`content-submission-file-progress ${
                  uploadPercentage === 100 ? "content-submission-file-progress-complete" : ""
                }`}
                value={uploadPercentage}
                max="100"
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
});

const UploadContentSubmissionForm: FC<UploadContentSubmissionFormProps> = ({
  cancelUploadProgress,
  resetUploadForm,
  formLabels,
  contentTypes,
  buttonsLabels,
  contentSubmissionLabels,
  participationId,
  thumbnail,
  fileTypes,
  showProgressBar,
  setShowProgressBar,
  source,
  uploadPercentage,
  setUploadPercentage,
  analytics,
  locale,
  opportunity,
  setUpdatedContent,
  isForUpdateUploadedFile,
  content,
  deliverableId,
  deliverableTitle,
  deliverableType,
  creatorId,
  nucleusId
}) => {
  const { errorHandler, configuration: config } = useDependency();
  const [isFileSelected, setIsFileSelected] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const { dispatch } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { success: successToast, error: errorToast } = useToast();
  const [isApiPending, setApiPending] = useState(false);
  const isSignedUrlEnabled = config.FLAG_SIGNED_URL_V2_ENABLED || config.FLAG_SIGNED_URL_V1_ENABLED;

  const onSubmitFileContent = async (uploadContent) => {
    try {
      let submitFileContentService;
      if (isForUpdateUploadedFile && !isSignedUrlEnabled) {
        submitFileContentService = SubmittedContentService.updateUploadedContent;
      } else if (isSignedUrlEnabled) {
        submitFileContentService = SubmittedContentService.markUploadComplete;
      } else {
        submitFileContentService = SubmittedContentService.submitUploadedContentDeliverable;
      }
      const serviceParam = isSignedUrlEnabled ? uploadContent.contentId : uploadContent;
      await submitFileContentService(serviceParam);
      resetUploadForm();
      analytics.submittedFileUpload({
        locale,
        opportunity,
        contentType: uploadContent.contentType,
        fileExtension: uploadContent.fileName?.split(".")[1],
        fileSize: selectedFile.size,
        deliverableTitle,
        deliverableType
      });
      setUpdatedContent(true);
      successToast(
        <Toast
          header={contentSubmissionLabels.success?.title}
          content={contentSubmissionLabels.success?.content}
          closeButtonAriaLabel={buttonsLabels.close}
        />,
        {
          onClose: () => onToastClose(SUCCESS, dispatch)
        }
      );
    } catch (e) {
      setApiPending(false);
      errorHandler(stableDispatch, e);
    }
  };

  const onSubmit = useCallback(
    async (data) => {
      const formattedFileName = data.title.replace(/[^\w\s]/gi, "");
      const fileName = formattedFileName.split(" ").join("-").toLowerCase();
      const extension = selectedFile.name.split(".").pop() || undefined;
      const contentType = data.type?.value.toLowerCase();
      const mimeType = contentType === "text" ? selectedFile.type : (await fileTypeFromBlob(selectedFile)).mime;
      const fileTitle = selectedFile && selectedFile.name;

      let percent = 0;
      setApiPending(true);
      setShowProgressBar(true);
      /** spliting uuid as the original submission from the content uri
       *  when isForUpdateUploadedFile is enabled */
      let originalFileId: string;
      if (isForUpdateUploadedFile && !config.FLAG_SIGNED_URL_V2_ENABLED && !config.FLAG_SIGNED_URL_V1_ENABLED) {
        const contentUri = new URL(content.contentUri);
        const path = contentUri.pathname.split("/");
        originalFileId = path[path.length - 2];
      }
      const signedURLRequestBody = {
        participationId,
        creatorId,
        deliverableId,
        fileName: fileTitle,
        title: data.title?.trim(),
        extension: `.${extension}`,
        contentType,
        mimeType,
        thumbnail,
        fileSize: selectedFile.size,
        programCode: config.PROGRAM_CODE
      };
      const getSignedUrl = async () => {
        if (config.FLAG_SIGNED_URL_V2_ENABLED) {
          delete signedURLRequestBody.title;
          const signedURLRequestBodyV2 = {
            ...signedURLRequestBody,
            nucleusId: String(nucleusId),
            contentTitle: data.title?.trim(),
            contentDescription: data.contentDescription?.trim(),
            contentScanSourceType: CONTENT_SCAN_SOURCE_TYPE_FILE_UPLOAD
          };
          return SubmittedContentService.getSignedUrlV2(signedURLRequestBodyV2);
        }
        return config.FLAG_SIGNED_URL_V1_ENABLED
          ? SubmittedContentService.getSignedUrlV1(signedURLRequestBody)
          : SubmittedContentService.getSignedUrl(participationId, `${fileName}.${extension}`, originalFileId);
      };
      const handleSignedUrlResponse = async (signedResponse) => {
        const configuration = {
          onUploadProgress: (progressEvent) => {
            const { loaded, total } = progressEvent;
            percent = Math.floor((loaded * 100) / total);
            setUploadPercentage(percent);
          },
          cancelToken: source.token
        };
        const configWithHeaders = {
          headers: {
            CONTENT_TYPE_HEADER: mimeType,
            "Content-Type": mimeType
          },
          ...configuration
        };
        const uploadContentV1 = {
          fileName: `${fileName}.${extension}`,
          contentType: data.type?.value.toLowerCase()
        };
        const contentId = signedResponse.data.id;
        const configurationV2 = isSignedUrlEnabled ? configWithHeaders : configuration;

        const uploadResponse = await axios.put(signedResponse.data.url, selectedFile, configurationV2);

        if (isSignedUrlEnabled) {
          await onSubmitFileContent({ ...uploadContentV1, contentId });
        } else {
          const uploadContent = {
            ...uploadContentV1,
            title: data.title?.trim(),
            versionId: uploadResponse.headers["x-amz-version-id"],
            thumbnail: thumbnail,
            id: signedResponse.data.fileId,
            participationId
          };
          await onSubmitFileContent({ ...uploadContent, deliverableId });
        }
      };

      try {
        const signedResponse = await getSignedUrl();
        await handleSignedUrlResponse(signedResponse);
      } catch (e) {
        setApiPending(false);
        setShowProgressBar(false);
        errorToast(
          <Toast
            header={contentSubmissionLabels.error?.title}
            content={contentSubmissionLabels.error?.content}
            closeButtonAriaLabel={buttonsLabels.close}
          />,
          {
            onClose: () => onToastClose(ERROR, dispatch)
          }
        );
        errorHandler(stableDispatch, e);
      }
    },
    [selectedFile]
  );
  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);
  return (
    <Form onSubmit={submitHandler} defaultValues={{}} mode="onChange">
      <UploadContentInput
        {...{
          formLabels,
          contentTypes,
          contentSubmissionLabels,
          setIsFileSelected,
          fileTypes,
          selectedFile,
          setSelectedFile,
          uploadPercentage,
          showProgressBar,
          isForUpdateUploadedFile,
          content,
          nucleusId
        }}
      />
      <FormButtons
        {...{
          buttons: { cancel: buttonsLabels.cancel, upload: buttonsLabels.upload },
          close: cancelUploadProgress,
          isPending:
            isPending ||
            isApiPending /* isPending is getting false in the first API call itself. We should show loader till third API call is getting resolved */,
          isFileSelected,
          showProgressBar
        }}
      />
    </Form>
  );
};

export default UploadContentSubmissionForm;
