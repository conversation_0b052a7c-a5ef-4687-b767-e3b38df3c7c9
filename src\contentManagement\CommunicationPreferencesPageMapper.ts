import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type CommunicationPreferencesPageLabels = {
  communicationPreferencesPageLabels: {
    title: string;
    profileTitle: string;
    description: string;
    success: {
      updatedInformationHeader: string;
      preferredEmail: string;
      preferredPhoneNumber: string;
      preferredLanguage: string;
      contentLanguage: string;
    };
    messages: {
      preferredEmail: string;
      preferredEmailTooLong: string;
      preferredEmailInvalid: string;
      preferredPhoneNumber: string;
      preferredPhoneNumberTooLong: string;
      contentLanguage: string;
      language: string;
    };
    labels: {
      addDiscord: string;
      discordTitle: string;
      discordDescription: string;
      preferredEmailAddressTitle: string;
      preferredEmailAddressDescription: string;
      preferredEmail: string;
      preferredPhoneNumberTitle: string;
      preferredPhoneNumber: string;
      contentLanguagesTitle: string;
      contentLanguagesDescription: string;
      contentLanguage: string;
      languageTitle: string;
      languageDescription: string;
      language: string;
    };
  };
};

export class CommunicationPreferencesPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): CommunicationPreferencesPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      communicationPreferencesPageLabels: {
        title: microCopy.get("communicationPreferences.title"),
        profileTitle: microCopy.get("communicationPreferences.profileTitle"),
        description: microCopy.get("communicationPreferences.description"),
        success: {
          updatedInformationHeader: microCopy.get("communicationPreferences.success.updatedInformationHeader"),
          preferredEmail: microCopy.get("communicationPreferences.success.preferredEmail"),
          preferredPhoneNumber: microCopy.get("communicationPreferences.success.preferredPhoneNumber"),
          preferredLanguage: microCopy.get("communicationPreferences.success.preferredLanguage"),
          contentLanguage: microCopy.get("communicationPreferences.success.contentLanguage")
        },
        messages: {
          preferredEmail: microCopy.get("communicationPreferences.messages.preferredEmail"),
          preferredEmailTooLong: microCopy.get("communicationPreferences.messages.preferredEmailTooLong"),
          preferredEmailInvalid: microCopy.get("communicationPreferences.messages.preferredEmailInvalid"),
          preferredPhoneNumber: microCopy.get("communicationPreferences.messages.preferredPhoneNumber"),
          preferredPhoneNumberTooLong: microCopy.get("communicationPreferences.messages.preferredPhoneNumberTooLong"),
          contentLanguage: microCopy.get("communicationPreferences.messages.contentLanguage"),
          language: microCopy.get("communicationPreferences.messages.language")
        },
        labels: {
          addDiscord: microCopy.get("communicationPreferences.labels.addDiscord"),
          discordTitle: microCopy.get("communicationPreferences.labels.discordTitle"),
          discordDescription: microCopy.get("communicationPreferences.labels.discordDescription"),
          preferredEmailAddressTitle: microCopy.get("communicationPreferences.labels.preferredEmailAddressTitle"),
          preferredEmailAddressDescription: microCopy.get(
            "communicationPreferences.labels.preferredEmailAddressDescription"
          ),
          preferredEmail: microCopy.get("communicationPreferences.labels.preferredEmail"),
          preferredPhoneNumberTitle: microCopy.get("communicationPreferences.labels.preferredPhoneNumberTitle"),
          preferredPhoneNumber: microCopy.get("communicationPreferences.labels.preferredPhoneNumber"),
          contentLanguagesTitle: microCopy.get("communicationPreferences.labels.contentLanguagesTitle"),
          contentLanguagesDescription: microCopy.get("communicationPreferences.labels.contentLanguagesDescription"),
          contentLanguage: microCopy.get("communicationPreferences.labels.contentLanguage"),
          languageTitle: microCopy.get("communicationPreferences.labels.languageTitle"),
          languageDescription: microCopy.get("communicationPreferences.labels.languageDescription"),
          language: microCopy.get("communicationPreferences.labels.language")
        }
      }
    };
  }
}
