import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
import { Toast, useToast } from "../../toast";
import FormTitle from "../../formTitle/FormTitle";

const tShirtSizes = [
  {
    value: "XS",
    label: "XS"
  },
  {
    value: "S",
    label: "S"
  },
  {
    value: "M",
    label: "M"
  },
  {
    value: "L",
    label: "L"
  },
  {
    value: "XL",
    label: "XL"
  },
  {
    value: "XXL",
    label: "XXL"
  },
  {
    value: "XXXL",
    label: "XXXL"
  }
];

const MiscellaneousForm = ({
  infoLabels,
  rules,
  additionalInformation,
  hardwarePartners,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(infoLabels.success.miscellaneous.length * 50, 2000), 7000);
  const [defaultHoodieSize, setDefaultHoodieSize] = useState({});

  useEffect(() => {
    setDefaultHoodieSize({
      value: additionalInformation.hoodieSize || "",
      label: additionalInformation.hoodieSize || "None"
    });
  }, [additionalInformation]);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={infoLabels.success.updatedInformationHeader}
            content={infoLabels.success.miscellaneous}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: { timetoDisplay }
          }
        )}
      <div className="miscellaneous-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.miscellaneous} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="miscellaneous-field-title" id="hoodie-size">
          {infoLabels.labels.tShirtSize}
        </div>
        <div className="miscellaneous-field">
          {!isEdit && (additionalInformation.hoodieSize || infoLabels.labels.none)}
          {isEdit && (
            <Controller
              control={control}
              name="hoodieSize"
              rules={rules.tShirtSize}
              defaultValue={additionalInformation.hoodieSize}
              render={({ field, formState: { error } }) => (
                <Select
                  id="hoodie-size" // label prop is not passed, so 'id' is being used for aria-labelledby
                  selectedOption={defaultHoodieSize}
                  errorMessage={error && error.message}
                  options={tShirtSizes}
                  onChange={(item) => {
                    field.onChange(item);
                  }}
                />
              )}
            />
          )}
        </div>

        <div className="miscellaneous-field-title">{infoLabels.labels.hardwarePartners}</div>
        <div className={`miscellaneous-field ${!isEdit && "hardware-partner-field"}`}>
          {!isEdit &&
            (additionalInformation.hardwarePartners.length
              ? additionalInformation.hardwarePartners.map((item, key) => (
                  <div className="hardware-partner-head" key={key}>
                    {item.label || item.name}
                  </div>
                ))
              : infoLabels.labels.none)}
          {isEdit && (
            <Controller
              control={control}
              name="hardwarePartners"
              defaultValue={additionalInformation.hardwarePartners}
              render={({ field, formState: { error } }) => (
                <MultiSelect
                  selectedOptions={field.value}
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  options={hardwarePartners}
                  placeholder={infoLabels.labels.hardwarePartners}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default MiscellaneousForm;
