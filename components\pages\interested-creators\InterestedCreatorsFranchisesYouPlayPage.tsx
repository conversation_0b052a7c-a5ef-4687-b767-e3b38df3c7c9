import React, { FC, useCallback, useEffect, useMemo, useState } from "react";
import { ERROR, onToastClose, toastContent, VALIDATION_ERROR } from "../../../utils";
import InterestedCreatorsFranchisesYouPlayForm from "../../forms/InterestedCreatorsFranchisesYouPlayForm";
import Loading from "../../Loading";
import {
  FranchisesYouPlayFormLabels,
  FranchisesYouPlayLabels
} from "../../../pages/interested-creators/franchises-you-play";
import { Toast } from "../../../components/toast";
import { NextRouter } from "next/router";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import CancelRegistrationModal from "./CancelRegistrationModal";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

export type FranchiseItem = {
  value: string;
  label: string;
  image: string;
};

type InterestedCreatorsFranchisesYouPlayPageProps = {
  interestedCreator: InterestedCreator;
  franchisesYouPlayLabels: FranchisesYouPlayLabels;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  stableDispatch: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  onClose: () => void;
  isError?: boolean;
  isValidationError?: boolean;
  errorToast: (JSXElementConstructor, CloseHandler) => void;
  unhandledError: string;
  router: NextRouter;
  locale: string;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

const InterestedCreatorsFranchisesYouPlayPage: FC<InterestedCreatorsFranchisesYouPlayPageProps> = ({
  interestedCreator,
  franchisesYouPlayLabels,
  franchisesYouPlayFormLabels,
  stableDispatch,
  showConfirmation,
  setShowConfirmation,
  onClose,
  isError,
  isValidationError,
  errorToast,
  unhandledError,
  router,
  locale,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}) => {
  const { errorHandler } = useDependency();
  const [franchises, setFranchises] = useState<FranchiseItem[]>([] as FranchiseItem[]);
  const { metadataClient } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);
  const handleCancelRegistration = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [locale, router]);

  const modalLabels = {
    title: franchisesYouPlayLabels.modalConfirmationTitle,
    yes: franchisesYouPlayLabels.buttons.yes,
    no: franchisesYouPlayLabels.buttons.no,
    close: franchisesYouPlayLabels.buttons.close,
    confirmationDesc1: franchisesYouPlayLabels.confirmationDesc1,
    confirmationDesc2: franchisesYouPlayLabels.confirmationDesc2
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const franchises = await metadataService.getFranchises();
        if (franchises) setFranchises(franchises);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={franchisesYouPlayFormLabels.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  return (
    <div className="interested-creator-franchises-container">
      <div className="mg-franchises-you-play">
        <h3 className="mg-franchises-you-play-title">{franchisesYouPlayLabels.title}</h3>
        <div className="mg-franchises-you-play-description">{franchisesYouPlayLabels.description}</div>
      </div>
      {franchises && interestedCreator && (
        <InterestedCreatorsFranchisesYouPlayForm
          interestedCreator={interestedCreator}
          franchises={franchises}
          franchisesYouPlayFormLabels={franchisesYouPlayFormLabels}
          stableDispatch={stableDispatch}
          onClose={onClose}
          router={router}
          analytics={analytics}
          INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        />
      )}
      {!franchises && (
        <div className="loader">
          <Loading />
        </div>
      )}
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: modalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </div>
  );
};

export default InterestedCreatorsFranchisesYouPlayPage;
