import { render, screen, waitFor } from "@testing-library/react";
import InterestedCreatorsCreatorTypeForm, {
  InterestedCreatorsCreatorTypeFormProps
} from "@components/forms/InterestedCreatorsCreatorTypeForm";
import userEvent from "@testing-library/user-event";
import { labels } from "../../translations";
import { NextRouter } from "next/router";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import InterestedCreatorsService from "@src/api/services/InterestedCreatorsServices";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/api/services/InterestedCreatorsServices");
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsCreatorTypeForm", () => {
  const locale = "en-us";
  const creatorTypes = [
    aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
    aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
    aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" }),
    aCreatorType({ label: "designer_artist", value: "DESIGNER_ARTIST" }),
    aCreatorType({ label: "blogger", value: "BLOGGER" }),
    aCreatorType({ label: "live_streamer", value: "LIVE_STREAMER" }),
    aCreatorType({ label: "podcaster", value: "PODCASTER" }),
    aCreatorType({ label: "cosplayer", value: "COSPLAYER" }),
    aCreatorType({ label: "animator", value: "ANIMATOR" }),
    aCreatorType({ label: "screenshoter", value: "SCREENSHOTER" }),
    aCreatorType({ label: "other", value: "OTHER" })
  ];
  const { creatorsTypeLabels } = labels;
  const { formLabels, pageLabels } = creatorsTypeLabels;
  const analytics = {} as unknown as BrowserAnalytics;
  const onClose = jest.fn();
  const t = jest.fn();
  const interestedCreator = {
    nucleusId: 0,
    defaultGamerTag: "",
    originEmail: "",
    dateOfBirth: undefined,
    contentUrls: [{ url: "", followers: "" }],
    creatorTypes: []
  };
  const router = { locale, push: jest.fn() } as unknown as NextRouter;
  const interestedCreatorsCreatorTypeFormProps: InterestedCreatorsCreatorTypeFormProps = {
    creatorTypes,
    onClose,
    t,
    formLabels: { ...formLabels, ...pageLabels },
    interestedCreator,
    router,
    analytics,
    stableDispatch: jest.fn()
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.resetAllMocks();
    t.mockImplementation((key) => key?.match(/(?<=\.)\w+/g) && key?.match(/(?<=\.)\w+/g)[0]);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (useDependency as jest.Mock).mockReturnValue({ errorHandler });
  });

  it("shows all existing creator types", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);

    await waitFor(() => {
      expect(screen.getAllByRole("checkbox")).toHaveLength(creatorTypes.length);
      expect(screen.getByTestId("youtuber")).not.toBeChecked();
      expect(screen.getByTestId("lifestyle")).not.toBeChecked();
      expect(screen.getByTestId("photographer")).not.toBeChecked();
      expect(screen.getByTestId("designer_artist")).not.toBeChecked();
      expect(screen.getByTestId("blogger")).not.toBeChecked();
      expect(screen.getByTestId("live_streamer")).not.toBeChecked();
      expect(screen.getByTestId("podcaster")).not.toBeChecked();
      expect(screen.getByTestId("cosplayer")).not.toBeChecked();
      expect(screen.getByTestId("animator")).not.toBeChecked();
      expect(screen.getByTestId("screenshoter")).not.toBeChecked();
      expect(screen.getByTestId("other")).not.toBeChecked();
    });
  });

  it("pre-populates checkboxes values", async () => {
    const interestedCreatorWithTypes = {
      ...interestedCreator,
      creatorTypes: [
        aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
        aCreatorType({ label: "lifestyle", value: "LIFESTYLE" })
      ]
    };
    render(
      <InterestedCreatorsCreatorTypeForm
        {...interestedCreatorsCreatorTypeFormProps}
        interestedCreator={interestedCreatorWithTypes}
      />
    );

    await waitFor(() => {
      expect(screen.getAllByRole("checkbox")).toHaveLength(creatorTypes.length);
      expect(screen.getByTestId("youtuber")).toBeChecked();
      expect(screen.getByTestId("lifestyle")).toBeChecked();
      expect(screen.getByTestId("photographer")).not.toBeChecked();
      expect(screen.getByTestId("designer_artist")).not.toBeChecked();
      expect(screen.getByTestId("blogger")).not.toBeChecked();
      expect(screen.getByTestId("live_streamer")).not.toBeChecked();
      expect(screen.getByTestId("podcaster")).not.toBeChecked();
      expect(screen.getByTestId("cosplayer")).not.toBeChecked();
      expect(screen.getByTestId("animator")).not.toBeChecked();
      expect(screen.getByTestId("screenshoter")).not.toBeChecked();
      expect(screen.getByTestId("other")).not.toBeChecked();
    });
  });

  it("enables next button on populating required values", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());

    await userEvent.click(screen.getByTestId("youtuber"));

    await waitFor(() => expect(nextButton).toBeEnabled());
  });

  it("display error message if no checkbox is selected", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    const youtuber = screen.getByTestId("youtuber");
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(youtuber);
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(youtuber);

    await waitFor(() => {
      expect(nextButton).toBeDisabled();
      expect(screen.getByText(/creatorTypes/i)).toBeInTheDocument();
    });
  });

  it("redirects to the next page in the application flow", async () => {
    (InterestedCreatorsService.saveApplication as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} analytics={analytics} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(screen.getByTestId("youtuber"));
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        creatorTypes: ["YOUTUBER"],
        finalStep: false,
        locale: locale,
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/interested-creators/franchises-you-play");
      expect(InterestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
    });
  });

  it("handles API error while submit creator types", async () => {
    (InterestedCreatorsService.saveApplication as jest.Mock).mockRejectedValue({
      response: {
        data: {
          statusCode: 401,
          message: "Unauthorized"
        }
      }
    });

    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} analytics={analytics} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(screen.getByTestId("youtuber"));
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(InterestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(1);
    });
  });
});
