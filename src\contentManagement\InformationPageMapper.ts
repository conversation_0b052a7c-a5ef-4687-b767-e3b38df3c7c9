import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type InformationPageLabels = {
  informationLabels: {
    messages: {
      firstName: string;
      emailTooLong: string;
      email: string;
      lastNameTooLong: string;
      state: string;
      tShirtSize: string;
      dateOfBirthInvalid: string;
      country: string;
      city: string;
      emailInvalid: string;
      ageMustBe18OrOlder: string;
      street: string;
      hardwarePartners: string;
      dateOfBirth: string;
      firstNameTooLong: string;
      businessNameTooLong: string;
      primaryPlatform: string;
      businessName: string;
      streetTooLong: string;
      stateTooLong: string;
      entityType: string;
      zipCode: string;
      zipCodeTooLong: string;
      url: string;
      cityTooLong: string;
      followersMaxLength: string;
      lastName: string;
    };
    labels: {
      state: string;
      hardwarePartners: string;
      city: string;
      contentFollowers: string;
      EAEmail: string;
      tShirtSize: string;
      country: string;
      contentFollowersPlaceholder: string;
      contentUrl: string;
      contentMediaTitle: string;
      none: string;
      contentLanguage: string;
      additionalLinkPlaceholder: string;
      lastName: string;
      preferredEmail: string;
      zipCode: string;
      business: string;
      individual: string;
      businessName: string;
      legalAddressAsMailingAddress: string;
      additionalContentAndWebsiteDescription: string;
      EAID: string;
      eaEmailID: string;
      duplicateUrl: string;
      urlScanFailed: string;
      invalidUrl: string;
      firstName: string;
      dateOfBirth: string;
      selectCountry: string;
      additionalContentAndWebsiteTitle: string;
      primaryPlatform: string;
      websiteUrlLabel: string;
      contentUrlPlaceholder: string;
      entityType: string;
      street: string;
      contentMediaDescription: string;
    };
    info: {
      businessName: string;
    };
    success: {
      miscellaneous: string;
      personalInformation: string;
      legalEntityType: string;
      updatedInformationHeader: string;
      mailingAddress: string;
      platformPreferences: string;
    };
    profilePicture: {
      termsAndConditionsLast: string;
      avatarRequired: string;
      message: string;
      termsAndConditionsFirst: string;
      title: string;
      avatarInvalid: string;
      avatarMoreThanLimit: string;
      termsAndConditionsMiddle: string;
    };
    miscellaneous: string;
    creatorSince: string;
    secondaryPlatformsTitle: string;
    interestedUserDescription2: string;
    legalEntityDescription: string;
    interestedUserDescription1: string;
    secondaryPlatforms: string;
    primaryPlatform: string;
    mailingAddress: string;
    legalEntityType: string;
    platformPreferencesTitle: string;
    description: string;
    platformPreferences: string;
    title: string;
    interestedUserDescription: string;
    infoSubTitle: string;
    infoTitle: string;
    interestedCreatorTitle: string;
    personalInformation: string;
    basicInformation: string;
    eaEmailID: string;
    informationPageTitle: string;
  };
};

export class InformationPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): InformationPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      informationLabels: {
        messages: {
          firstName: microCopy.get("information.messages.firstName"),
          emailTooLong: microCopy.get("information.messages.emailTooLong"),
          email: microCopy.get("information.messages.email"),
          lastNameTooLong: microCopy.get("information.messages.lastNameTooLong"),
          state: microCopy.get("information.messages.state"),
          tShirtSize: microCopy.get("information.messages.tShirtSize"),
          dateOfBirthInvalid: microCopy.get("information.messages.dateOfBirthInvalid"),
          country: microCopy.get("information.messages.country"),
          city: microCopy.get("information.messages.city"),
          emailInvalid: microCopy.get("information.messages.emailInvalid"),
          ageMustBe18OrOlder: microCopy.get("information.messages.ageMustBe18OrOlder"),
          street: microCopy.get("information.messages.street"),
          hardwarePartners: microCopy.get("information.messages.hardwarePartners"),
          dateOfBirth: microCopy.get("information.messages.dateOfBirth"),
          firstNameTooLong: microCopy.get("information.messages.firstNameTooLong"),
          businessNameTooLong: microCopy.get("information.messages.businessNameTooLong"),
          primaryPlatform: microCopy.get("information.messages.primaryPlatform"),
          businessName: microCopy.get("information.messages.businessName"),
          streetTooLong: microCopy.get("information.messages.streetTooLong"),
          stateTooLong: microCopy.get("information.messages.stateTooLong"),
          entityType: microCopy.get("information.messages.entityType"),
          zipCode: microCopy.get("information.messages.zipCode"),
          zipCodeTooLong: microCopy.get("information.messages.zipCodeTooLong"),
          url: microCopy.get("information.messages.url"),
          cityTooLong: microCopy.get("information.messages.cityTooLong"),
          followersMaxLength: microCopy.get("information.messages.followersMaxLength"),
          lastName: microCopy.get("information.messages.lastName")
        },
        labels: {
          state: microCopy.get("information.labels.state"),
          hardwarePartners: microCopy.get("information.labels.hardwarePartners"),
          city: microCopy.get("information.labels.city"),
          contentFollowers: microCopy.get("information.labels.contentFollowers"),
          EAEmail: microCopy.get("information.labels.EAEmail"),
          tShirtSize: microCopy.get("information.labels.tShirtSize"),
          country: microCopy.get("information.labels.country"),
          contentFollowersPlaceholder: microCopy.get("information.labels.contentFollowersPlaceholder"),
          contentUrl: microCopy.get("information.labels.contentUrl"),
          contentMediaTitle: microCopy.get("information.labels.contentMediaTitle"),
          none: microCopy.get("information.labels.none"),
          contentLanguage: microCopy.get("information.labels.contentLanguage"),
          additionalLinkPlaceholder: microCopy.get("information.labels.additionalLinkPlaceholder"),
          lastName: microCopy.get("information.labels.lastName"),
          preferredEmail: microCopy.get("information.labels.preferredEmail"),
          zipCode: microCopy.get("information.labels.zipCode"),
          business: microCopy.get("information.labels.business"),
          individual: microCopy.get("information.labels.individual"),
          businessName: microCopy.get("information.labels.businessName"),
          legalAddressAsMailingAddress: microCopy.get("information.labels.legalAddressAsMailingAddress"),
          additionalContentAndWebsiteDescription: microCopy.get(
            "information.labels.additionalContentAndWebsiteDescription"
          ),
          duplicateUrl: microCopy.get("information.messages.duplicateUrl"),
          urlScanFailed: microCopy.get("information.messages.urlScanFailed"),
          invalidUrl: microCopy.get("information.messages.invalidUrl"),
          EAID: microCopy.get("information.labels.EAID"),
          eaEmailID: microCopy.get("information.eaEmailID"),
          firstName: microCopy.get("information.labels.firstName"),
          dateOfBirth: microCopy.get("information.labels.dateOfBirth"),
          selectCountry: microCopy.get("information.labels.selectCountry"),
          additionalContentAndWebsiteTitle: microCopy.get("information.labels.additionalContentAndWebsiteTitle"),
          primaryPlatform: microCopy.get("information.labels.primaryPlatform"),
          websiteUrlLabel: microCopy.get("information.labels.websiteUrlLabel"),
          contentUrlPlaceholder: microCopy.get("information.labels.contentUrlPlaceholder"),
          entityType: microCopy.get("information.labels.entityType"),
          street: microCopy.get("information.labels.street"),
          contentMediaDescription: microCopy.get("information.labels.contentMediaDescription")
        },
        info: {
          businessName: microCopy.get("information.info.businessName")
        },
        success: {
          miscellaneous: microCopy.get("information.success.miscellaneous"),
          personalInformation: microCopy.get("information.success.personalInformation"),
          legalEntityType: microCopy.get("information.success.legalEntityType"),
          updatedInformationHeader: microCopy.get("information.success.updatedInformationHeader"),
          mailingAddress: microCopy.get("information.success.mailingAddress"),
          platformPreferences: microCopy.get("information.success.platformPreferences")
        },
        profilePicture: {
          termsAndConditionsLast: microCopy.get("information.profilePicture.termsAndConditionsLast"),
          avatarRequired: microCopy.get("information.profilePicture.avatarRequired"),
          message: microCopy.get("information.profilePicture.message"),
          termsAndConditionsFirst: microCopy.get("information.profilePicture.termsAndConditionsFirst"),
          title: microCopy.get("information.profilePicture.title"),
          avatarInvalid: microCopy.get("information.profilePicture.avatarInvalid"),
          avatarMoreThanLimit: microCopy.get("information.profilePicture.avatarMoreThanLimit"),
          termsAndConditionsMiddle: microCopy.get("information.profilePicture.termsAndConditionsMiddle")
        },
        miscellaneous: microCopy.get("information.miscellaneous"),
        creatorSince: microCopy.get("information.creatorSince"),
        secondaryPlatformsTitle: microCopy.get("information.secondaryPlatformsTitle"),
        interestedUserDescription2: microCopy.get("information.interestedUserDescription2"),
        legalEntityDescription: microCopy.get("information.legalEntityDescription"),
        interestedUserDescription1: microCopy.get("information.interestedUserDescription1"),
        secondaryPlatforms: microCopy.get("information.secondaryPlatforms"),
        primaryPlatform: microCopy.get("information.primaryPlatform"),
        mailingAddress: microCopy.get("information.mailingAddress"),
        legalEntityType: microCopy.get("information.legalEntityType"),
        platformPreferencesTitle: microCopy.get("information.platformPreferencesTitle"),
        description: microCopy.get("information.description"),
        platformPreferences: microCopy.get("information.platformPreferences"),
        title: microCopy.get("information.title"),
        interestedUserDescription: microCopy.get("information.interestedUserDescription"),
        infoSubTitle: microCopy.get("information.infoSubTitle"),
        infoTitle: microCopy.get("information.infoTitle"),
        interestedCreatorTitle: microCopy.get("information.interestedCreatorTitle"),
        personalInformation: microCopy.get("information.personalInformation"),
        basicInformation: microCopy.get("information.basicInformation"),
        eaEmailID: microCopy.get("information.eaEmailID"),
        informationPageTitle: microCopy.get("information.informationPageTitle")
      }
    };
  }
}
