import React, { FC, useEffect } from "react";
import Link from "next/link";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";

export type ApplicationAcceptedLabels = {
  title: string;
  descriptionPara1: string;
  descriptionPara2: string;
  descriptionPara3: string;
  returnToCreatorNetwork: string;
};
export type ApplicationAcceptedProps = {
  applicationAcceptedLabels: ApplicationAcceptedLabels;
  locale: string;
  analytics: BrowserAnalytics;
};

export const ApplicationAcceptedPage: FC<ApplicationAcceptedProps> = ({
  applicationAcceptedLabels,
  locale,
  analytics
}) => {
  const { title, descriptionPara1, descriptionPara2, descriptionPara3, returnToCreatorNetwork } =
    applicationAcceptedLabels;

  useEffect(() => {
    analytics.checkedApplicationStatus({ locale, status: "Accepted" });
  }, [locale]);

  return (
    <>
      <div className="mg-bg"> </div>
      <div className="mg-page">
        <div className="application-accepted-wrapper">
          <div className="application-accepted-thumbnail" />
          <div className="application-accepted-content-container">
            <h3 className="application-accepted-title">{title}</h3>
            <div className="application-accepted-descriptionPara1">{descriptionPara1}</div>
            <div className="application-accepted-descriptionPara2">{descriptionPara2}</div>
            <div className="application-accepted-descriptionPara3">{descriptionPara3}</div>
          </div>
          <div className="application-accepted-back-home">
            <Link href="/onboarding/information" type="submit" className="btn btn-primary btn-md">
              {returnToCreatorNetwork}
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};
