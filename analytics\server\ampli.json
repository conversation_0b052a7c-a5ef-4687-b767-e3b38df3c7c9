{"Zone": "us", "OrgId": "125444", "WorkspaceId": "5c1babc2-a107-4c5b-bb34-0db25f939002", "SourceId": "ae70f37e-5336-409e-b356-8fa1bd7494d4", "Runtime": "node.js:typescript-ampli", "Branch": "main", "Version": "35.0.0", "VersionId": "576bc349-2a0f-473a-acb9-5f2fa0d1425f", "Path": "./src/ampli", "OmitApiKeys": true, "Platform": "Node.js", "Language": "TypeScript", "SDK": "@amplitude/node@^1.0", "InstanceNames": ["analytics", "ampli"], "SourceDirs": ["../../src"]}