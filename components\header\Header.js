import HeaderWeb from "./HeaderWeb";
import HeaderMobile from "./HeaderMobile";
import TopNavBar from "./TopNavBar";
import { useEffect, useState } from "react";
import cx from "classnames";
import { useRouter } from "next/router";
import { useDetectScreen } from "../../utils";
import ProgramTopNavigation from "../ProgramTopNavigation";

const Header = ({
  labels,
  user,
  interestedCreator,
  notificationsLabels,
  analytics,
  FLAG_NEW_NAVIGATION_ENABLED = false
}) => {
  const isMobileOrTab = useDetectScreen(1279);
  // `useDetectScreen` uses its argument to determine current maximum screen resolution
  // 10, 000 should account for most, if not all, desktop resolutions
  const isWeb = useDetectScreen(10000);
  const [sticky, setSticky] = useState(false);
  const router = useRouter();
  const locale = router.locale;

  useEffect(() => {
    window.addEventListener("scroll", handleScrollPos);
    return () => window.removeEventListener("scroll", handleScrollPos);
  }, [isMobileOrTab, isWeb]);

  const handleScrollPos = () => {
    const headerMobile = document.getElementById("header-mobile");
    const headerWeb = document.getElementById("header-web");
    const topNavBar = document.getElementById("top-nav-bar");
    const header = isMobileOrTab ? headerMobile : isWeb && headerWeb;

    if (!header) return;

    if (window.scrollY > header.offsetTop) {
      topNavBar.classList.add("hide-nav-bar");
      setSticky(true);
    } else {
      topNavBar.classList.remove("hide-nav-bar");
      setSticky(false);
    }
  };

  return (
    <header data-testid="header-container" className="header-container">
      <TopNavBar {...{ locale, labels: { topNavigation: labels.topNavigation } }} />
      {/* Below method user.status === "ACTIVE" should be removed. isActive() method should be added in Identity  */}
      {FLAG_NEW_NAVIGATION_ENABLED && user?.status === "ACTIVE" ? (
        <ProgramTopNavigation analytics={analytics} notificationsLabels={notificationsLabels} />
      ) : isMobileOrTab ? (
        <nav className={cx("header-mobile", { sticky })} data-testid="header-mobile" id="header-mobile">
          <HeaderMobile
            labels={labels}
            user={user}
            interestedCreator={interestedCreator}
            notificationsLabels={notificationsLabels}
            analytics={analytics}
          />
        </nav>
      ) : (
        isWeb && (
          <nav className={cx("header-web", { sticky })} data-testid="header-web" id="header-web">
            <HeaderWeb
              labels={labels}
              user={user}
              interestedCreator={interestedCreator}
              notificationsLabels={notificationsLabels}
              analytics={analytics}
            />
          </nav>
        )
      )}
    </header>
  );
};
export default Header;
