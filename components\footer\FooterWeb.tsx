import { Icon, Select } from "@eait-playerexp-cn/core-ui-kit";
import { memo } from "react";
import eaLogo from "../icons/EaLogo";
import { useRouter } from "next/router";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export type SelectedItemProps = {
  label: string;
  locale: string;
  value: string;
};

export type FooterWebProps = {
  onChange: (value: SelectedItemProps) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  labels: any;
  locale: string;
  analytics: BrowserAnalytics;
};

export default memo(function FooterWeb({ onChange, options, labels, locale, analytics }: FooterWebProps) {
  const router = useRouter();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { value = locale }: any = (typeof window !== "undefined" && JSON.parse(localStorage.getItem("locale"))) || {};

  const navigateToMarketingPage = (url) => {
    analytics.clickedFooterLink({ locale: router.locale, url });
    router.push(url);
  };

  return (
    <>
      <div className="footer-v1-web-container">
        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-web-grid-row1">
            <Icon icon={eaLogo} width="3.375rem" height="3.375rem" />
          </div>
          <div className="footer-v1-web-grid-row2">
            <div>
              <p className="footer-v1-links-container">
                <span
                  className="footer-v1-title-link"
                  role="button"
                  onClick={() => navigateToMarketingPage("/how-it-works")}
                >
                  {labels.how}
                </span>
                <span
                  className="footer-v1-title-link"
                  role="button"
                  onClick={() => navigateToMarketingPage("/opportunities-rewards")}
                >
                  {labels.perks}
                </span>
                <span className="footer-v1-title-link" role="button" onClick={() => navigateToMarketingPage("/faq")}>
                  {labels.faqs}
                </span>
                <span
                  className="footer-v1-title-link"
                  role="button"
                  onClick={() => navigateToMarketingPage("/trust-and-safety-guidelines")}
                >
                  {labels.policy}
                </span>
                <span
                  className="footer-v1-title-link"
                  role="button"
                  onClick={() => navigateToMarketingPage("/disclosure")}
                >
                  {labels.disclosure}
                </span>
              </p>
            </div>
          </div>

          <div className="footer-v1-web-grid-row3">
            <Select
              id="site-locale"
              showIcon
              onChange={onChange}
              options={options}
              selectedOption={options.find((option) => option.value === value)}
              skipOnChangeDuringRender
            />
          </div>
        </div>

        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-web-grid-row4 ">
            <p className="footer-v1-web-links">
              <a
                className="footer-v1-small-title-link"
                href="https://www.ea.com/legal-notices"
                target="_blank"
                rel="noreferrer"
              >
                {labels.legal}
              </a>
              <a
                className="footer-v1-small-title-link4"
                href="http://www.ea.com/1/product-eulas"
                target="_blank"
                rel="noreferrer"
              >
                {labels.disclaimer}
              </a>
              <a
                className="footer-v1-small-title-link"
                href="http://www.ea.com/1/service-updates"
                target="_blank"
                rel="noreferrer"
              >
                {labels.updates}
              </a>
            </p>
          </div>
          <div className="footer-v1-web-grid-row5">
            <p className="footer-v1-web-links2">
              <a
                className="footer-v1-small-title-link"
                href="http://tos.ea.com/legalapp/WEBTERMS/US/en/PC/"
                target="_blank"
                rel="noreferrer"
              >
                {labels.terms}
              </a>
              <a
                className="footer-v1-small-title-link2"
                href={`https://www.ea.com/privacy-policy?isLocalized=true&setLocale=${locale}`}
                target="_blank"
                rel="noreferrer"
              >
                {labels.privacy}
              </a>
              <span className="footer-v1-small-title-link6">{` (${labels.rights}) `}</span>
              <a
                className="footer-v1-small-title-link4 footer-v1-small-title-link5"
                href={`https://help.ea.com/${locale}/help-contact-us/?product=origin&topic=report-toxicity&category=report-concerns-or-harassment&subCategory=report-player`}
                target="_blank"
                rel="noreferrer"
              >
                {labels.report}
              </a>
            </p>
          </div>
          <hr className="footer-v1-web-grid-row6" />

          <div className="footer-v1-web-grid-row7">
            <p className="footer-v1-web-hallmark">
              &#169;<span> {LocalizedDate.year()} Electronic Arts Inc.</span>
            </p>
          </div>
        </div>
      </div>
    </>
  );
});
