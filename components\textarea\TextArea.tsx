import classNames from "classnames";
import { forwardRef, RefObject, TextareaHTMLAttributes, useState } from "react";

interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  placeholder: string;
  label: string;
  errorMessage: string;
  disabled?: boolean;
  maxcharacterLimit?: string;
}

const TextArea = (
  { placeholder, label, errorMessage, value = "", disabled = false, maxcharacterLimit, ...props }: TextareaProps,
  ref: RefObject<HTMLTextAreaElement>
) => {
  const [focused, setFocused] = useState(false);
  const decodeHtmlContent = (input: string) => {
    const textarea = document.createElement("textarea");
    textarea.innerHTML = input;
    return textarea.value;
  };

  return (
    <label className="form-textarea-box">
      <div className="form-textarea-label-box">
        {label && <div className="form-textarea-label">{label}</div>}
        {maxcharacterLimit && <div className="form-textarea-character-count">{maxcharacterLimit}</div>}
      </div>
      <textarea
        ref={ref}
        onFocusCapture={() => setFocused(true)}
        onBlurCapture={() => setFocused(false)}
        className={classNames(
          {
            "form-textarea-input-box-error": errorMessage,
            "form-textarea-input-box-focused": focused,
            "form-textarea-input-box-disabled": disabled
          },
          "form-textarea-field"
        )}
        placeholder={placeholder}
        {...props}
        disabled={disabled}
        value={decodeHtmlContent(value as string)}
      />
      <div>{errorMessage && <div className="form-error-message">{errorMessage}</div>}</div>
    </label>
  );
};

export default forwardRef(TextArea);
