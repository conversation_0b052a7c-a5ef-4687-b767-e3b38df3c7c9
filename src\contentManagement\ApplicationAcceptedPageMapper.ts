import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ApplicationAcceptedPageLabels = {
  applicationAcceptedLabels: {
    descriptionPara3: string;
    descriptionPara1: string;
    returnToCreatorNetwork: string;
    descriptionPara2: string;
    pageTitle: string;
    title: string;
    completeYourProfile: string;
  };
};

export class ApplicationAcceptedPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ApplicationAcceptedPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      applicationAcceptedLabels: {
        descriptionPara3: microCopy.get("applicationAccepted.descriptionPara3"),
        descriptionPara1: microCopy.get("applicationAccepted.descriptionPara1"),
        returnToCreatorNetwork: microCopy.get("applicationAccepted.returnToCreatorNetwork"),
        descriptionPara2: microCopy.get("applicationAccepted.descriptionPara2"),
        pageTitle: microCopy.get("applicationAccepted.pageTitle"),
        title: microCopy.get("applicationAccepted.title"),
        completeYourProfile: microCopy.get("applicationAccepted.completeYourProfile")
      }
    };
  }
}
