import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../../../src/ApiContainer";
import { createRouter } from "next-connect";
import User from "../../../../src/authentication/User";
import JoinOpportunityAction from "../../../../src/actions/JoinOpportunity/JoinOpportunityAction";
import JoinOpportunityInput from "../../../../src/actions/JoinOpportunity/JoinOpportunityInput";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(JoinOpportunityAction);

    const user = config.FLAG_PER_PROGRAM_PROFILE ? (req.session.identity as User) : (req.session.user as User);

    const opportunityId = req.query.id as string;

    const participation = await action.execute(new JoinOpportunityInput(user.id, opportunityId, req.body));

    res.status(201).json(participation);
    res.end();
  });

export default router.handler({ onError });
