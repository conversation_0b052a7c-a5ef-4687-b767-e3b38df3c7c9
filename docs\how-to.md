---
currentMenu: howto
---

# How to

- [Add new pages](how-to/add-new-pages.md)
- [Implement page components](how-to/implement-page-components.md)
- [Structure form components](how-to/structure-form-components.md)
- [Write effective tests](how-to/testing.md)
- [Work with Amplitude Events](how-to/work-with-amplitude-events.md)
- [Scaffold an API route](how-to/scaffold-api.md)
- [Import CSS from Node modules](#import-css-from-node-modules)
- [Generate SVG icon components](#generate-svg-icon-components)

## Import CSS from Node modules

CSS coming from a third-party packages cannot be imported using a CSS `@import`.
CSS from modules must be imported from `_app.js` with a ES6 `import` as shown in the snippet below.

```tsx
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
```

## Generate SVG icon components

```bash
npx @svgr/cli --template components/templates/Svgr.js public/icons --ignore-existing --out-dir components/icons --icon --replace-attr-values "#767676=currentColor"
```

## Use mock APIs in your local environment

> > **Deprecated**. We're looking for a simpler setup, and it's very unlikely APIs won't be ready before implementation

You'll have to download the [Open API spec for the Operations API](https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations-api/docs/api.html) and save it as `operations-api.yaml` in the root fo your project.

You'll also will have to download the [Open API spec for the Metadata API](https://eait-playerexp-cn.gitlab.ea.com/cn-crm/cn-crm-api-docs/api.html) and save it as `metadata-API.v1.yaml` in the root of your project.
