import { Icon, Select } from "@eait-playerexp-cn/core-ui-kit";
import { memo } from "react";
import Ea<PERSON><PERSON> from "../icons/EaLogo";
import { useRouter } from "next/router";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import { SelectedItemProps } from "./FooterWeb";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export type FooterMobileProps = {
  onChange: (value: SelectedItemProps) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  labels: any;
  locale: string;
  analytics: BrowserAnalytics;
};

export default memo(function FooterMobile({ onChange, options, labels, locale, analytics }: FooterMobileProps) {
  const router = useRouter();
  const navigateToMarketingPage = (url) => {
    analytics.clickedFooterLink({ locale: router.locale, url });
    router.push(url);
  };

  return (
    <>
      <div className="footer-v1-mobile-container">
        <div className="footer-v1-row-mobile-1">
          <div className="footer-v1-mobile-grid-row1">
            <Icon icon={EaLogo} width="3rem" height="3rem" className="footer-v1-mobile-ea-icon" />
          </div>

          <div className="footer-v1-mobile-grid-row2">
            <Select
              id="site-locale"
              showIcon
              onChange={onChange}
              options={options}
              selectedOption={options.find((option) => option.value === locale)}
              skipOnChangeDuringRender
            />
          </div>
        </div>

        <div className="footer-v1-grid-rows-container">
          <p className="footer-v1-mobile-grid-row3">
            <span
              className="footer-v1-title-link"
              role="button"
              onClick={() => navigateToMarketingPage("/how-it-works")}
            >
              {labels.how}
            </span>
            <span
              className="footer-v1-title-link"
              role="button"
              onClick={() => navigateToMarketingPage("/opportunities-rewards")}
            >
              {labels.perks}
            </span>
          </p>
        </div>

        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-mobile-grid-row4">
            <p className="footer-v1-links-container">
              <a
                className="footer-v1-small-title-link"
                href="https://www.ea.com/legal-notices"
                target="_blank"
                rel="noreferrer"
              >
                {labels.legal}
              </a>
              <a
                className="footer-v1-small-title-link"
                href="http://www.ea.com/1/product-eulas"
                target="_blank"
                rel="noreferrer"
              >
                {labels.disclaimer}
              </a>
            </p>
          </div>
        </div>

        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-mobile-grid-row4">
            <p>
              <a
                className="footer-v1-small-title-link"
                href="http://www.ea.com/1/service-updates"
                target="_blank"
                rel="noreferrer"
              >
                {labels.updates}
              </a>
            </p>
          </div>
        </div>
        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-mobile-grid-row4">
            <p className="footer-v1-links-container">
              <a
                className="footer-v1-small-title-link"
                href="http://tos.ea.com/legalapp/WEBTERMS/US/en/PC/"
                target="_blank"
                rel="noreferrer"
              >
                {labels.terms}
              </a>
            </p>
          </div>
        </div>
        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-mobile-grid-row4">
            <p className="footer-v1-links-container">
              <a
                className="footer-v1-small-title-link2"
                href={`https://www.ea.com/privacy-policy?isLocalized=true&setLocale=${locale}`}
                target="_blank"
                rel="noreferrer"
              >
                {labels.privacy}
              </a>
              <span className="footer-v1-small-title-link6"> &nbsp;{` (${labels.rights}) `}</span>
            </p>
          </div>
        </div>
        <div className="footer-v1-grid-rows-container">
          <div className="footer-v1-mobile-grid-row4">
            <p className="footer-v1-links-container">
              <a
                className="footer-v1-small-title-link"
                href="https://help.ea.com/en/help-contact-us/?product=origin&topic=report-toxicity&category=report-concerns-or-harassment&subCategory=report-player"
                target="_blank"
                rel="noreferrer"
              >
                {labels.report}
              </a>
            </p>
          </div>
        </div>
        <div className="footer-v1-grid-rows-container">
          <hr className="footer-v1-mobile-grid-row5" />
        </div>
        <div className="footer-v1-grid-rows-container ">
          <div className="footer-v1-mobile-grid-row6">
            <p className="footer-v1-web-hallmark">
              &#169;<span> {LocalizedDate.year()} Electronic Arts Inc.</span>
            </p>
          </div>
        </div>
      </div>
    </>
  );
});
