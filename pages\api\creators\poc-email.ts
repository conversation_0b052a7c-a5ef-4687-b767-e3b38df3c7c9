import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import ApiContainer from "../../../src/ApiContainer";
import SendPOCEmailAction from "../../../src/actions/Creators/SendPOCEmail/SendPOCEmailAction";
import User from "../../../src/authentication/User";
import SendPOCEmailInput from "../../../src/actions/Creators/SendPOCEmail/SendPOCEmailInput";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(SendPOCEmailAction);
    const user = config.FLAG_PER_PROGRAM_PROFILE ? User.from(req.session.identity as User) : (req.session.user as User);

    await action.execute(new SendPOCEmailInput(user.id, req.body));

    res.status(201).end();
  });

export default router.handler({ onError });
