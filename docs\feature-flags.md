---
currentMenu: flags
---

# Feature Flags

## FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED

This flag allows creators to submit new content types from Instagram: Photos and Carousel photos

It affects the following logic

- Uses [new endpoint](https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramMultipleMediaSupport) to submit content
- Updates error message for non supported content types
- Updates help text to inform abut the new supported types

## FLAG_COUNTRIES_BY_TYPE

This flag allows to fetch configured countries and all the countries based on the value passed in query parameter "type".

- Uses [new endpoint](https://eait-playerexp-cn.gitlab.ea.com/cn-services/metadata-api/docs/api.html#tag/Metadata/operation/getConfiguredCountries) to fetch the list of countries.

It affects the following pages

- `my-profile`, `interested-creator` and `onboarding`.

## FLAG_OPPORTUNITIES_API_CLIENT

This flag, when on, will call the new [Opportunity API endpoints](https://eait-playerexp-cn.gitlab.ea.com/cn-services/opportunities/opportunities-api/docs/api.html) instead of the same endpoints in the Operations API.

Once all endpoints related to opportunities are moved to the new Opportunities API, all the duplicated code will be removed from the Operations API.

If this flag is turned on, `"opportunityClient"` is injected to following HTTP Clients

- `OpportunitiesHttpClient`, `ParticipationsHttpClient`, `OpportunityRegistrationsHttpClient`, `EmailsHttpClient`

If it is turned off, `"operationsClient"` is injected to above HTTP clients.

## FLAG_COMMUNICATIONS_API_CLIENT

This flag, when on, will call the new [Communications API endpoints](https://eait-playerexp-cn.gitlab.ea.com/cn-services/communications/communications-api/docs/api.html) instead of the same endpoints in the Operations API.

Once all endpoints related to Discord and emails are moved to the Communications API, all the duplicated code will be removed from the Operations API.

If this flag is turned on, `"communicationsClient"` is injected to following new HTTP Clients

- `EmailsHttpClient`, `DiscordAccountHttpClient`

If it is turned off, `"operationsClient"` is injected to above HTTP clients.

## FLAG_NEW_FOOTER_ENABLED

This flag, when on, will render the new `Footer` component from core ui kit. It will take links and CTA's as props.

If it is turned off, the existing legacy component will be rendered.

## FLAG_NEW_NAVIGATION_ENABLED

This flag, when on, will render the new `TopNavigation` and `SideNavigation` component from core ui kit to display Top and Side panel respectively. They will take links and CTA's as props.

If it is turned off, the only legacy top navigation will be rendered. There is no exisiting side navigation.

## FLAG_SIGNED_URL_V1_ENABLED

This flag, when on, will call the new endpoints [signed-urls](https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/viewContentUploadPreSignedUrl) and [upload-complete](https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/markUploadComplete) in `content-scanning-api`, instead of existing endpoints.

## FLAG_SIGNED_URL_V2_ENABLED

This flag, when on, will use the V2 endpoints for signed URLs with enhanced features. The V2 implementation adds support for additional metadata including:

- Nucleus ID
- Content title and description
- Content scan source type identification

The V2 endpoint `/api/v2/signed-urls` provides improved scanning capabilities and metadata handling compared to V1.

If both this flag and `FLAG_SIGNED_URL_V1_ENABLED` are turned off, the system will fall back to the original implementation in `content-submission-api`.

If it is turned off, signed-urls api and upload complete api in Content-deliverables tab will point to existing endpoints in `content-submission-api`.

## FLAG_ONBOARDING_CUSTOM_LINKS

This flag, when on, will include the Custom links section in the Information step page of OnBoarding flow, where Creator can add Additional Content and Website Links.

## FLAG_CONTENT_WITH_FINAL_REMARK

This flag, when on, will call the new end point V5 version for submitted-content api (https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/viewSubmittedContentWithReviewComments).

If it is turned off, will call the old end point api V4 version for submitted-content.

## FLAG_SUBMITTED_CONTENT_WITH_PROGRAM

This flag when on, will call the new end point V6 version for submitted-content api (https://dev-services.cn.ea.com/cn-content-submission-api/swagger-ui/index.html?configUrl=/cn-content-submission-api/v3/api-docs/swagger-config#/view-submitted-content-controller/viewSubmittedContentWithProgramCode).

If it is turned off, will call the old end point api V5 version for submitted-content.

## FLAG_PER_PROGRAM_PROFILE

When enabled this flag will use the new middleware and request handler from the [@eait-playerexp-cn/identity](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/authentication/identity) package, instead of the equivalent classes from the [@eait-playerexp-cn/server-kernel](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/shared-kernel/server-kernel) package.

## FLAG_OPPORTUNITIES_PER_PROGRAM

1. This flag when on, will call the new end point V5 version for search opportunities api (https://dev-services.cn.ea.com/cn-opportunities-api/swagger-ui/index.html?configUrl=/cn-opportunities-api/v3/api-docs/swagger-config#/search-opportunities-controller/searchOpportunitiesWithProgramCode).

If it is turned off, will call the old end point api V4 version for search opportunities.

2. This flag is also used to call the list of opportunities based on PAST/INVITED/JOINED.
   (https://dev-services.cn.ea.com/cn-opportunities-api/swagger-ui/index.html?configUrl=/cn-opportunities-api/v3/api-docs/swagger-config#/view-participations-controller/viewParticipationsWithProgramCode)

If it is turned off, will call the old end point api V4 version for list of opportunities(PAST, INVITED, JOINED).

## FLAG_SEND_EMAIL_WITH_PROGRAM

This flag when on, will call the new [v2 endpoint](https://dev-services.cn.ea.com/cn-communications-api/swagger-ui/index.html?configUrl=/cn-communications-api/v3/api-docs/swagger-config#/send-email-controller/sendEmailWithCreatorProgram) to send email for a specific program.

If it is turned off, will call the old end point api V1 version for send email.
