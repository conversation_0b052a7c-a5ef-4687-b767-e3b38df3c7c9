import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import UploadCreatorAvatarAction from "../../src/actions/Creators/Avatar/UploadCreatorAvatarAction";
import AvatarInput from "../../src/actions/Creators/Avatar/AvatarInput";
import User from "../../src/authentication/User";
import CreatorsWithPayableStatusHttpClient from "../../src/creators/CreatorsWithPayableStatusHttpClient";
import parseUploadFiles from "../../src/middleware/ParseUploadedFiles";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithMultipartFile
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import configuration from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithMultipartFile, NextApiResponse>();

// `config` excludes a body parser since the `parseUploadFiles` middleware will do the job
export const config = {
  api: {
    bodyParser: false
  }
};

router
  .use(session)
  .use(configuration.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(configuration.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .use(parseUploadFiles)
  .post(async (req: NextApiRequestWithMultipartFile, res: NextApiResponse) => {
    if (!req.files.avatar || req.error) {
      res.status(400).end(); // TODO: we should return an API Problem as usual
      return;
    }

    const action = ApiContainer.get(UploadCreatorAvatarAction);
    const creator = req.session.user as User;
    const user = User.from(creator);
    await action.execute(new AvatarInput(user.id as string, req.files.avatar[0]));

    const creators = ApiContainer.get(CreatorsWithPayableStatusHttpClient);
    const updatedCreator = await creators.withId(user.id);
    user.updateAvatar(updatedCreator?.avatar);
    req.session.user = user;
    req.session.save();

    res.status(201).end();
  });

export default router.handler({ onError });
