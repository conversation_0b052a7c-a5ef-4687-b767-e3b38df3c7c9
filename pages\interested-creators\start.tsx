import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsInterestedCreatorStartPage from "../../config/translations/interested-creator-start-page";
import labelsCommon from "../../config/translations/common";
import labelsIndex from "../../config/translations/index";
import labelsInformation from "../../config/translations/information";
import InterestedCreatorsStartPage from "../../components/pages/interested-creators/InterestedCreatorsStartPage";
import { useAppContext } from "@src/context";
import Error from "../_error";
import { useToast } from "../../components/toast";
import flags from "../../utils/feature-flags";
import { useRouter } from "next/router";
import { PageProps } from "@components/pages/interested-creators/ExplorePages";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody } from "../../components/Layout";
import Loading from "../../components/Loading";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import applicationStartPageProps from "@src/serverprops/ApplicationStartPageProps";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { useDependency } from "@src/context/DependencyContext";

export type PageLabels = {
  title: string;
  subTitle: string;
  description: string;
  descriptionSuffix: string;
  button: string;
  alreadyApplied: string;
  alreadyAppliedSuffix: string;
  explore: string;
  close: string;
};

export type StartProps = {
  runtimeConfiguration?: Record<string, unknown>;
};

export default function Start(): JSX.Element {
  const { analytics } = useDependency();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { dispatch, state, state: { exceptionCode = null, sessionUser = null, isLoading } = {} } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const { error: errorToast } = useToast();
  const { t } = useTranslation(["common", "interested-creator-start-page", "information", "index"]);
  const { completeLabels, unhandledError } = useMemo(() => {
    const {
      header: { creatorNetwork, how },
      buttons: { yes, no, close },
      main: { unhandledError }
    } = labelsCommon(t);
    const { howItWorks } = labelsIndex(t);
    const infoLabels = labelsInformation(t);
    const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle, interestedCreatorTitle } = infoLabels;
    const { leftTitle, rightTitle } = howItWorks;
    return {
      completeLabels: {
        creatorNetwork,
        ...labelsInterestedCreatorStartPage(t),
        rightTitle,
        leftTitle,
        how,
        yes,
        no,
        confirmationDesc1,
        confirmationDesc2,
        modalConfirmationTitle,
        interestedCreatorTitle,
        close
      },
      unhandledError
    };
  }, [t]);
  const {
    title,
    subTitle,
    description,
    descriptionSuffix,
    buttons: { applyNow },
    alreadyApplied,
    alreadyAppliedSuffix,
    creatorNetwork,
    leftTitle,
    rightTitle,
    perks,
    how,
    explore,
    interestedCreatorTitle,
    close
  } = completeLabels;

  const pageLabels: PageLabels = {
    title,
    subTitle,
    description,
    descriptionSuffix,
    button: applyNow,
    alreadyApplied,
    alreadyAppliedSuffix,
    explore,
    close
  };

  const explorePages: PageProps[] = useMemo(
    () => [
      {
        title: leftTitle,
        image: "/img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: how,
        href: "/how-it-works"
      },
      {
        title: rightTitle,
        image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: perks,
        href: "/opportunities-rewards"
      }
    ],
    [how, perks, rightTitle, leftTitle]
  );

  const onClose = useCallback(() => {
    router.push("/");
  }, [router]);

  useEffect(() => {
    const timer = setTimeout(() => {
      document.title = interestedCreatorTitle;
    }, 50);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }
  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        {isLoading && <Loading />}
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close, onClose }} />
          <InterestedCreatorsStartPage
            {...{
              pageLabels,
              explorePages,
              showConfirmation,
              setShowConfirmation,
              state,
              stableDispatch,
              errorToast,
              unhandledError,
              router,
              analytics
            }}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (flags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationStartPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<StartProps>;
  }

  if (!flags.isInterestedCreatorFlowEnabled()) {
    return { notFound: true };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      ...(await serverSideTranslations(locale, ["common", "interested-creator-start-page", "index", "information"]))
    }
  };
};
