import "reflect-metadata";
import React, { ComponentType, useCallback, useEffect, useMemo } from "react";
import dynamic from "next/dynamic";
import InterestedCreatorsStartPage from "../../components/pages/interested-creators/InterestedCreatorsStartPage";
import { useAppContext } from "@src/context";
import Error from "../_error";
import { useToast } from "../../components/toast";
import flags from "../../utils/feature-flags";
import { useRouter } from "next/router";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody } from "../../components/Layout";
import Loading from "../../components/Loading";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import applicationStartPageProps from "@src/serverprops/ApplicationStartPageProps";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { useDependency } from "@src/context/DependencyContext";
import session from "@src/middleware/Session";
import { addTelemetryInformation } from "@eait-playerexp-cn/server-kernel";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";

const InterestedCreatorsStartPageNew: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("applications/InterestedCreatorsStartPage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

const ExplorePages: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("applications/ExplorePage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type explorePages = {
  image: string;
  title: string;
  actionLabel: string;
  href: string;
  key?: string | number;
};

export type PageLabels = {
  title: string;
  subTitle: string;
  description: string;
  descriptionSuffix: string;
  button: string;
  alreadyApplied: string;
  alreadyAppliedSuffix: string;
  explore: string;
  close: string;
  creatorNetwork: string;
  unhandledError: string;
  interestedCreatorTitle: string;
};

export type StartProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels;
};

export default function Start({ pageLabels }: StartProps): JSX.Element {
  const { configuration, analytics } = useDependency();
  const { applicationStartPageLabels, informationLabels, commonPageLabels } = pageLabels;
  const { dispatch, state, state: { exceptionCode = null, sessionUser = null, isLoading } = {} } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const { error: errorToast } = useToast();
  const { unhandledError, applyNow, creatorNetwork, close, how } = commonPageLabels;
  const {
    title,
    subTitle,
    description,
    descriptionSuffix,
    alreadyApplied,
    alreadyAppliedSuffix,
    explore,
    exploreLeftTitle,
    exploreRightTitle,
    perks
  } = applicationStartPageLabels;

  const explorePages: explorePages[] = useMemo(
    () => [
      {
        title: exploreLeftTitle,
        image: "/img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: how,
        href: "/how-it-works"
      },
      {
        title: exploreRightTitle,
        image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: perks,
        href: "/opportunities-rewards"
      }
    ],
    [how, perks, exploreLeftTitle, exploreRightTitle]
  );

  const onClose = useCallback(() => {
    router.push("/");
  }, [router]);

  useEffect(() => {
    const timer = setTimeout(() => {
      document.title = informationLabels.interestedCreatorTitle;
    }, 50);
    return () => {
      clearTimeout(timer);
    };
  }, [informationLabels.interestedCreatorTitle]);

  const startApplication = useCallback(() => {
    analytics.startedCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push(
      configuration.FLAG_PER_PROGRAM_PROFILE
        ? `/api/applications`
        : `${configuration.APPLICATIONS_MFE_BASE_URL}/api/applications`
    );
  }, [router, analytics, configuration]);

  const labels = {
    title,
    subTitle,
    description,
    descriptionSuffix,
    button: applyNow,
    alreadyApplied,
    alreadyAppliedSuffix,
    explore,
    close,
    creatorNetwork,
    unhandledError,
    interestedCreatorTitle: informationLabels.interestedCreatorTitle
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout main-layout">
        {isLoading && <Loading />}
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close, onClose }} />
          <div className="mg-bg"></div>
          {flags.isNewInterestedCreatorComponentEnabled() ? (
            <>
              <InterestedCreatorsStartPageNew
                labels={labels}
                stableDispatch={stableDispatch}
                state={state}
                startApplication={startApplication}
                errorToast={errorToast}
                unhandledError={unhandledError}
                startThumbnail="/img/home-header--980w-x-690h.png"
              />
              <ExplorePages title={explore} explorePages={explorePages} />
            </>
          ) : (
            <InterestedCreatorsStartPage
              pageLabels={{
                title,
                subTitle,
                description,
                descriptionSuffix,
                button: applyNow,
                alreadyApplied,
                alreadyAppliedSuffix,
                explore,
                close,
                creatorNetwork,
                unhandledError,
                interestedCreatorTitle: informationLabels.interestedCreatorTitle
              }}
              explorePages={explorePages}
              state={state}
              stableDispatch={stableDispatch}
              errorToast={errorToast}
              unhandledError={unhandledError}
              router={router}
              analytics={analytics}
            />
          )}
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (flags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationStartPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<StartProps>;
  }

  if (!flags.isInterestedCreatorFlowEnabled()) {
    return { notFound: true };
  }

  await session(req, res, null);
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  await addTelemetryInformation(req, res, () => {});
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationStart");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      pageLabels
    }
  };
};
