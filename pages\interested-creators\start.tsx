import "reflect-metadata";
import React, { ComponentType, useCallback, useEffect, useMemo } from "react";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import dynamic from "next/dynamic";
import labelsInterestedCreatorStartPage from "../../config/translations/interested-creator-start-page";
import labelsCommon from "../../config/translations/common";
import labelsIndex from "../../config/translations/index";
import labelsInformation from "../../config/translations/information";
import InterestedCreatorsStartPage from "../../components/pages/interested-creators/InterestedCreatorsStartPage";
import { useAppContext } from "@src/context";
import Error from "../_error";
import { useToast } from "../../components/toast";
import flags from "../../utils/feature-flags";
import { useRouter } from "next/router";
import { PageProps } from "@components/pages/interested-creators/ExplorePages";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody } from "../../components/Layout";
import Loading from "../../components/Loading";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import applicationStartPageProps from "@src/serverprops/ApplicationStartPageProps";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { useDependency } from "@src/context/DependencyContext";
import session from "@src/middleware/Session";
import { addTelemetryInformation } from "@eait-playerexp-cn/server-kernel";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";

const InterestedCreatorsStartPageNew: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("applications/InterestedCreatorsStartPage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

const ExplorePages: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("applications/ExplorePage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type explorePages = {
  image: string;
  title: string;
  actionLabel: string;
  href: string;
  key?: string | number;
};

export type PageLabels = {
  title: string;
  subTitle: string;
  description: string;
  descriptionSuffix: string;
  button: string;
  alreadyApplied: string;
  alreadyAppliedSuffix: string;
  explore: string;
  close: string;
  creatorNetwork: string;
  unhandledError: string;
  interestedCreatorTitle: string;
};

export type StartProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels?: ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels;
};

export default function Start({ pageLabels }: StartProps): JSX.Element {
  const { configuration, analytics } = useDependency();
  const { dispatch, state, state: { exceptionCode = null, sessionUser = null, isLoading } = {} } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const { error: errorToast } = useToast();
  const { t } = useTranslation(["common", "interested-creator-start-page", "information", "index"]);

  // Local translation labels (used when feature flag is disabled)
  const { completeLabels, unhandledError } = useMemo(() => {
    const {
      header: { creatorNetwork, how },
      buttons: { yes, no, close },
      main: { unhandledError }
    } = labelsCommon(t);
    const { howItWorks } = labelsIndex(t);
    const infoLabels = labelsInformation(t);
    const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle, interestedCreatorTitle } = infoLabels;
    const { leftTitle, rightTitle } = howItWorks;
    return {
      completeLabels: {
        creatorNetwork,
        ...labelsInterestedCreatorStartPage(t),
        rightTitle,
        leftTitle,
        how,
        yes,
        no,
        confirmationDesc1,
        confirmationDesc2,
        modalConfirmationTitle,
        interestedCreatorTitle,
        close
      },
      unhandledError
    };
  }, [t]);

  // Extract local labels for existing component
  const {
    title: localTitle,
    subTitle: localSubTitle,
    description: localDescription,
    descriptionSuffix: localDescriptionSuffix,
    buttons: { applyNow: localApplyNow },
    alreadyApplied: localAlreadyApplied,
    alreadyAppliedSuffix: localAlreadyAppliedSuffix,
    creatorNetwork: localCreatorNetwork,
    leftTitle,
    rightTitle,
    perks: localPerks,
    how: localHow,
    explore: localExplore,
    interestedCreatorTitle: localInterestedCreatorTitle,
    close: localClose
  } = completeLabels;

  // Contentful labels (used when feature flag is enabled)
  const contentfulLabels = pageLabels ? {
    applicationStartPageLabels: pageLabels.applicationStartPageLabels,
    informationLabels: pageLabels.informationLabels,
    commonPageLabels: pageLabels.commonPageLabels
  } : null;

  // Create explorePages based on feature flag
  const explorePages: explorePages[] = useMemo(() => {
    if (flags.isNewInterestedCreatorComponentEnabled() && contentfulLabels) {
      const { applicationStartPageLabels, commonPageLabels } = contentfulLabels;
      return [
        {
          title: applicationStartPageLabels.exploreLeftTitle,
          image: "/img/home-how-does-platform-work--650w-x-650h.png",
          actionLabel: commonPageLabels.how,
          href: "/how-it-works"
        },
        {
          title: applicationStartPageLabels.exploreRightTitle,
          image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
          actionLabel: applicationStartPageLabels.perks,
          href: "/opportunities-rewards"
        }
      ];
    } else {
      // Use local translation labels
      return [
        {
          title: leftTitle,
          image: "/img/home-how-does-platform-work--650w-x-650h.png",
          actionLabel: localHow,
          href: "/how-it-works"
        },
        {
          title: rightTitle,
          image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
          actionLabel: localPerks,
          href: "/opportunities-rewards"
        }
      ];
    }
  }, [flags.isNewInterestedCreatorComponentEnabled(), contentfulLabels, leftTitle, rightTitle, localHow, localPerks]);

  const onClose = useCallback(() => {
    router.push("/");
  }, [router]);

  useEffect(() => {
    const timer = setTimeout(() => {
      const title = contentfulLabels
        ? contentfulLabels.informationLabels.interestedCreatorTitle
        : localInterestedCreatorTitle;
      document.title = title;
    }, 50);
    return () => {
      clearTimeout(timer);
    };
  }, [contentfulLabels, localInterestedCreatorTitle]);

  const startApplication = useCallback(() => {
    analytics.startedCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push(
      configuration.FLAG_PER_PROGRAM_PROFILE
        ? `/api/applications`
        : `${configuration.APPLICATIONS_MFE_BASE_URL}/api/applications`
    );
  }, [router, analytics, configuration]);

  // Labels for new component (from Contentful)
  const newComponentLabels = contentfulLabels ? {
    title: contentfulLabels.applicationStartPageLabels.title,
    subTitle: contentfulLabels.applicationStartPageLabels.subTitle,
    description: contentfulLabels.applicationStartPageLabels.description,
    descriptionSuffix: contentfulLabels.applicationStartPageLabels.descriptionSuffix,
    button: contentfulLabels.commonPageLabels.applyNow,
    alreadyApplied: contentfulLabels.applicationStartPageLabels.alreadyApplied,
    alreadyAppliedSuffix: contentfulLabels.applicationStartPageLabels.alreadyAppliedSuffix,
    explore: contentfulLabels.applicationStartPageLabels.explore,
    close: contentfulLabels.commonPageLabels.close,
    creatorNetwork: contentfulLabels.commonPageLabels.creatorNetwork,
    unhandledError: contentfulLabels.commonPageLabels.unhandledError,
    interestedCreatorTitle: contentfulLabels.informationLabels.interestedCreatorTitle
  } : null;

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout main-layout">
        {isLoading && <Loading />}
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close, onClose }} />
          <div className="mg-bg"></div>
          {flags.isNewInterestedCreatorComponentEnabled() ? (
            <>
              <InterestedCreatorsStartPageNew
                labels={labels}
                stableDispatch={stableDispatch}
                state={state}
                startApplication={startApplication}
                errorToast={errorToast}
                unhandledError={unhandledError}
                startThumbnail="/img/home-header--980w-x-690h.png"
              />
              <ExplorePages title={explore} explorePages={explorePages} />
            </>
          ) : (
            <InterestedCreatorsStartPage
              pageLabels={{
                title,
                subTitle,
                description,
                descriptionSuffix,
                button: applyNow,
                alreadyApplied,
                alreadyAppliedSuffix,
                explore,
                close,
                creatorNetwork,
                unhandledError,
                interestedCreatorTitle: informationLabels.interestedCreatorTitle
              }}
              explorePages={explorePages}
              state={state}
              stableDispatch={stableDispatch}
              errorToast={errorToast}
              unhandledError={unhandledError}
              router={router}
              analytics={analytics}
            />
          )}
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (flags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationStartPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<StartProps>;
  }

  if (!flags.isInterestedCreatorFlowEnabled()) {
    return { notFound: true };
  }

  await session(req, res, null);
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  await addTelemetryInformation(req, res, () => {});
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationStart");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      pageLabels
    }
  };
};
