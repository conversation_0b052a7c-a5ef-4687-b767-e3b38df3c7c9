import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import InterestedCreatorsStartPage from "../../../../components/pages/interested-creators/InterestedCreatorsStartPage";
import { commonTranslations, labels } from "../../../translations";
import { axe } from "jest-axe";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { NextRouter } from "next/router";

describe("InterestedCreatorsStartPage", () => {
  const {
    main: { unhandledError }
  } = commonTranslations;
  const { startPageLabels } = labels;
  const appliedLabels = {
    alreadyApplied: "Already submitted? Check the status of",
    alreadyAppliedSuffix: "your request here."
  };
  const interestedCreatorsStartPageProps = {
    pageLabels: { ...startPageLabels, ...appliedLabels },
    stableDispatch: jest.fn(),
    explorePages: [
      {
        title: "How does the new platform work?",
        image: "/img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: "How it Works",
        href: "/how-it-works"
      },
      {
        title: "Opportunities, communities and partnerships",
        image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: "Available Perks",
        href: "/opportunities-rewards"
      }
    ],
    setShowConfirmation: jest.fn(),
    showConfirmation: false,
    errorToast: jest.fn(),
    t: (str) => str,
    unhandledError,
    state: {},
    router: {
      push: jest.fn().mockResolvedValue(true),
      locale: "en-us"
    } as unknown as NextRouter,
    analytics: {} as unknown as BrowserAnalytics
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows start creator application labels", async () => {
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    await expect(await screen.findByText("Start your submission")).toBeInTheDocument();
    expect(
      await screen.findByText(/Thanks for your interest in EA Creator Network. Please start your submission below./i)
    ).toBeInTheDocument();
    expect(
      await screen.findByText(/To submit, you need to be over 18 years old and will need an/i)
    ).toBeInTheDocument();
    expect(await screen.findByText(/Electronic Arts Account./i)).toBeInTheDocument();
    expect(await screen.findByText(/Request to Join/i)).toBeInTheDocument();
    expect(await screen.findByText(/Already submitted/i)).toBeInTheDocument();
    expect(await screen.findByText(/your request here./i)).toBeInTheDocument();
    expect(await screen.findByText(/Explore what the EA Creator Network has to Offer/i)).toBeInTheDocument();
    expect(await screen.findByText(/How does the new platform work?/i)).toBeInTheDocument();
    expect(await screen.findByText(/Opportunities, communities and partnerships/i)).toBeInTheDocument();
  });

  it("shows a link to create a new 'Electronic Arts Account'", async () => {
    const stableDispatch = jest.fn();

    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} stableDispatch={stableDispatch} />);

    expect(screen.getByText(/Electronic Arts Account./i)).toHaveAttribute("href", "/api/accounts");
  });

  it("shows link 'your request here' to check the status of an existing application", async () => {
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    expect(screen.getByText(/your request here./i)).toHaveAttribute("href", "/api/applications");
  });

  it("logs 'Started Creator Application' on clicking Apply Now", async () => {
    const analytics = { startedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} analytics={analytics} />);

    await userEvent.click(await screen.findByRole("button", { name: "Request to Join" }));

    await waitFor(() => {
      expect(analytics.startedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.startedCreatorApplication).toHaveBeenCalledWith({ locale: "en-us", page: "/" });
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
