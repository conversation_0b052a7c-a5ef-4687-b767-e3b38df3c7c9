import { Service } from "typedi";
import { NextApiResponse, NextApiRequest } from "next";
import Controller from "./Controller";
{{#if folderName}}
import {{pascalCase ApiResourceName}}HttpClient from "../../src/{{camelCase folderName}}/{{pascalCase ApiResourceName}}HttpClient";
{{else}}
import {{pascalCase ApiResourceName}}HttpClient from "../{{camelCase ApiResourceName}}/{{pascalCase ApiResourceName}}HttpClient";
{{/if}}

@Service()
export default class {{pascalCase ApiResourceName}}Controller extends Controller {
  constructor(private {{camelCase ApiResourceName}}: {{pascalCase ApiResourceName}}HttpClient) {
    super();
  }

  async execute(req: NextApiRequest, res: NextApiResponse) {
    const {{camelCase ApiResourceName}}Id = this.query(req, "id") as string;
    await this.{{camelCase ApiResourceName}}.callApi({{camelCase ApiResourceName}}Id);
    this.okResponse(res);
  }
}