import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Toast, useToast } from "../toast";
import {
  CURRENT_DELIVERABLE_PAGE,
  ERROR,
  LOADING,
  onToastClose,
  SESSION_USER,
  TOAST_ERROR,
  toastContent,
  useAsync,
  useDetectScreen,
  VALIDATION_ERROR
} from "../../utils";
import { useRouter } from "next/router";
import ContentDeliverablesTab from "./content-submission/ContentDeliverablesTab";
import {
  Button,
  collab,
  ContentGuideLines,
  ContentSubmissionCard,
  creatorCode,
  CreatorCodeCard,
  dailyAllowance,
  designCouncil,
  earlyAccess,
  exclusivePreview,
  food,
  freeGameCode,
  GameAndCreatorCodeCard,
  hotel,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  ModalHeader,
  ModalTitle,
  ModalV2,
  OnlineEventDetailsCard,
  OpportunityDescription,
  OpportunityDetails,
  paid,
  PerksCardWithInvitation,
  privateDiscordChannel,
  RemoteEvent,
  swag,
  travel,
  vipEvent
} from "@eait-playerexp-cn/core-ui-kit";
import OpportunityService from "../../src/api/services/OpportunityService";
import SubmittedContentService from "../../src/api/services/SubmittedContentService";
import OperationsService from "../../src/api/services/OperationsService";
import Loading from "../../components/Loading";
import { useAppContext } from "../../src/context";
import OpportunityWithPerksHeader from "../opportunities/OpportunityWithPerksHeader";
import cx from "classnames";
import PaymentBanner from "./payment-information/PaymentDetailsTab/PaymentBanner";
import PointOfContactDetails from "../profile/PointOfContactDetails";
import { useDependency } from "@src/context/DependencyContext";

export const perksIcons = {
  PAID: paid,
  COLLAB: collab,
  DAILY_ALLOWANCE: dailyAllowance,
  DESIGN_COUNCIL: designCouncil,
  EARLY_ACCESS: earlyAccess,
  EXCLUSIVE_PREVIEW: exclusivePreview,
  FOOD: food,
  FREE_GAME_CODE: freeGameCode,
  HOTEL: hotel,
  PRIVATE_DISCORD_CHANNEL: privateDiscordChannel,
  SWAG: swag,
  TRAVEL: travel,
  VIP_EVENT: vipEvent,
  CREATOR_CODE: creatorCode
};

const FooterButtons = memo(function FooterButtons({ onCancel, onDecline, spinner, layout, cancelButtonRef }) {
  return (
    <>
      <Button variant="tertiary" dark size="sm" onClick={onCancel} ref={cancelButtonRef}>
        {layout.buttons.cancel}
      </Button>
      <Button onClick={onDecline} spinner={spinner} disabled={spinner}>
        {layout.buttons.declineTermsAndCondition}
      </Button>
    </>
  );
});

export default function Opportunity({
  user,
  WATERMARKS_URL,
  opportunitiesLabels,
  layout,
  contentSubmissionTabLabels,
  YOUTUBE_HOSTS,
  TWITCH_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TIKTOK_HOSTS,
  t,
  accountConnected,
  error,
  pages,
  myContentLabels,
  pocLabels,
  setConnectedAccount,
  referer,
  paymentBannerLabels,
  invalidTikTokScope,
  UPDATE_OPPORTUNITY_DETAILS,
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_CONTENT_WITH_FINAL_REMARK
}) {
  const {
    analytics,
    errorHandler,
    configuration: { FLAG_SUBMITTED_CONTENT_WITH_PROGRAM }
  } = useDependency();
  const isWeb = useDetectScreen(10000);
  const isTab = useDetectScreen(1279);
  const isMobile = useDetectScreen(767);
  const cancelButtonRef = useRef(null);

  const size = isWeb && !isTab ? "DESKTOP" : isTab && !isMobile ? "TABLET" : "MOBILE";

  const [gameCodeData, setGameCodeData] = useState(null);
  const [opportunity, setOpportunity] = useState(null);
  const [participationStatus, setParticipationStatus] = useState(null);
  const [participationId, setParticipationId] = useState(null);
  const [isJoined, setIsJoined] = useState(false);
  const [opportunityStatus, setOpportunityStatus] = useState();
  const [submittedDeliverableContent, setSubmittedDeliverableContent] = useState(null);
  const [currentDeliverablePageContents, setCurrentDeliverablePageContents] = useState(null);
  const [overview, setOverview] = useState(true);
  const [platform, setPlatform] = useState();
  const [declined, setDeclined] = useState(false);
  const [declineModal, setDeclineModal] = useState(false);
  const [creator, setCreator] = useState(null);
  const [updatedContent, setUpdatedContent] = useState(false);
  const [deliverablePages, setDeliverablePages] = useState(null);
  const [pointOfContact, setPointOfContact] = useState(null);
  const [gameAndCreatorCode, setGameAndCreatorCode] = useState(null);
  const [contentSubmissionCardLoader, setContentSubmissionCardLoader] = useState(true);
  const DEFAULT_PAGE = 1;
  const DEFAULT_SIZE_SINGLE = 1; // for deliverable type single
  const DEFAULT_SIZE_UNLIMITED = 5; // for deliverable type unlimited
  const router = useRouter();
  const { declineModalContent, declineModalTitle, decline, declinedText, invitedText } = opportunitiesLabels;
  const {
    dispatch,
    state: {
      isLoading,
      isError,
      isValidationError,
      onToastCloseAction,
      isToastError,
      currentDeliverablePage = null,
      clickedDeliverableId
    } = {}
  } = useAppContext() || {};
  const onPaymentBannerClick = () => {
    analytics.clickedPaymentDetailsIncompleteHelperBanner({ locale: router.locale });
    const paymentSettingsUrl = FLAG_NEW_NAVIGATION_ENABLED
      ? "/payment-information?tab=payment-settings"
      : "/profile?section=payment-information&tab=payment-settings";
    router.push(paymentSettingsUrl);
  };

  useEffect(() => {
    function isConnectingAccounts() {
      if (participationStatus?.status === "JOINED" && (pages?.length > 0 || error)) {
        setOverview(false);
      }
    }
    isConnectingAccounts();
  }, [pages, error, participationStatus]);

  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast, info: infoToast } = useToast();

  useEffect(() => {
    function isAccountConnectedSuccessfully() {
      if (participationStatus?.status === "JOINED" && accountConnected) {
        setOverview(false);
        setUpdatedContent(true);
      }
    }
    isAccountConnectedSuccessfully();
  }, [accountConnected, participationStatus]);

  const onDeclineModal = useCallback(() => setDeclineModal(true), []);
  const onDeclineHandler = useCallback(async () => {
    try {
      await OpportunityService.declineInvitation(participationStatus.invitationId);
      analytics.declinedOpportunityInvitation({
        locale: router.locale,
        opportunity
      });
      infoToast(
        <Toast
          header={layout.toasts.declineInvitationTitle}
          content={layout.toasts.declineInvitationDescription}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => {
            setDeclined(true);
            setOpportunityStatus("DECLINED");
          }
        }
      );
    } catch (e) {
      errorHandler(stableDispatch, e);
      stableDispatch({ type: TOAST_ERROR });
    }

    setDeclineModal(false);
  }, [
    stableDispatch,
    participationStatus?.invitationId,
    infoToast,
    layout.toasts.declineInvitationTitle,
    layout.toasts.declineInvitationDescription,
    router,
    setOpportunityStatus,
    errorHandler,
    setDeclineModal,
    setDeclined
  ]);
  const { pending: spinner, execute: onDecline, error: declineError } = useAsync(onDeclineHandler, false);

  useEffect(() => {
    if (opportunity && gameCodeData) {
      opportunity.gameCodes?.availability.find((_iter) => {
        if (_iter?.platform?.value === gameCodeData.platformId) {
          setPlatform(_iter?.platform?.label?.toUpperCase());
          setContentSubmissionCardLoader(false);
        }
      });
    }
  }, [gameCodeData, opportunity]);

  useEffect(() => {
    function getOpportunityDetails() {
      if (!router.isReady) return;
      stableDispatch({ type: LOADING, data: true });
      let opportunity;
      if (UPDATE_OPPORTUNITY_DETAILS) {
        opportunity = OpportunityService.getOpportunityWithPaymentDetails(router.query.id); // V9 will have all the data from v8 and payment details
      } else {
        opportunity = OpportunityService.getOpportunityWithDeliverables(router.query.id);
      }
      opportunity
        .then((res) => {
          setOpportunity(res.data.opportunity);
          setCreator(res.data.creator);
          setPointOfContact(res.data.opportunity.pointOfContact);
          stableDispatch({ type: LOADING, data: false });
          document.title = res.data.opportunity.title;
        })
        .catch((e) => {
          stableDispatch({ type: LOADING, data: false });
          errorHandler(stableDispatch, e);
          stableDispatch({ type: TOAST_ERROR });
        });
    }
    getOpportunityDetails();
  }, [router.isReady, router.query.id, stableDispatch]);

  const formatSubmittedContent = useCallback(
    (content) => {
      let formattedContent = content;
      formattedContent.contents.forEach((content) => (content.contentTypeLabel = myContentLabels[content.contentType]));
      return formattedContent;
    },
    [myContentLabels]
  );

  const pageCalculation = (total, pageLimit = 10) => {
    let pages = [];
    for (let i = 0; i < Math.ceil(total / pageLimit); i++) {
      pages.push(i + 1);
    }
    return pages;
  };

  useEffect(() => {
    function updateOpportunityDetails() {
      if (!router.isReady) return;
      if (invalidTikTokScope) {
        stableDispatch({ type: TOAST_ERROR });
        stableDispatch({ type: ERROR, data: layout.main.unhandledError });
        return;
      }
      if ((opportunity && !participationStatus) || updatedContent) {
        !updatedContent && stableDispatch({ type: LOADING, data: true });
        OpportunityService.getParticipationDetails(router.query.id)
          .then(async (res) => {
            if (res?.data?.[0]?.status) {
              setParticipationStatus(res.data[0]);
              setParticipationId(res.data[0].participationId);
              setGameAndCreatorCode(res.data[0].creatorCode ? res.data[0].creatorCodeDetail() : null);
              analytics.viewedOpportunityDetails({
                locale: router.locale,
                opportunity
              });
            }
            setUpdatedContent(false);
            /* Repeating Backward compatibility logic start */
            let status = res.data[0].status;
            if (res?.data?.[0]?.participationId) {
              if (opportunity.isCompleted()) {
                status = "COMPLETED";
              }

              if (opportunity.hasDeliverables) {
                /** If deliverables is true, we call submit content api for content deliverables*/
                let contentObj = {},
                  deliverablePagesObj = {},
                  currentDeliverablePageObj = {},
                  submittedDeliverableContentObj = {};
                let getSubmittedContent;
                if (FLAG_SUBMITTED_CONTENT_WITH_PROGRAM) {
                  getSubmittedContent = SubmittedContentService.getSubmittedContentsFinalRemarksWithProgramCode;
                } else if (FLAG_CONTENT_WITH_FINAL_REMARK) {
                  getSubmittedContent = SubmittedContentService.getSubmittedContentsFinalRemarks;
                } else {
                  getSubmittedContent = SubmittedContentService.getSubmittedContents;
                }
                opportunity.contentSubmission?.deliverables?.forEach((deliverable) => {
                  const size = deliverable.type === "SINGLE" ? DEFAULT_SIZE_SINGLE : DEFAULT_SIZE_UNLIMITED;
                  getSubmittedContent(size, DEFAULT_PAGE, res.data[0].participationId, deliverable.id)
                    .then((curContent) => {
                      let formattedContents = formatSubmittedContent(curContent.data);
                      if (formattedContents.contents?.length > 0) {
                        // when content card status is null, deliverable card status will be "APPROVED"
                        // when content card status is UNREVIEWED || IN_SCAN, deliverable status will be "PENDING"
                        // when deliverable type is UNLIMITED, deliverable status will be "UNLIMITED_CONTENT"
                        const deliverableStatusWithTypeSingle =
                          formattedContents.contents[0]?.status === "UNREVIEWED" ||
                          formattedContents.contents[0]?.status === "IN_SCAN" ||
                          formattedContents.contents[0]?.status === "CHANGE_RECEIVED"
                            ? "PENDING"
                            : formattedContents.contents[0]?.status
                            ? formattedContents.contents[0]?.status
                            : "APPROVED";
                        deliverable.status =
                          deliverable.type === "SINGLE" ? deliverableStatusWithTypeSingle : "UNLIMITED_CONTENT";
                      }
                      if (curContent?.data?.total > 0) {
                        /** This contains mapping of deliverableId with pagesList(list of page numbers)  (eg) { "del1233": [1,2] } */
                        deliverablePagesObj = {
                          ...deliverablePagesObj,
                          [deliverable.id]: pageCalculation(curContent.data.total, 5)
                        };
                        setDeliverablePages(deliverablePagesObj);
                        /** This contains mapping of deliverableId with current page number (eg) { "del1233": 1 } */
                        currentDeliverablePageObj = {
                          ...currentDeliverablePageObj,
                          [deliverable.id]: DEFAULT_PAGE
                        };
                        stableDispatch({ type: CURRENT_DELIVERABLE_PAGE, data: currentDeliverablePageObj });
                      }
                      submittedDeliverableContentObj = {
                        ...submittedDeliverableContentObj,
                        [deliverable.id]: {
                          ...submittedDeliverableContent?.[deliverable?.id],
                          [DEFAULT_PAGE]: formattedContents.contents
                        }
                      };
                      setSubmittedDeliverableContent(submittedDeliverableContentObj);
                      contentObj = {
                        ...contentObj,
                        [deliverable.id]: formattedContents.contents
                      };
                      setCurrentDeliverablePageContents(contentObj);
                      stableDispatch({ type: LOADING, data: false });
                    })
                    .catch((e) => {
                      stableDispatch({ type: LOADING, data: false });
                      errorHandler(stableDispatch, e);
                      stableDispatch({ type: TOAST_ERROR });
                      stableDispatch({ type: ERROR, data: layout.main.unhandledError });
                    });
                });
              }
              if (opportunity && opportunity.hasGameCodes) {
                OperationsService.viewAssignedGameCode(res.data[0].participationId)
                  .then((gameCodeResponse) => {
                    const gameCode = gameCodeResponse.data;
                    setGameCodeData(gameCode);
                    setGameAndCreatorCode(
                      gameCode.gameCode ? gameCode.gameCode.code : opportunitiesLabels?.gameCodePending
                    );
                  })
                  .catch((e) => {
                    stableDispatch({ type: LOADING, data: false });
                    setContentSubmissionCardLoader(false);
                    if (e?.response?.status === 404) return; // Ignore if no codes have been assigned
                    errorHandler(stableDispatch, e);
                    stableDispatch({ type: TOAST_ERROR });
                  });
              } else {
                setContentSubmissionCardLoader(false);
              }
            }
            setIsJoined(res.data[0].status === "JOINED");
            setOpportunityStatus(status);
            /* Repeating Backward compatibility logic end */
            stableDispatch({ type: LOADING, data: false });
          })
          .catch((e) => {
            stableDispatch({ type: LOADING, data: false });
            errorHandler(stableDispatch, e);
            stableDispatch({ type: TOAST_ERROR });
            setContentSubmissionCardLoader(false);
          });
      }
    }
    updateOpportunityDetails();
  }, [opportunity, router.isReady, stableDispatch, updatedContent]);

  const getSubmittedContentsForSpecificPage = (currentPageNumber, deliverableId) => {
    const getSubmittedContent = FLAG_CONTENT_WITH_FINAL_REMARK
      ? SubmittedContentService.getSubmittedContentsFinalRemarks
      : SubmittedContentService.getSubmittedContents;
    getSubmittedContent(DEFAULT_SIZE_UNLIMITED, currentPageNumber, participationId, deliverableId)
      .then((res) => {
        if (res?.data?.total > 0) {
          let data = formatSubmittedContent(res.data);
          setSubmittedDeliverableContent({
            ...submittedDeliverableContent,
            [deliverableId]: {
              ...submittedDeliverableContent[deliverableId],
              [currentPageNumber]: data.contents
            }
          });
          setCurrentDeliverablePageContents({
            ...currentDeliverablePageContents,
            [deliverableId]: data.contents
          });
        }
      })
      .catch((e) => {
        errorHandler(stableDispatch, e);
        stableDispatch({ type: TOAST_ERROR });
        stableDispatch({ type: ERROR, data: layout.main.unhandledError });
      });
  };

  /** This is for handling deliverables current page contents */
  useEffect(() => {
    if (currentDeliverablePage) {
      const currentPageNumber = currentDeliverablePage[clickedDeliverableId];
      if (currentPageNumber > 1) {
        getSubmittedContentsForSpecificPage(currentPageNumber, clickedDeliverableId);
      } else if (
        submittedDeliverableContent &&
        currentPageNumber &&
        submittedDeliverableContent?.[clickedDeliverableId]?.[currentPageNumber].length > 0
      ) {
        setCurrentDeliverablePageContents({
          ...currentDeliverablePageContents,
          [clickedDeliverableId]: submittedDeliverableContent?.[clickedDeliverableId]?.[currentPageNumber]
        });
      }
    }
  }, [currentDeliverablePage, clickedDeliverableId]);

  const {
    main: { unhandledError }
  } = layout;

  useEffect(() => {
    if (user) stableDispatch({ type: SESSION_USER, data: user });
  }, [user, stableDispatch]);

  useEffect(() => {
    if (isToastError && (isError || isValidationError)) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => {
            onToastCloseAction && onToastCloseAction();
            onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch);
          }
        }
      );
    }
  }, [errorToast, isError, isValidationError, stableDispatch, unhandledError, isToastError]);

  const handleModalClose = useCallback(() => setDeclineModal(false), []);

  const joinAction = useCallback(() => {
    analytics.acceptedOpportunityInvitation({
      locale: router.locale,
      opportunity
    });
    router.push(`/opportunities/${opportunity?.id}/registrations`);
  }, [router, opportunity, participationStatus]);

  const actions = useMemo(() => {
    const buttons = [
      {
        label: !UPDATE_OPPORTUNITY_DETAILS
          ? opportunitiesLabels.join
          : participationStatus?.invitationId
          ? opportunitiesLabels.join
          : opportunitiesLabels.joinOpportunity,
        onClick: joinAction,
        disable: opportunity?.isPastOpportunity() || ["DECLINED", "JOINED"].includes(participationStatus?.status),
        variant: "primary",
        size: "sm"
      }
    ];
    if (participationStatus?.invitationId) {
      buttons.push({
        label: decline,
        onClick: onDeclineModal,
        disable: opportunity?.isPastOpportunity() || ["DECLINED", "JOINED"].includes(participationStatus?.status),
        variant: "secondary",
        size: "sm"
      });
    }
    return buttons;
  }, [
    opportunitiesLabels.join,
    joinAction,
    opportunity,
    participationStatus?.status,
    participationStatus?.invitationId,
    decline,
    onDeclineModal
  ]);

  useEffect(() => {
    if (router.query.tab === "content-submission" || router.query.tab === "content-deliverables") setOverview(false);
  }, [router]);
  const opportunitySettings = useCallback(() => {
    if (opportunity) {
      return opportunity.settings(opportunitiesLabels, router.locale);
    }
    return [];
  }, [opportunity]);

  const statuses = useMemo(() => {
    if (!UPDATE_OPPORTUNITY_DETAILS) {
      return {
        JOINED: opportunitiesLabels.joined,
        DECLINED: opportunitiesLabels.declined,
        COMPLETED: opportunitiesLabels.completed
      };
    } else {
      return {
        JOINED: opportunitiesLabels.joined,
        DECLINED: opportunitiesLabels.declined,
        COMPLETED: opportunitiesLabels.completed,
        PAST: opportunitiesLabels.past,
        OPEN: opportunitiesLabels.open,
        INVITED: opportunitiesLabels.invited
      };
    }
  }, [opportunitiesLabels]);

  const opportunityDeclined = useMemo(
    () => participationStatus?.status === "DECLINED" || declined,
    [participationStatus?.status, declined]
  );

  const handleContentDeliverablesTab = () => {
    analytics.clickedDeliverablesTab({
      locale: router.locale,
      opportunity
    });
    setOverview(false);
  };

  const opportunityForHeader = useMemo(() => {
    if (!UPDATE_OPPORTUNITY_DETAILS) {
      return {
        opportunity: {
          status: `${statuses[opportunityStatus] || ""}`,
          settings: opportunitySettings(),
          title: opportunity?.title,
          declined: opportunityDeclined
        }
      };
    } else {
      let status = "";
      if (["COMPLETED", "JOINED", "DECLINED"].includes(opportunityStatus)) {
        status = opportunityStatus;
      } else if (("INVITED" === opportunityStatus || !opportunityStatus) && opportunity?.isPastOpportunity()) {
        status = "PAST";
      } else if ("INVITED" === opportunityStatus && participationStatus?.invitationId) {
        status = "INVITED";
      } else {
        status = "OPEN";
      }
      return {
        opportunity: {
          status: status,
          settings: opportunitySettings(),
          title: opportunity?.title
        },
        statusLabel: `${statuses[status] || ""}`
      };
    }
  }, [statuses, opportunityStatus, opportunitySettings, opportunity?.title, opportunityDeclined]);
  const tabsWithoutContentSubmission = [{ label: opportunitiesLabels.overview, callback: () => setOverview(true) }];
  const tabsWithContentSubmission = [
    { label: opportunitiesLabels.overview, callback: () => setOverview(true) },
    {
      label: opportunitiesLabels.contentDeliverables,
      callback: () => {
        handleContentDeliverablesTab();
      }
    }
  ];
  const handleOpenSubmitContentModal = () => {
    analytics.startedContentSubmissionFlow({
      locale: router.locale,
      opportunity
    });
    setOverview(false);
  };

  const stretchHeroImage = (participationId, opportunity) =>
    participationId &&
    ((!opportunity.hasGameCodes && !opportunity.hasDeliverables) || opportunity.type === "support_a_creator");

  const getPerks = (opportunity) =>
    opportunity?.formattedPerks(opportunitiesLabels.perksLabels).map((perk) => ({
      ...perk,
      Icon: perksIcons[perk.value],
      className: "opportunity-details-perk-icon"
    })) || [];

  const getTranslatedPerks = (perks) => {
    let filteredLabels =
      perks?.map((perk) => {
        return { code: perk.code, name: opportunitiesLabels.perksLabels[perk.code] };
      }) || [];
    return filteredLabels || [];
  };

  const remoteEventInfo = (opportunity) => {
    if (opportunity.eventPeriodHasEnded()) return opportunitiesLabels.remoteEvent.description;
    if (!opportunity.event.meetingLink) return opportunitiesLabels.remoteEvent.NoMeetingLinkInfo;
  };

  return (
    <>
      <div className="opportunity-perks-container">
        {(isLoading && (
          <div className="opportunities-container">
            <div className="opportunities-full-screen">
              <Loading />
            </div>
          </div>
        )) ||
          (opportunity && opportunity.id && (
            <div className="opportunity-perks-full-screen">
              <OpportunityWithPerksHeader
                {...{
                  participationStatus,
                  participationId,
                  opportunitiesLabels,
                  opportunityHeader: opportunityForHeader,
                  actions,
                  opportunity,
                  opportunityHeroImage: opportunity.heroImage,
                  locale: router.locale,
                  opportunityDeclined,
                  invitedText,
                  declinedText,
                  overview,
                  isJoined,
                  tabsWithContentSubmission,
                  tabsWithoutContentSubmission,
                  referer,
                  opportunityStatus,
                  UPDATE_OPPORTUNITY_DETAILS
                }}
              />
              {opportunity && overview && (
                <div
                  className={
                    !UPDATE_OPPORTUNITY_DETAILS
                      ? "opportunity-perks-overview-body"
                      : "opportunity-perks-overview-body-with-flag"
                  }
                >
                  <div className="opportunity-perks-hero-card-container">
                    {!UPDATE_OPPORTUNITY_DETAILS && (
                      <img
                        src={opportunity.heroImage}
                        alt="Hero Image"
                        className={cx("opportunity-perks-hero-image", {
                          "opportunity-perks-hero-image-full-width": stretchHeroImage(participationId, opportunity)
                        })}
                      />
                    )}
                    <div
                      className={
                        !UPDATE_OPPORTUNITY_DETAILS
                          ? cx("opportunity-perks-details", {
                              "opportunity-perks-details-no-left-spacing": stretchHeroImage(
                                participationId,
                                opportunity
                              )
                            })
                          : "opportunity-perks-details-with-flag"
                      }
                    >
                      {!UPDATE_OPPORTUNITY_DETAILS && participationStatus && !participationId && (
                        <PerksCardWithInvitation
                          actions={actions}
                          title={opportunitiesLabels.perks}
                          subtitle={opportunitiesLabels.perksSubtitle}
                          footNote={`${opportunitiesLabels.registrationCloses} ${opportunity.registrationPeriodEnd(
                            router.locale
                          )}`}
                          perks={getTranslatedPerks(opportunity.perks)}
                          invited={participationStatus?.invitationId !== null}
                          declined={opportunityDeclined}
                          invitedText={invitedText}
                          declinedText={declinedText}
                        />
                      )}
                      {!UPDATE_OPPORTUNITY_DETAILS &&
                        participationId &&
                        (opportunity.hasGameCodes || opportunity.hasDeliverables) && (
                          <ContentSubmissionCard
                            actionLabel={opportunity.hasDeliverables && opportunitiesLabels.submitContent}
                            footNote={
                              (opportunity.hasDeliverables &&
                                opportunity.contentSubmission?.submissionWindow &&
                                (!opportunity.contentSubmissionWindowHasStarted()
                                  ? `${opportunitiesLabels.submissionOpens} ${opportunity.contentSubmissionWindowStart(
                                      router.locale
                                    )}`
                                  : `${opportunitiesLabels.submissionCloses} ${opportunity.contentSubmissionWindowEnd(
                                      router.locale
                                    )}`)) ||
                              ""
                            }
                            submissionClosedMessage={
                              opportunity.hasDeliverables &&
                              opportunity.contentSubmission?.submissionWindow &&
                              opportunity.contentSubmissionWindowHasStarted() &&
                              opportunitiesLabels.submissionClosedMessage
                            }
                            gameCode={
                              opportunity.hasGameCodes && {
                                code: gameCodeData?.gameCode
                                  ? gameCodeData?.gameCode.code
                                  : opportunitiesLabels?.gameCodePending,
                                platform: platform,
                                title: opportunitiesLabels?.gameCodeTitle,
                                copyCodeText: opportunitiesLabels?.copyGameCode,
                                copiedText: opportunitiesLabels?.copied,
                                hasCode: gameCodeData?.gameCode?.code
                              }
                            }
                            actionHandler={handleOpenSubmitContentModal}
                            disableSubmission={opportunity.isSubmissionWindowClosed()}
                            isLoading={contentSubmissionCardLoader}
                          />
                        )}
                    </div>
                  </div>
                  {participationStatus?.creatorCode &&
                    participationStatus?.creatorCode?.code &&
                    !UPDATE_OPPORTUNITY_DETAILS && (
                      <div className="opportunity-creator-code">
                        <CreatorCodeCard
                          title={opportunitiesLabels.creatorCode.title}
                          creatorCode={participationStatus?.creatorCode?.code}
                          activationStartDate={participationStatus?.creatorCode?.activationWindow?.start?.formatWithEpoch(
                            "MMM DD, YYYY",
                            router.locale
                          )}
                          activationEndDate={participationStatus?.creatorCode?.activationWindow?.end?.formatWithEpoch(
                            "MMM DD, YYYY",
                            router.locale
                          )}
                          copyLabel={opportunitiesLabels.creatorCode.copyLabel}
                          copyText={opportunitiesLabels.creatorCode.copyText}
                          textCopied={opportunitiesLabels.creatorCode.textCopied}
                          disabled={
                            !participationStatus.creatorCodeHasStarted() || participationStatus.creatorCodeHasEnded()
                          }
                        />
                        {!participationStatus.creatorCodeHasStarted() && (
                          <p className="opportunity-creator-code-info-text">
                            {opportunitiesLabels.creatorCode.codeNotYetActiveMessage}
                          </p>
                        )}
                        {participationStatus.creatorCodeHasEnded() && (
                          <p className="opportunity-creator-code-info-text">
                            {opportunitiesLabels.creatorCode.codeExpiredMessage}
                          </p>
                        )}
                      </div>
                    )}
                  {!UPDATE_OPPORTUNITY_DETAILS &&
                    opportunity.hasEvent &&
                    opportunity.event.type === "Remote Event" &&
                    isJoined && (
                      <div className="opportunity-remote-event-wrapper">
                        <RemoteEvent
                          {...{
                            title: opportunitiesLabels.remoteEvent.title,
                            description: opportunity.remoteEventDescription(
                              opportunitiesLabels.remoteEvent,
                              router.locale
                            ),
                            eventUrl: opportunity.event.meetingLink,
                            eventPassword: opportunity.event.meetingPassword,
                            isDisabled: opportunity?.eventPeriodHasEnded() || !opportunity.event.meetingLink,
                            labels: {
                              joinEvent: opportunitiesLabels.remoteEvent.joinEvent,
                              noPasswordRequired: opportunitiesLabels.remoteEvent.noPasswordRequired,
                              noEventDetailsAdded: opportunitiesLabels.remoteEvent.noEventDetailsAdded
                            }
                          }}
                        />
                      </div>
                    )}
                  {isJoined && opportunity.hasPayments && !creator?.accountInformation?.isPayable && (
                    <div
                      className={cx({
                        "opportunity-payment-banner-wrapper": UPDATE_OPPORTUNITY_DETAILS
                      })}
                    >
                      <PaymentBanner {...{ labels: paymentBannerLabels, onPaymentBannerClick }} />
                    </div>
                  )}
                  <div
                    className={cx({
                      "opportunity-description-container-with-flag": UPDATE_OPPORTUNITY_DETAILS
                    })}
                  >
                    <OpportunityDescription
                      title={
                        UPDATE_OPPORTUNITY_DETAILS
                          ? opportunitiesLabels.aboutThisOpportunity
                          : opportunitiesLabels.opportunityDescription
                      }
                      description={opportunity.description}
                      buttonLabel={opportunitiesLabels.viewAll}
                      size={size}
                    />
                  </div>
                  {UPDATE_OPPORTUNITY_DETAILS &&
                    gameAndCreatorCode &&
                    (participationStatus?.creatorCode || opportunity.hasGameCodes) && (
                      <section className="opportunity-game-and-creator-code-section">
                        <h5 className="opportunity-game-and-creator-code-header">
                          {participationStatus?.creatorCode
                            ? opportunitiesLabels.creatorCode.title
                            : opportunitiesLabels.getGameCode}
                        </h5>
                        <GameAndCreatorCodeCard
                          code={gameAndCreatorCode}
                          activationStartDate={
                            participationStatus?.creatorCode &&
                            participationStatus.creatorCodeActivationWindowStart(router.locale)
                          }
                          activationEndDate={
                            participationStatus?.creatorCode &&
                            participationStatus.creatorCodeActivationWindowEnd(router.locale)
                          }
                          copyLabel={opportunitiesLabels.creatorCode.copyLabel}
                          copyText={
                            participationStatus?.creatorCode
                              ? opportunitiesLabels.creatorCode.copyText
                              : opportunitiesLabels.copyGameCode
                          }
                          textCopied={opportunitiesLabels.creatorCode.textCopied}
                          disabled={
                            (participationStatus?.creatorCode &&
                              (!participationStatus.creatorCodeHasStarted() ||
                                participationStatus.creatorCodeHasEnded())) ||
                            gameAndCreatorCode === opportunitiesLabels?.gameCodePending
                          }
                          platform={participationStatus?.creatorCode ? "CREATOR_CODE" : platform}
                        />
                        {participationStatus?.creatorCode && !participationStatus.creatorCodeHasStarted() && (
                          <p className="opportunity-creator-code-info-text">
                            {opportunitiesLabels.creatorCode.codeNotYetActiveMessage}
                          </p>
                        )}
                        {participationStatus?.creatorCode && participationStatus.creatorCodeHasEnded() && (
                          <p className="opportunity-creator-code-info-text">
                            {opportunitiesLabels.creatorCode.codeExpiredMessage}
                          </p>
                        )}
                      </section>
                    )}
                  {UPDATE_OPPORTUNITY_DETAILS && opportunity?.payment && !opportunity.isSupportACreatorOpportunity() && (
                    <section className="opportunity-payment-section">
                      <h5 className="opportunity-payment-header">{opportunitiesLabels.payment}</h5>
                      <div className="opportunity-payment-details">
                        {opportunity.getPayment(opportunitiesLabels.sales)}
                      </div>
                    </section>
                  )}

                  {UPDATE_OPPORTUNITY_DETAILS && (
                    <section className="opportunity-key-dates-section">
                      <h5 className="opportunity-key-dates-header">{opportunitiesLabels.keyDates}</h5>
                      <ul data-testid="opportunity-key-dates-list">
                        {opportunity
                          .settingKeyDates(
                            opportunitiesLabels,
                            router.locale,
                            participationStatus?.creatorCode?.activationWindow
                          )
                          .map(({ label, value }, index) => {
                            return (
                              <li key={index} className="opportunity-key-date">
                                <span className="opportunity-key-date-label">{label}</span>&nbsp;
                                <span className="opportunity-key-date-value">{value}</span>
                              </li>
                            );
                          })}
                      </ul>
                      {opportunity.hasDeliverables && (
                        <section className="opportunity-view-deliverables-container">
                          <Button variant="primary" size="md" onClick={handleContentDeliverablesTab}>
                            {opportunitiesLabels.viewDeliverables}
                          </Button>
                        </section>
                      )}
                    </section>
                  )}
                  {UPDATE_OPPORTUNITY_DETAILS && opportunity.isInPerson() && (
                    <section className="opportunity-in-person-event-section">
                      <h5 className="opportunity-in-person-event-header">{opportunitiesLabels.inPersonEvent}</h5>
                      <section
                        className="opportunity-in-person-event-location-details"
                        data-testid="opportunity-in-person-event-location-details"
                      >
                        <p className="opportunity-in-person-event-location">{opportunitiesLabels.location}</p>
                        <p>{opportunity?.event?.address?.venue}</p>
                        <p>{opportunity.eventStreetAddress()}</p>
                        <p>{opportunity.eventAddress()}</p>
                        <p>{opportunity.event.address.country.name}</p>
                      </section>
                    </section>
                  )}

                  {UPDATE_OPPORTUNITY_DETAILS && opportunity.hasEvent && !opportunity.isInPerson() && isJoined && (
                    <div className="opportunity-online-event-card-section">
                      <h5 className="opportunity-online-event-card-header">{opportunitiesLabels.remoteEvent.title}</h5>
                      <OnlineEventDetailsCard
                        labels={{
                          joinEvent: opportunitiesLabels.remoteEvent.joinEvent,
                          copied: opportunitiesLabels.copied,
                          copy: opportunitiesLabels.eventDetails.copyPassword
                        }}
                        eventPeriodHasEnded={opportunity?.eventPeriodHasEnded()}
                        password={opportunity?.event.meetingPassword}
                        meetingLink={opportunity?.event.meetingLink}
                        info={remoteEventInfo(opportunity)}
                      />
                    </div>
                  )}

                  {UPDATE_OPPORTUNITY_DETAILS && opportunity.hasEvent && opportunity.event?.description && (
                    <div className="opportunity-event-information-section">
                      <h5 className="opportunity-event-information-header">{opportunitiesLabels.eventInformation}</h5>
                      <div className="opportunity-event-information-description formatted-content">
                        <div dangerouslySetInnerHTML={{ __html: opportunity.event.description }} />
                      </div>
                    </div>
                  )}

                  {!UPDATE_OPPORTUNITY_DETAILS && opportunity.hasEvent && opportunity.event?.description && (
                    <div className="opportunity-perks-additional-info-container">
                      <div className="opportunity-perks-additional-info">{opportunitiesLabels.additionalInfo}</div>
                      <div className="opportunity-perks-information-content formatted-content">
                        <div dangerouslySetInnerHTML={{ __html: opportunity.event.description }} />
                      </div>
                    </div>
                  )}
                  <div
                    className={cx({
                      "opportunity-perks-what-to-do-with-flag": UPDATE_OPPORTUNITY_DETAILS
                    })}
                  >
                    <div className="opportunity-perks-what-to-do">
                      <OpportunityDetails
                        title={opportunitiesLabels.toDoTitle}
                        features={opportunity?.whatToDo(opportunitiesLabels.whatToDoLabels) || []}
                      />
                    </div>
                    {!isJoined && opportunity?.hasGameCodes && opportunity?.gameCodes && (
                      <div className="opportunity-perks-free-game-code-details-container">
                        <OpportunityDetails
                          title={opportunitiesLabels.freeGameCode}
                          description={opportunitiesLabels.freeGameCodeDesc}
                          features={opportunity?.codesAvailability() || []}
                        />
                      </div>
                    )}
                  </div>
                  {!UPDATE_OPPORTUNITY_DETAILS ? (
                    isJoined && (
                      <div className="opportunity-perks-what-to-do">
                        <OpportunityDetails title={opportunitiesLabels.perks} features={getPerks(opportunity)} />
                      </div>
                    )
                  ) : (
                    <div className="opportunity-perks-what-to-do-with-flag">
                      <OpportunityDetails title={opportunitiesLabels.perks} features={getPerks(opportunity)} />
                    </div>
                  )}

                  {opportunity?.isSupportACreatorOpportunity() && (
                    <div
                      className={cx({
                        "opportunity-disclosure-policy": !UPDATE_OPPORTUNITY_DETAILS,
                        "opportunity-disclosure-policy-flag": UPDATE_OPPORTUNITY_DETAILS
                      })}
                    >
                      <ContentGuideLines
                        title={opportunitiesLabels.supportACreatorDisclosure.title}
                        subTitle={opportunitiesLabels.supportACreatorDisclosure.subTitle}
                        descriptions={[
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description1
                          },
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description2
                          },
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description3
                          },
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description4
                          },
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description5
                          },
                          {
                            descriptionText: opportunitiesLabels.supportACreatorDisclosure.description6
                          }
                        ]}
                      />
                    </div>
                  )}
                  <div
                    className={cx({
                      "point-of-contact-card-point-of-contact-container-with-flag": UPDATE_OPPORTUNITY_DETAILS
                    })}
                  >
                    {pointOfContact && isJoined && !creator?.accountInformation?.isFlagged && (
                      <PointOfContactDetails
                        title={opportunitiesLabels.pocLabels.opportunityContact}
                        pocLabels={pocLabels}
                        creator={creator}
                        opportunityId={router.query.id}
                        profileLabels={opportunitiesLabels?.pocLabels}
                        pocName={pointOfContact.name}
                        pocDiscordTag={pointOfContact.discordTag}
                        analytics={analytics}
                        buttons={layout.buttons}
                        imageUrl="/img/icons/ea-poc.png"
                        showEllipsis={true}
                      />
                    )}
                  </div>
                </div>
              )}
              {opportunity && !overview && opportunity?.hasDeliverables && (
                <>
                  <ContentDeliverablesTab
                    contentDeliverablesTabLabels={contentSubmissionTabLabels}
                    layout={layout}
                    opportunity={opportunity}
                    WATERMARKS_URL={WATERMARKS_URL}
                    isJoined={isJoined}
                    isSubmissionWindowClosed={opportunity.isSubmissionWindowClosed()}
                    hasContentSubmissionWindowStarted={opportunity.contentSubmissionWindowHasStarted()}
                    size={size}
                    opportunitiesLabels={opportunitiesLabels}
                    creator={creator}
                    accountConnected={accountConnected}
                    setConnectedAccount={setConnectedAccount}
                    t={t}
                    analytics={analytics}
                    locale={router.locale}
                    pages={pages}
                    error={error}
                    participationId={participationId}
                    YOUTUBE_HOSTS={YOUTUBE_HOSTS}
                    TWITCH_HOSTS={TWITCH_HOSTS}
                    INSTAGRAM_HOSTS={INSTAGRAM_HOSTS}
                    FACEBOOK_HOSTS={FACEBOOK_HOSTS}
                    TIKTOK_HOSTS={TIKTOK_HOSTS}
                    setUpdatedContent={setUpdatedContent}
                    myContentLabels={myContentLabels}
                    content={currentDeliverablePageContents}
                    thumbnail={opportunity.heroImage}
                    deliverablePages={deliverablePages}
                    FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED={FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED}
                    UPDATE_OPPORTUNITY_DETAILS={UPDATE_OPPORTUNITY_DETAILS}
                    FLAG_CONTENT_WITH_FINAL_REMARK={FLAG_CONTENT_WITH_FINAL_REMARK}
                  />
                </>
              )}
            </div>
          ))}
      </div>

      {declineModal && (
        <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{declineModalTitle}</ModalTitle>
            <ModalCloseButton ariaLabel={layout.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <p>{declineModalContent}</p>
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons {...{ layout, onDecline, onCancel: handleModalClose, spinner, cancelButtonRef }} />
          </ModalFooter>
        </ModalV2>
      )}
    </>
  );
}
