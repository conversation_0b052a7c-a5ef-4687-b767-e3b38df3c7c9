import { render, screen, waitFor } from "@testing-library/react";
import AgeRestrictionPage, {
  AgeRestrictionPageProps,
  AgeRestrictionPageLabels
} from "@components/pages/interested-creators/age-restriction/AgeRestrictionPage";
import { NextRouter } from "next/router";
import { ageRestrictionTranslations, commonTranslations } from "../../../../translations/index";
import { axe } from "jest-axe";
import userEvent from "@testing-library/user-event";

describe("AgeRestrictionPage", () => {
  const router = { push: jest.fn() } as unknown as NextRouter;
  const {
    header: { creatorNetwork },
    buttons: { close }
  } = commonTranslations;
  const { title, subTitle, bannerImageLabel } = ageRestrictionTranslations;
  const ageRestrictionPageLabels: AgeRestrictionPageLabels = {
    close,
    creatorNetwork,
    title,
    subTitle,
    bannerImageLabel
  };
  const ageRestrictionPageProps: AgeRestrictionPageProps = {
    ageRestrictionLabels: ageRestrictionPageLabels,
    router: router
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows age restriction page with correct labels", () => {
    render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    expect(screen.getByText(creatorNetwork)).toBeInTheDocument();
    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(subTitle)).toBeInTheDocument();
    expect(screen.getByText(close)).toBeInTheDocument();
  });

  it("redirects to log out page on clicking close button", async () => {
    render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /close/i }));

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("is accessible", async () => {
    const { container } = render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
