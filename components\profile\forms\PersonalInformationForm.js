import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction";
import FormTitle from "../../formTitle/FormTitle";
import { Toast, useToast } from "../../toast";
import { useRouter } from "next/router";
import { DateInput, Input } from "@eait-playerexp-cn/core-ui-kit";
import { isAdult } from "../../../utils";
import { useDependency } from "../../../src/context/DependencyContext";

const PersonalInformationForm = ({
  infoLabels,
  rules,
  creator,
  accountInformation,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const methods = useFormContext();
  const {
    configuration: { FLAG_PER_PROGRAM_PROFILE }
  } = useDependency();
  const { control, setValue, setError } = methods;
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timeToDisplay = Math.min(Math.max(infoLabels.success.personalInformation.length * 50, 2000), 7000);
  const router = useRouter();
  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={infoLabels.success.updatedInformationHeader}
            content={infoLabels.success.personalInformation}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: timeToDisplay
          }
        )}
      <div className="personal-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.personalInformation} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="personal-field-title">{infoLabels.labels.firstName}</div>

        <div className="personal-field">
          {!isEdit && accountInformation.firstName}
          {isEdit && (
            <Controller
              control={control}
              name="firstName"
              rules={rules.firstName}
              defaultValue={accountInformation.firstName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.firstName}
                  ariaLabel={infoLabels.labels.firstName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.lastName}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.lastName}
          {isEdit && (
            <Controller
              control={control}
              name="lastName"
              rules={rules.lastName}
              defaultValue={accountInformation.lastName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.lastName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.EAID}</div>
        <div className="personal-field">{accountInformation.defaultGamerTag}</div>

        <div className="personal-field-title">{infoLabels.labels.EAEmail}</div>
        <div className="personal-field">{accountInformation.originEmail}</div>

        <div className="personal-field-title">{infoLabels.labels.dateOfBirth}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.dateOfBirth.format("MMMM D, YYYY", router.locale)}
          {isEdit && (
            <Controller
              control={control}
              name="dateOfBirth"
              rules={rules.dateOfBirth}
              defaultValue={FLAG_PER_PROGRAM_PROFILE ? creator.dateOfBirth() : accountInformation.dateOfBirth.toDate()}
              render={({ field, fieldState: { error } }) => (
                <DateInput
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.dateOfBirth}
                  locale={router.locale}
                  maxDate={new Date()}
                  title={infoLabels.header.calendar}
                  cancelText={buttons.cancel}
                  okText={buttons.ok}
                  onCancel={(date) => {
                    if (isAdult(date)) {
                      setError(
                        "dateOfBirth",
                        { type: "manual", message: infoLabels.messages.ageMustBe18OrOlder },
                        { shouldFocus: true }
                      );
                    } else {
                      setError("dateOfBirth", null);
                    }
                    setValue("dateOfBirth", date);
                  }}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default PersonalInformationForm;
