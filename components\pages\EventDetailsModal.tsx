import React, { FC, Fragment, memo, useRef } from "react";
import {
  Button,
  copy1,
  Copy<PERSON>ooltip,
  Icon,
  Modal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { ActivationWindowDetails } from "./SupportACreatorModal";

type EventLabelValueProps = {
  label: string;
  value: string;
};

type EventDetailsLabels = {
  close: string;
  title: string;
  copied?: string;
  copy?: string;
  joinEvent?: string;
};

type CopyLabels = {
  copied: string;
  copy: string;
};

export type EventDetailsProps = {
  labels: EventDetailsLabels;
  onClose: () => void;
  eventWindow: Array<ActivationWindowDetails>;
  eventAddress?: EventLabelValueProps;
  isInPerson?: boolean;
  eventPeriodEnded?: EventLabelValueProps;
  remoteEventDetails?: EventLabelValueProps;
  meetingLink?: string;
  isMeetingLinkDisabled?: boolean;
};

export type EventWindowProps = {
  eventWindow: Array<ActivationWindowDetails>;
};

export type EventAddressProps = {
  eventAddress: EventLabelValueProps;
  copyLabels: CopyLabels;
};

export type RemoteEventProps = {
  remoteEventDetails: EventLabelValueProps;
  copyLabels: CopyLabels;
  isMeetingLinkDisabled: boolean;
};

export type EventPeriodEndedDetailProps = {
  eventPeriodEnded: EventLabelValueProps;
};

const EventWindow: FC<EventWindowProps> = memo(function EventWindow({ eventWindow }) {
  return (
    <section data-testid="event-detail-modal-window-details">
      {eventWindow?.map(({ label, value }, index) => {
        return (
          <Fragment key={index}>
            <h6 className="event-detail-modal-date-label">{label}</h6>
            <p className="event-detail-modal-date-value">{value}</p>
          </Fragment>
        );
      })}
    </section>
  );
});

const EventAddress: FC<EventAddressProps> = memo(function EventAddress({ eventAddress, copyLabels }) {
  return (
    <section data-testid="event-detail-modal-address-details">
      <h6 className="event-detail-modal-address-label">{eventAddress.label}</h6>
      <section className="event-detail-modal-address-value-section">
        <p className="event-detail-modal-address-value">{eventAddress.value}</p>
        <div className="event-detail-modal-address-copy-container">
          <CopyTooltip labels={copyLabels} textToBeCopied={eventAddress.value}>
            <Icon icon={copy1} className="event-detail-modal-address-copy-icon" color="#000000" />
          </CopyTooltip>
        </div>
      </section>
    </section>
  );
});

const RemoteEvent: FC<RemoteEventProps> = memo(function RemoteEvent({
  remoteEventDetails,
  copyLabels,
  isMeetingLinkDisabled
}) {
  return (
    <section data-testid="event-detail-modal-remote-details">
      <h6 className="event-detail-modal-remote-label">{remoteEventDetails.label}</h6>
      <section className="event-detail-modal-remote-value-section">
        <p className="event-detail-modal-remote-value">{remoteEventDetails.value}</p>
        {!isMeetingLinkDisabled && (
          <div className="event-detail-modal-remote-copy-container">
            <CopyTooltip labels={copyLabels} textToBeCopied={remoteEventDetails.value}>
              <Icon icon={copy1} className="event-detail-modal-remote-copy-icon" color="#000000" />
            </CopyTooltip>
          </div>
        )}
      </section>
    </section>
  );
});

const EventPeriodEndedDetail: FC<EventPeriodEndedDetailProps> = memo(function EventPeriodEnded({ eventPeriodEnded }) {
  return (
    <section data-testid="event-detail-modal-event-ended-details">
      <h6 className="event-detail-modal-event-ended-label">{eventPeriodEnded.label}</h6>
      <p className="event-detail-modal-event-ended-value">{eventPeriodEnded.value}</p>
    </section>
  );
});

const EventDetailsModal: FC<EventDetailsProps> = ({
  labels,
  onClose,
  eventWindow,
  eventAddress,
  isInPerson,
  eventPeriodEnded,
  remoteEventDetails,
  meetingLink,
  isMeetingLinkDisabled
}) => {
  const closeModalButtonRef = useRef<HTMLButtonElement | null>(null);
  return (
    <ModalV2 closeButtonRef={closeModalButtonRef}>
      <ModalHeader>
        <ModalTitle>
          <span className="event-detail-modal-details-modal-header">{labels.title}</span>
        </ModalTitle>
        <ModalCloseButton ariaLabel={labels.close} closeButtonRef={closeModalButtonRef} />
      </ModalHeader>
      <ModalBody>
        <EventWindow eventWindow={eventWindow} />
        {isInPerson && !eventPeriodEnded && (
          <EventAddress eventAddress={eventAddress} copyLabels={{ copy: labels.copy, copied: labels.copied }} />
        )}
        {!isInPerson && !eventPeriodEnded && remoteEventDetails && (
          <RemoteEvent
            remoteEventDetails={remoteEventDetails}
            copyLabels={{ copy: labels.copy, copied: labels.copied }}
            isMeetingLinkDisabled={isMeetingLinkDisabled}
          />
        )}
        {eventPeriodEnded && <EventPeriodEndedDetail eventPeriodEnded={eventPeriodEnded} />}
      </ModalBody>
      <ModalFooter showDivider>
        <Button size="sm" variant="secondary" dark onClick={onClose} ref={closeModalButtonRef}>
          {labels.close}
        </Button>
        {!isInPerson && (
          <>
            {!eventPeriodEnded && meetingLink ? (
              <a
                href={meetingLink}
                target="_blank"
                rel="noreferrer"
                className="btn btn-primary btn-sm"
                aria-label={labels.joinEvent}
              >
                {labels.joinEvent}
              </a>
            ) : (
              <Button variant="primary" size="sm" dark disabled aria-label={labels.joinEvent}>
                {labels.joinEvent}
              </Button>
            )}
          </>
        )}
      </ModalFooter>
    </ModalV2>
  );
};

export default EventDetailsModal;
