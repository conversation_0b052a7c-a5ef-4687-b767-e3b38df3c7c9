import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import ApiContainer from "../../src/ApiContainer";
import LegacyUpdateApplicationController from "../../src/controllers/LegacyUpdateApplicationController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";
import LegacyStartApplicationController from "@src/controllers/StartApplicationController";
import { StartApplicationController } from "@eait-playerexp-cn/interested-creators-authentication-plugins";
import UpdateApplicationController from "@src/controllers/UpdateApplicationController";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  // GET request occurs with unauthenticated users, skip the check in that case
  .use(async (req, res, next) => {
    if (req.method === "GET") next();
    else {
      await (config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)(
        req as unknown as NextApiRequestWithSession,
        res,
        next
      );
    }
  })
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(
      config.FLAG_PER_PROGRAM_PROFILE ? StartApplicationController : LegacyStartApplicationController
    );
    await controller.handle(req, res);
  })
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(
      config.FLAG_PER_PROGRAM_PROFILE ? UpdateApplicationController : LegacyUpdateApplicationController
    );
    await controller.handle(req, res);
  });

export default router.handler({ onError });
