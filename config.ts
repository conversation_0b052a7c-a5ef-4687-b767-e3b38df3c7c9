import { LogLevel } from "@eait-playerexp-cn/activity-logger";
import {
  Environment,
  RedisCacheConfiguration,
  RedisClientOptions,
  ScaleReadType,
  SessionOptions
} from "@eait-playerexp-cn/server-kernel";

const env = new Environment(process.env);

const creatorTypesFallback = env.parseArray("FALLBACK_CREATOR_TYPES").map((creatorType) => {
  // Parsed creator types have the following format {"label": "imageAsIcon"}
  return {
    value: Object.keys(creatorType)[0].toUpperCase(),
    label: Object.keys(creatorType)[0],
    imageAsIcon: Object.values(creatorType)[0]
  };
});
const hasAllTikTokScopes = (scopes: string) => validateScopes(scopes, env.parseCsv("TIKTOK_SCOPES"));

const hasAllFacebookScopes = (scopes: string) => validateScopes(scopes, env.parseCsv("FACEBOOK_SCOPES"));

const validateScopes = (selectedScopes: string, requiredScopes: string[]) => {
  const selectedUserScopes = selectedScopes?.split(",");
  return requiredScopes.filter((scope) => !selectedUserScopes.includes(scope)).length === 0;
};

const redisClient = new RedisClientOptions(
  env.get("REDIS_HOST"),
  env.parseNumber("REDIS_PORT"),
  env.get("REDIS_SCALE_READ") as ScaleReadType
);

const config = {
  LOGIN_URL: env.getURL("LOGIN_URL"),
  LOGOUT_URL: env.getURL("LOGOUT_URL"),
  LOGIN_REDIRECT_URI: env.getURL("LOGIN_REDIRECT_URI"),
  CREATE_ACCOUNT_URL: env.get("CREATE_ACCOUNT_URL"),
  CLIENT_ID: env.get("CLIENT_ID"),
  ACCESS_TOKEN_BASE_URL: env.get("ACCESS_TOKEN_BASE_URL"),
  OPERATIONS_API_BASE_URL: env.get("OPERATIONS_API_BASE_URL"),
  CONTENT_SCANNING_API_BASE_URL: env.get("CONTENT_SCANNING_API_BASE_URL"),
  CONTENT_SUBMISSION_BASE_URL: env.get("CONTENT_SUBMISSION_BASE_URL"),
  FLAG_SIGNED_URL_V1_ENABLED: env.parseBoolean("FLAG_SIGNED_URL_V1_ENABLED"),
  FLAG_SIGNED_URL_V2_ENABLED: env.parseBoolean("FLAG_SIGNED_URL_V2_ENABLED"),
  METADATA_API_BASE_URL: env.get("METADATA_API_BASE_URL"),
  COMMUNICATIONS_API_BASE_URL: env.get("COMMUNICATIONS_API_BASE_URL"),
  LEGAL_API_BASE_URL: env.get("LEGAL_API_BASE_URL"),
  PAYMENTS_API_BASE_URL: env.get("PAYMENTS_API_BASE_URL"),
  CONTENT_MANAGEMENT_API_BASE_URL: env.get("CONTENT_MANAGEMENT_API_BASE_URL"),
  API_CLIENT_ID: env.get("API_CLIENT_ID"),
  API_CLIENT_SECRET: env.get("API_CLIENT_SECRET"),
  SESSION_COOKIE_NAME: env.get("SESSION_COOKIE_NAME"),
  GTM_AUTH: env.get("GTM_AUTH"),
  GTM_PREVIEW: env.get("GTM_PREVIEW"),
  DEFAULT_FRANCHISE_IMAGE: env.get("DEFAULT_FRANCHISE_IMAGE"),
  YOUTUBE_CLIENT_ID: env.get("YOUTUBE_CLIENT_ID"),
  YOUTUBE_CLIENT_SECRET: env.get("YOUTUBE_CLIENT_SECRET"),
  YOUTUBE_CLIENT_REDIRECT_URI: env.get("YOUTUBE_CLIENT_REDIRECT_URI"),
  YOUTUBE_CLIENT_REDIRECT_URL: env.getURL("YOUTUBE_CLIENT_REDIRECT_URI"),
  YOUTUBE_SCOPES: env.parseCsv("YOUTUBE_SCOPES"),
  TWITCH_CLIENT_ID: env.get("TWITCH_CLIENT_ID"),
  TWITCH_CLIENT_REDIRECT_URI: env.get("TWITCH_CLIENT_REDIRECT_URI"),
  TWITCH_CLIENT_REDIRECT_URL: env.getURL("TWITCH_CLIENT_REDIRECT_URI"),
  TWITCH_SCOPES: env.get("TWITCH_SCOPES"),
  TIKTOK_SCOPES: env.get("TIKTOK_SCOPES"),
  TIKTOK_CLIENT_ID: env.get("TIKTOK_CLIENT_ID"),
  TIKTOK_CLIENT_REDIRECT_URI: env.get("TIKTOK_CLIENT_REDIRECT_URI"),
  TIKTOK_CLIENT_REDIRECT_URL: env.getURL("TIKTOK_CLIENT_REDIRECT_URI"),
  TIKTOK_AUTH_BASE_URI: env.get("TIKTOK_AUTH_BASE_URI"),
  FACEBOOK_SCOPES: env.get("FACEBOOK_SCOPES"),
  FACEBOOK_CLIENT_ID: env.get("FACEBOOK_CLIENT_ID"),
  FACEBOOK_CLIENT_REDIRECT_URI: env.get("FACEBOOK_CLIENT_REDIRECT_URI"),
  FACEBOOK_CLIENT_REDIRECT_URL: env.getURL("FACEBOOK_CLIENT_REDIRECT_URI"),
  FACEBOOK_STATE: env.get("FACEBOOK_STATE"),
  FACEBOOK_API_VERSION: env.get("FACEBOOK_API_VERSION"),
  INSTAGRAM_CLIENT_ID: env.get("INSTAGRAM_CLIENT_ID"),
  INSTAGRAM_CLIENT_REDIRECT_URI: env.get("INSTAGRAM_CLIENT_REDIRECT_URI"),
  INSTAGRAM_CLIENT_REDIRECT_URL: env.getURL("INSTAGRAM_CLIENT_REDIRECT_URI"),
  INSTAGRAM_STATE: env.get("INSTAGRAM_STATE"),
  INSTAGRAM_SCOPE: env.get("INSTAGRAM_SCOPE"),
  INSTAGRAM_API_VERSION: env.get("INSTAGRAM_API_VERSION"),
  hasAllFacebookScopes,
  hasAllTikTokScopes,
  DISCORD_CLIENT_ID: env.get("DISCORD_CLIENT_ID"),
  DISCORD_CLIENT_REDIRECT_URI: env.get("DISCORD_CLIENT_REDIRECT_URI"),
  DISCORD_CLIENT_REDIRECT_URL: env.getURL("DISCORD_CLIENT_REDIRECT_URI"),
  DISCORD_SCOPES: env.get("DISCORD_SCOPES"),
  WATERMARKS_URL: env.get("WATERMARKS_URL"),
  APP_ENV: env.get("APP_ENV"),
  APP_DEBUG: env.parseBoolean("APP_DEBUG"),
  redisClient,
  PACTSAFE_ID: env.get("PACTSAFE_ID"),
  sessionOptions: {
    cookies: {
      secret: env.get("COOKIE_PASSWORD"),
      httpOnly: env.parseBoolean("COOKIE_HTTP_ONLY"),
      sameSite: env.get("COOKIE_SAME_SITE"),
      domain: env.get("COOKIE_DOMAIN", undefined),
      secure: env.parseBoolean("COOKIE_SECURE")
    },
    ttl: env.parseNumber("SESSION_TTL"),
    proxy: env.parseBoolean("SESSION_PROXY")
  } as SessionOptions,
  cacheOptions: {
    cachePrefix: env.get("CACHE_PREFIX"),
    redisClient
  } as RedisCacheConfiguration,
  LOG_LEVEL: env.get("LOG_LEVEL") as LogLevel,
  TERMS_STATUS_CACHE_TTL: env.parseNumber("TERMS_STATUS_CACHE_TTL"),
  HTTP_REQUEST_TIMEOUT: env.parseNumber("HTTP_REQUEST_TIMEOUT"),
  DEFAULT_AVATAR_IMAGE: env.get("DEFAULT_AVATAR_IMAGE"),
  SERVICE_NAME: env.get("SERVICE_NAME"),
  NOTIFICATIONS_MFE_BASE_URL: env.get("NOTIFICATIONS_MFE_BASE_URL"),
  PROGRAM_CODE: env.get("PROGRAM_CODE"),
  NOTIFICATION_BASE_URLS: env.parseObject("NOTIFICATION_BASE_URLS"),
  SINGLE_PROGRAM_NOTIFICATIONS: env.parseBoolean("SINGLE_PROGRAM_NOTIFICATIONS"),
  DEFAULT_NOTIFICATION_PROGRAM: env.get("DEFAULT_NOTIFICATION_PROGRAM"),
  SENTRY_DSN: env.get("SENTRY_DSN"),
  RELEASE_VERSION: env.get("RELEASE_VERSION", "1.0.0.dev"),
  FLAG_INITIAL_MESSAGE: env.parseBoolean("FLAG_INITIAL_MESSAGE"),
  INTERESTED_CREATOR_REAPPLY_PERIOD: env.parseBoolean("INTERESTED_CREATOR_REAPPLY_PERIOD"),
  FALLBACK_CREATOR_TYPES: creatorTypesFallback,
  AMPLITUDE_API_KEY: env.get("AMPLITUDE_API_KEY"),
  AMPLITUDE_ENV: env.get("AMPLITUDE_ENV"),
  YOUTUBE_HOSTS: env.parseArray("YOUTUBE_HOSTS"),
  TWITCH_HOSTS: env.parseArray("TWITCH_HOSTS"),
  INSTAGRAM_HOSTS: env.parseArray("INSTAGRAM_HOSTS"),
  FACEBOOK_HOSTS: env.parseArray("FACEBOOK_HOSTS"),
  TIKTOK_HOSTS: env.parseArray("TIKTOK_HOSTS"),
  CN_LAUNCH_DATE: env.get("CN_LAUNCH_DATE"),
  SUPPORTED_LOCALES: env.parseArray<string>("SUPPORTED_LOCALES"),
  UPDATE_OPPORTUNITY_DETAILS: env.parseBoolean("UPDATE_OPPORTUNITY_DETAILS"),
  RE_APPLY_THRESHOLD_IN_DAYS: env.get("RE_APPLY_THRESHOLD_IN_DAYS"),
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED: env.parseBoolean("FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED"),
  INITIAL_MESSAGE_TITLE: env.get("INITIAL_MESSAGE_TITLE"),
  INITIAL_MESSAGE_DESCRIPTION: env.get("INITIAL_MESSAGE_DESCRIPTION"),
  ANALYTICS_SAMPLE_RATE: env.parseNumber("ANALYTICS_SAMPLE_RATE"),
  FLAG_COUNTRIES_BY_TYPE: env.parseBoolean("FLAG_COUNTRIES_BY_TYPE"),
  FLAG_OBSERVABILITY: env.parseBoolean("FLAG_OBSERVABILITY"),
  FLAG_NEW_NAVIGATION_ENABLED: env.parseBoolean("FLAG_NEW_NAVIGATION_ENABLED"),
  FLAG_NEW_FOOTER_ENABLED: env.parseBoolean("FLAG_NEW_FOOTER_ENABLED"),
  FLAG_COMMUNICATIONS_API_CLIENT: env.parseBoolean("FLAG_COMMUNICATIONS_API_CLIENT"),
  FLAG_ONBOARDING_CUSTOM_LINKS: env.parseBoolean("FLAG_ONBOARDING_CUSTOM_LINKS"),
  APPLICATIONS_MFE_BASE_URL: env.get("APPLICATIONS_MFE_BASE_URL"),
  pagesMicroCopy: {
    notifications: env.parseArray<string>("MICROCOPY_NOTIFICATIONS_PAGE"),
    noAccount: env.parseArray<string>("MICROCOPY_NO_ACCOUNT_PAGE"),
    ageRestriction: env.parseArray<string>("MICROCOPY_AGE_RESTRICTION_PAGE"),
    applicationAccepted: env.parseArray<string>("MICROCOPY_APPLICATION_ACCEPTED_PAGE"),
    applicationRejected: env.parseArray<string>("MICROCOPY_APPLICATION_REJECTED_PAGE"),
    applicationPending: env.parseArray<string>("MICROCOPY_APPLICATION_PENDING_PAGE"),
    applicationStart: env.parseArray<string>("MICROCOPY_APPLICATION_START_PAGE"),
    applicationComplete: env.parseArray<string>("MICROCOPY_APPLICATION_COMPLETE_PAGE"),
    information: env.parseArray<string>("MICROCOPY_INFORMATION_PAGE"),
    creatorType: env.parseArray<string>("MICROCOPY_CREATOR_TYPE_PAGE"),
    franchisesYouPlay: env.parseArray<string>("MICROCOPY_FRANCHISES_YOU_PLAY_PAGE")

  },
  FLAG_CONTENT_WITH_FINAL_REMARK: env.parseBoolean("FLAG_CONTENT_WITH_FINAL_REMARK"),
  SEARCH_CREATORS_API_WITH_PROGRAM: env.parseBoolean("SEARCH_CREATORS_API_WITH_PROGRAM"),
  FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM: env.parseBoolean("FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM"),
  FLAG_SUBMITTED_CONTENT_WITH_PROGRAM: env.parseBoolean("FLAG_SUBMITTED_CONTENT_WITH_PROGRAM"),
  FLAG_OPPORTUNITIES_PER_PROGRAM: env.parseBoolean("FLAG_OPPORTUNITIES_PER_PROGRAM"),
  FLAG_CREATORS_API_WITH_PROGRAM: env.parseBoolean("FLAG_CREATORS_API_WITH_PROGRAM"),
  MENU_ITEMS: env.parseObject("MENUITEM_NAMES"),
  FLAG_OPPORTUNITIES_API_CLIENT: env.get("FLAG_OPPORTUNITIES_API_CLIENT"),
  OPPORTUNITIES_API_BASE_URL: env.get("OPPORTUNITIES_API_BASE_URL"),
  INTERESTED_CREATOR: env.parseBoolean("INTERESTED_CREATOR"),
  FLAG_PER_PROGRAM_PROFILE: env.parseBoolean("FLAG_PER_PROGRAM_PROFILE"),
  CREATORS_API_BASE_URL: env.get("CREATORS_API_BASE_URL"),
  FLAG_SEND_EMAIL_WITH_PROGRAM: env.parseBoolean("FLAG_SEND_EMAIL_WITH_PROGRAM"),
  FLAG_INTERESTED_CREATOR_MFE: env.parseBoolean("FLAG_INTERESTED_CREATOR_MFE")
};

export default config;
