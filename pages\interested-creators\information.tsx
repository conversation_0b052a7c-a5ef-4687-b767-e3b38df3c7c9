import "reflect-metadata";
import React, { memo, useCallback, useMemo, useState } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Error from "../_error";
import { useAppContext } from "@src/context";
import InterestedCreatorsInformationPage from "../../components/pages/interested-creators/InterestedCreatorsInformationPage";
import { useTranslation } from "next-i18next";
import labelsCommon from "../../config/translations/common";
import MigrationLayout from "../../components/MigrationLayout";
import labelsInformation from "../../config/translations/information";
import labelsAddContent from "../../config/translations/add-content";
import labelsOpportunities from "../../config/translations/opportunities";
import labelsCommunicationPreferences from "../../config/translations/communication-preferences";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import CreatorForm, { CommunicationFormRules, CreatorFormRules } from "../../components/FormRules/CreatorForm";
import flags from "../../utils/feature-flags";
import { FormLabels } from "@components/forms/InterestedCreatorInformationForm";
import {
  Country,
  Language,
  PageLabels,
  PreferredLanguage
} from "@components/pages/interested-creators/InterestedCreatorsInformationPage";
import { useRouter } from "next/router";
import { AuthenticatedUser, AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import labelsBreadCrumb from "../../config/translations/breadcrumb";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import labelsConnectAccounts from "../../config/translations/connect-accounts";
import labelsProfile from "../../config/translations/profile";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorInformationProps from "@src/serverprops/InterestedCreatorInformationProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import { useDependency } from "@src/context/DependencyContext";

export type ContentUrl = {
  url: string;
  followers: string;
};
export type ContentUrlWithoutFollowers = {
  url: string;
};
export type Information = {
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  defaultGamerTag: string;
  originEmail: string;
  contentLanguages?: Array<Language>;
  preferredLanguage?: PreferredLanguage;
  contentUrls?: Array<ContentUrl>;
  country?: Country;
  dateOfBirth: string;
  countryCode?: string;
  contentAccounts?: Array<ContentUrl>;
};
type Fbpage = {
  accessToken: string;
  id: string;
  name: string;
};
export type Fbpages = {
  pages: Array<Fbpage>;
};
export type InterestedCreatorInformationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator?: InterestedCreator & Information;
  accessToken?: string;
  user: AuthenticatedUser;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  FLAG_COUNTRIES_BY_TYPE: boolean;
  pages: Array<Fbpage>;
};
export type Rules = Partial<CreatorFormRules & CommunicationFormRules>;

export const interestedCreatorPages = {
  information: "Information",
  creatorTypes: "CreatorTypes",
  franchises: "FranchisesYouPlay"
};

export default memo(function InterestedCreatorInformation({
  interestedCreator,
  pages,
  accessToken,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  FLAG_COUNTRIES_BY_TYPE
}: InterestedCreatorInformationProps) {
  const { analytics } = useDependency();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const [accountToRemove, setAccountToRemove] = useState(null);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const { locale } = useRouter();
  const router = useRouter();
  const { t } = useTranslation([
    "common",
    "breadcrumb",
    "information",
    "communication-preferences",
    "opportunities",
    "connect-accounts",
    "add-content"
  ]);
  const { layout, infoLabels, formLabels, pageLabels, translation, connectAccountLabels } = useMemo(() => {
    const infoLabels = labelsInformation(t);
    const addContentLabels = labelsAddContent(t);
    const opportunitiesLabels = labelsOpportunities(t);
    const translation = labelsCommunicationPreferences(t);
    const layout = {
      ...labelsCommon(t),
      ...labelsBreadCrumb(t),
      ...labelsOpportunities(t)
    };
    const {
      submitContentLabels: { addAnother }
    } = opportunitiesLabels;
    const {
      labels: { contentLanguagesTitle, contentLanguagesDescription, languageTitle, languageDescription, language }
    } = translation;
    const {
      buttons: { cancel, next, yes, no, remove, ok, close },
      header: { calendar }
    } = layout;
    const {
      infoTitle,
      confirmationDesc1,
      confirmationDesc2,
      modalConfirmationTitle,
      interestedCreatorTitle,
      interestedUserDescription1,
      interestedUserDescription2,
      labels: {
        firstName,
        lastName,
        dateOfBirth,
        preferredEmail,
        country,
        contentMediaTitle,
        contentMediaDescription,
        contentUrlPlaceholder,
        contentUrl,
        contentFollowers,
        contentFollowersPlaceholder,
        contentLanguage,
        selectCountry,
        connectSocialMediaAccountTitle,
        connectSocialMediaAccountDescription,
        additionalContentAndWebsiteTitle,
        additionalContentAndWebsiteDescription,
        websiteUrlLabel,
        additionalLinkPlaceholder
      },
      messages: { duplicateUrl, urlScanFailed, followersMaxLength, invalidUrl, ageMustBe18OrOlder }
    } = infoLabels;
    const { addMoreUrlLabel } = addContentLabels;
    const formLabels: FormLabels = Object.assign(
      {},
      {
        infoTitle,
        firstName,
        lastName,
        dateOfBirth,
        preferredEmail,
        country,
        contentMediaTitle,
        contentMediaDescription,
        contentUrlPlaceholder,
        contentUrl,
        contentFollowers,
        contentFollowersPlaceholder,
        addAnother,
        contentLanguagesTitle,
        contentLanguagesDescription,
        contentLanguage,
        languageTitle,
        languageDescription,
        language,
        cancel,
        next,
        duplicateUrl,
        urlScanFailed,
        followersMaxLength,
        selectCountry,
        remove,
        ok,
        calendar,
        close,
        connectSocialMediaAccountTitle,
        connectSocialMediaAccountDescription,
        additionalContentAndWebsiteTitle,
        additionalContentAndWebsiteDescription,
        websiteUrlLabel,
        additionalLinkPlaceholder,
        addMoreUrlLabel,
        invalidUrl,
        ageMustBe18OrOlder
      }
    );
    const pageLabels: PageLabels = Object.assign(
      {},
      {
        confirmationDesc1,
        confirmationDesc2,
        modalConfirmationTitle,
        interestedCreatorTitle,
        interestedUserDescription1,
        interestedUserDescription2,
        yes,
        no
      }
    );
    const connectAccountLabels = { ...labelsConnectAccounts(t), ...labelsProfile(t) };
    return { layout, translation, formLabels, pageLabels, infoLabels, connectAccountLabels };
  }, [t]);
  const allRules = {
    ...CreatorForm.rules(infoLabels),
    ...CreatorForm.communicationRules(translation)
  };
  const {
    firstName,
    lastName,
    dateOfBirth,
    interestedCreatorCountry,
    preferredEmail,
    contentLanguage,
    url,
    followers
  } = allRules;
  const rules: Rules = {
    firstName,
    lastName,
    dateOfBirth,
    interestedCreatorCountry,
    preferredEmail,
    contentLanguage,
    url,
    followers
  };
  const onClose = useCallback(() => {
    setShowConfirmation(!showConfirmation);
  }, [showConfirmation]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={infoLabels.interestedCreatorTitle}
      className="interested-creator"
      {...{ ...layout, onClose }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
    >
      <InterestedCreatorsInformationPage
        {...{
          formLabels,
          pageLabels,
          onClose,
          showConfirmation,
          interestedCreator,
          rules,
          stableDispatch,
          setShowConfirmation,
          router,
          locale,
          analytics,
          showAddConfirmation,
          setShowAddConfirmation,
          layout,
          connectAccountLabels,
          accountToRemove,
          setAccountToRemove,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          pages,
          accessToken,
          INTERESTED_CREATOR_REAPPLY_PERIOD,
          FLAG_COUNTRIES_BY_TYPE
        }}
      />
    </MigrationLayout>
  );
});

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorInformationProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorInformationProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.information);
  const { pages, accessToken } = (req.session.fbPages || {
    pages: [],
    accessToken: ""
  }) as { pages: string[]; accessToken: string };

  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      ...(await serverSideTranslations(locale, [
        "common",
        "breadcrumb",
        "information",
        "communication-preferences",
        "opportunities",
        "connect-accounts",
        "add-content"
      ])),
      pages,
      accessToken,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled()
    }
  };
};
