import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import YouTubeClient from "../../src/channels/youtube/YouTubeClient";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession, verifyInterestedCreatorSession } from "@eait-playerexp-cn/server-kernel";
import onOAuthError from "@src/api/OAuthErrorHandler";
import session from "@src/middleware/Session";
import { verifySession } from "@eait-playerexp-cn/identity";
import config from "../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .get((_req: NextApiRequestWithSession, res: NextApiResponse) => {
    const client = ApiContainer.get(YouTubeClient);

    res.setHeader("Location", client.authorizationUrl());
    res.status(302);
    res.end();
  });

export default router.handler({ onError: onOAuthError });
