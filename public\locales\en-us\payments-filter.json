{"filters": "Filters", "dateRange": "Date Range", "startDate": "Start date", "endDate": "End date", "paymentStatus": "Payment Status", "opportunityType": "Type", "applyFilters": "Apply", "startDateError": "Start date must be before the end date", "endDateError": "End date must be after the start date", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "sameDateError": "Start date and end date cannot be the same", "range": {"allTime": "All Time", "thisMonth": "This Month", "past30Days": "Past 30 Days", "past90Days": "Past 90 Days", "past6Months": "Past 6 Months", "yearToDate": "Year-to-Date", "lastYear": "Last Year", "custom": "Custom"}, "status": {"all": "All", "processed": "Processed", "pending": "Pending"}, "type": {"all": "All", "opportunity": "Opportunity", "creatorCode": "Creator Code", "simsMakerProgram": "Sims Maker Program"}}