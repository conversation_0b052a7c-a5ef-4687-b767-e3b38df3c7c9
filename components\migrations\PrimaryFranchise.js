import Search from "../Search";
import React, { useEffect, useState } from "react";
import SecondaryFranchise from "./SecondaryFranchise";
import classNames from "classnames/bind";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { Controller, useFormContext } from "react-hook-form";
import { useDependency } from "../../src/context/DependencyContext";

const PAGE_SIZE = 8;

const PrimaryFranchise = ({ franchises, creator, franchisesYouPlayLabels }) => {
  const methods = useFormContext();
  const {
    configuration: { FLAG_PER_PROGRAM_PROFILE }
  } = useDependency();
  const { control } = methods;
  const [franchiseOptions, setFranchiseOptions] = useState([]);
  const [showLoadMore, setShowLoadMore] = useState(false);
  const [franchise, setPrimaryFranchise] = useState(null);
  const updatePrimaryCard = (selectedItem) => {
    setPrimaryFranchise(selectedItem);
  };
  const secondaryFranchises = (creator && creator.preferredSecondaryFranchises) || [];
  const secondaryProps = {
    name: "secondaryFranchise",
    control: control,
    values: secondaryFranchises,
    disabled: franchise === null,
    items: franchiseOptions.map((item) => {
      return {
        ...item,
        checked: secondaryFranchises.filter((franchiseItem) => franchiseItem.value === item.value).length > 0
      };
    })
  };
  const addFranchiseOptions = () => {
    if (franchises.length > franchiseOptions.length) {
      if (franchises.length - franchiseOptions.length > PAGE_SIZE) {
        setFranchiseOptions(
          franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchiseOptions.length + PAGE_SIZE))
        );
      } else {
        setFranchiseOptions(franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchises.length)));
      }
    }
  };
  useEffect(() => {
    if (franchises.length > PAGE_SIZE) {
      setFranchiseOptions(franchises.slice(0, PAGE_SIZE));
    } else {
      setFranchiseOptions(franchises);
    }
  }, [franchises]);
  useEffect(() => {
    if (franchises.length > franchiseOptions.length) {
      setShowLoadMore(true);
    } else {
      setShowLoadMore(false);
    }
  }, [franchiseOptions]);
  useEffect(() => {
    if (FLAG_PER_PROGRAM_PROFILE) {
      if (creator && creator.preferredPrimaryFranchise) {
        franchises.filter((item) => {
          if (item.value === creator.preferredPrimaryFranchise.value) {
            setPrimaryFranchise(item);
          }
        });
      }
    } else {
      if (creator && creator.preferredPrimaryFranchises) {
        franchises.filter((item) => {
          if (item.value === creator.preferredPrimaryFranchises.value) {
            setPrimaryFranchise(item);
          }
        });
      }
    }
  }, [creator, franchises]);

  return (
    <>
      <div className="mg-primary-franchise-container">
        <div className="mg-primary-franchise">
          <h4 className="mg-primary-franchise-title">{franchisesYouPlayLabels.primaryFranchiseTitle}</h4>
          <div className="mg-primary-franchise-subtitle">{franchisesYouPlayLabels.primaryFranchiseSubTitle}</div>
        </div>
        {franchises && !!franchises.length && (
          <Controller
            control={control}
            name="primaryFranchise"
            rules={{ required: franchisesYouPlayLabels.messages.primaryFranchise }}
            defaultValue={
              creator &&
              (FLAG_PER_PROGRAM_PROFILE ? creator.preferredPrimaryFranchise : creator.preferredPrimaryFranchises)
            }
            render={({ field, fieldState: { error } }) => (
              <Search
                errorMessage={(error && error.message) || ""}
                {...field}
                onChange={(item) => {
                  updatePrimaryCard(item);
                  field.onChange(item);
                }}
                options={franchises}
                placeholder={franchisesYouPlayLabels.labels.primaryFranchise}
              />
            )}
          />
        )}
        <div className="primary-franchise-option">
          <img
            alt="Selected Franchise image"
            className={classNames(
              {
                "primary-franchise-selected": franchise
              },
              "primary-franchise-option-image"
            )}
            src={(franchise && franchise.image) || "/img/franchises-you-play/franchise-unselected.png"}
          />
        </div>
      </div>
      <div className="mg-sc-franchise-container">
        <div
          className={classNames({
            "mg-secondary-franchise-disabled": !franchise
          })}
        >
          <h4 className="mg-primary-franchise-title">{franchisesYouPlayLabels.secondaryFranchiseTitle}</h4>
          <div className="mg-primary-franchise-subtitle">{franchisesYouPlayLabels.secondaryFranchiseSubTitle}</div>
        </div>
        {secondaryProps && <SecondaryFranchise {...secondaryProps} />}
        {franchise && showLoadMore && (
          <div className="secondary-franchise-load-more">
            <Button variant="secondary" size="md" onClick={addFranchiseOptions}>
              {franchisesYouPlayLabels.labels.loadMore}
            </Button>
          </div>
        )}
      </div>
    </>
  );
};
export default PrimaryFranchise;
