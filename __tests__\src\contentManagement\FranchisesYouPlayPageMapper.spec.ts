import { FranchisesYouPlayPageMapper } from "@src/contentManagement/FranchisesYouPlayPageMapper";

describe("FranchisesYouPlayPageMapper", () => {
  const microCopies = {
    "franchisesYouPlay.title": "Title",
    "franchisesYouPlay.primaryFranchiseTitle": "Primary Franchise Title",
    "franchisesYouPlay.primaryFranchiseSubTitle": "Primary Franchise SubTitle",
    "franchisesYouPlay.secondaryFranchiseTitle": "Secondary Franchise Title",
    "franchisesYouPlay.secondaryFranchiseSubTitle": "Secondary Franchise SubTitle",
    "franchisesYouPlay.description": "Description",
    "franchisesYouPlay.messages.primaryFranchise": "Primary Franchise Message",
    "franchisesYouPlay.success.updatedInformationHeader": "Updated Information Header",
    "franchisesYouPlay.success.franchiseUpdate": "Franchise Update",
    "franchisesYouPlay.labels.primaryFranchise": "Primary Franchise Label",
    "franchisesYouPlay.labels.loadMore": "Load More",
    "franchisesYouPlay.franchisesYouPlayPageTitle": "SaC - Franchises You Play"
  };

  it("maps franchises you play page labels", () => {
    const mapper = new FranchisesYouPlayPageMapper();
    const labels = mapper.map(microCopies).franchisesYouPlayPageLabels;

    expect(labels.title).toEqual("Title");
    expect(labels.primaryFranchiseTitle).toEqual("Primary Franchise Title");
    expect(labels.primaryFranchiseSubTitle).toEqual("Primary Franchise SubTitle");
    expect(labels.secondaryFranchiseTitle).toEqual("Secondary Franchise Title");
    expect(labels.secondaryFranchiseSubTitle).toEqual("Secondary Franchise SubTitle");
    expect(labels.description).toEqual("Description");
    expect(labels.messages.primaryFranchise).toEqual("Primary Franchise Message");
    expect(labels.success.updatedInformationHeader).toEqual("Updated Information Header");
    expect(labels.success.franchiseUpdate).toEqual("Franchise Update");
    expect(labels.labels.primaryFranchise).toEqual("Primary Franchise Label");
    expect(labels.labels.loadMore).toEqual("Load More");
    expect(labels.franchisesYouPlayPageTitle).toEqual("SaC - Franchises You Play");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new FranchisesYouPlayPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key franchisesYouPlay.title is absent");
  });
});