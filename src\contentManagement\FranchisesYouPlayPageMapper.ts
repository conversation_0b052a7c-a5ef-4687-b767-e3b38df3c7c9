import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type FranchisesYouPlayPageLabels = {
  franchisesYouPlayPageLabels: {
    title: string;
    primaryFranchiseTitle: string;
    franchisesYouPlayPageTitle: string;
    primaryFranchiseSubTitle: string;
    secondaryFranchiseTitle: string;
    secondaryFranchiseSubTitle: string;
    description: string;
    messages: {
      primaryFranchise: string;
    };
    success: {
      updatedInformationHeader: string;
      franchiseUpdate: string;
    };
    labels: {
      primaryFranchise: string;
      loadMore: string;
    };
  };
};

export class FranchisesYouPlayPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): FranchisesYouPlayPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      franchisesYouPlayPageLabels: {
        title: microCopy.get("franchisesYouPlay.title"),
        franchisesYouPlayPageTitle: microCopy.get("franchisesYouPlay.franchisesYouPlayPageTitle"),
        primaryFranchiseTitle: microCopy.get("franchisesYouPlay.primaryFranchiseTitle"),
        primaryFranchiseSubTitle: microCopy.get("franchisesYouPlay.primaryFranchiseSubTitle"),
        secondaryFranchiseTitle: microCopy.get("franchisesYouPlay.secondaryFranchiseTitle"),
        secondaryFranchiseSubTitle: microCopy.get("franchisesYouPlay.secondaryFranchiseSubTitle"),
        description: microCopy.get("franchisesYouPlay.description"),
        messages: {
          primaryFranchise: microCopy.get("franchisesYouPlay.messages.primaryFranchise")
        },
        success: {
          updatedInformationHeader: microCopy.get("franchisesYouPlay.success.updatedInformationHeader"),
          franchiseUpdate: microCopy.get("franchisesYouPlay.success.franchiseUpdate")
        },
        labels: {
          primaryFranchise: microCopy.get("franchisesYouPlay.labels.primaryFranchise"),
          loadMore: microCopy.get("franchisesYouPlay.labels.loadMore")
        }
      }
    };
  }
}
