import {
  close,
  heart,
  home,
  Icon,
  image,
  live,
  logout,
  menu,
  union,
  user as userIcon
} from "@eait-playerexp-cn/core-ui-kit";
import NavLink from "./MobileNavLink";
import { useState } from "react";
import classNames from "classnames/bind";
import Link from "next/link";
import EAIcon from "../icons/EAIcon";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import { useDependency } from "@src/context/DependencyContext";

const NotificationsBell = dynamic(() => import("notifications/NotificationsBell"), {
  ssr: false,
  loading: () => <Loading />
});

const HeaderMobile = ({ labels, user, interestedCreator, notificationsLabels, analytics }) => {
  const router = useRouter();
  const {
    notificationsClient,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      PROGRAM_CODE,
      DEFAULT_NOTIFICATION_PROGRAM,
      FLAG_NEW_NAVIGATION_ENABLED
    }
  } = useDependency();
  const [open, setOpen] = useState(false);
  const [openAbout, setOpenAbout] = useState(false);
  const clickAbout = (event) => {
    event.stopPropagation();
    setOpenAbout(!openAbout);
  };
  const logoutApplication = () => {
    analytics.signedOutOfCreatorNetwork({ locale: router.locale });
    router.push("/api/logout");
  };
  const onProfileMenu = () => {
    analytics.visitedMyProfile({ locale: router.locale });
    router.push("/profile");
  };

  return (
    <>
      <div
        className={classNames(
          {
            "header-mobile-container-open": open
          },
          "header-mobile-container"
        )}
      >
        <div className="header-mobile-nav">
          <Link
            // Below method user?.status === "ACTIVE" should be removed. isActive() method should be added in Identity
            href={user?.status === "ACTIVE" ? "/dashboard" : "/"}
            className="icon-block"
            aria-label={labels.creatorNetworkHomepage}
          >
            <Icon icon={EAIcon} className="ea-icon-header-mobile" />
          </Link>
        </div>

        {user?.status === "ACTIVE" && (
          <NotificationsBell
            labels={notificationsLabels}
            locale={router.locale}
            configuration={{
              client: notificationsClient,
              programHosts: NOTIFICATION_BASE_URLS,
              program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
              defaultProgram: DEFAULT_NOTIFICATION_PROGRAM,
              enableNewNavigation: FLAG_NEW_NAVIGATION_ENABLED
            }}
          />
        )}

        <button className="header-mobile-nav" aria-label={labels.expand} onClick={() => setOpen(!open)}>
          <span className="icon-block">
            <Icon icon={open ? close : menu} className="menu-icon-header-mobile" color="#767676" />
          </span>
        </button>
      </div>
      {open && (
        <button className="menu-expand" aria-label={labels.expand} onClick={() => setOpen(!open)}>
          {user?.status === "ACTIVE" ? (
            <>
              <NavLink title={labels.dashboard} icon={home} href="/dashboard" />
              <NavLink title={labels.opportunities} icon={live} href="/opportunities" />
              <NavLink title={labels.myContent} icon={image} href="/my-content" />
              <div
                className={classNames(
                  {
                    "navlink-open": openAbout
                  },
                  "navlink"
                )}
                onClick={clickAbout}
              >
                <span className="icon-block navlink-icon-mobile">
                  <Icon icon={open ? union : close} className="navlink-icon-mobile" color={"#3028C7"} />
                </span>
                <span className="nav-mobile-link-label">{labels.about}</span>
              </div>
              {openAbout && (
                <div
                  className={classNames(
                    {
                      "navlink-open": openAbout,
                      navlink: !openAbout
                    },
                    "header-mobile-about-drop"
                  )}
                >
                  <Link href="/how-it-works" className="header-mobile-about-drop-nav-label">
                    {labels.how}
                  </Link>
                  <Link href="/opportunities-rewards" className="header-mobile-about-drop-nav-label">
                    {labels.perks}
                  </Link>
                  <Link href="/faq" className="header-mobile-about-drop-nav-label">
                    {labels.faqs}
                  </Link>
                  <Link href="/trust-and-safety-guidelines" className="header-mobile-about-drop-nav-label">
                    {labels.policy}
                  </Link>
                  <Link href="/disclosure" className="header-mobile-about-drop-nav-label">
                    {labels.disclosure}
                  </Link>
                </div>
              )}
              <div className="navlink" role="button" onClick={onProfileMenu}>
                <span className="icon-block navlink-icon-mobile">
                  <Icon icon={userIcon} className="navlink-icon-mobile" color={"#3028C7"} />
                </span>
                <span className="nav-mobile-link-label">{labels.myProfile}</span>
              </div>

              <div className="navlink" role="button" onClick={logoutApplication}>
                <span className="icon-block navlink-icon-mobile">
                  <Icon icon={logout} className="navlink-icon-mobile" color={"#3028C7"} />
                </span>
                <span className="nav-mobile-link-label">{labels.signout}</span>
              </div>
            </>
          ) : (
            <>
              <NavLink title={labels.home} icon={home} href="/" />
              <NavLink title={labels.works} icon={union} href="/how-it-works" />
              <NavLink title={labels.rewards} icon={heart} href="/opportunities-rewards" />
              <div
                className={classNames(
                  {
                    "navlink-open": openAbout
                  },
                  "navlink"
                )}
                onClick={clickAbout}
              >
                <span className="icon-block navlink-icon-mobile">
                  <Icon icon={union} className="navlink-icon-mobile" color={"#3028C7"} />
                </span>
                <span className="nav-mobile-link-label">{labels.about}</span>
              </div>
              {openAbout && (
                <div
                  className={classNames(
                    {
                      "navlink-open": openAbout,
                      navlink: !openAbout
                    },
                    "header-mobile-about-drop"
                  )}
                >
                  <Link href="/faq" className="header-mobile-about-drop-nav-label">
                    {labels.faqs}
                  </Link>
                  <Link href="/trust-and-safety-guidelines" className="header-mobile-about-drop-nav-label">
                    {labels.policy}
                  </Link>
                  <Link href="/disclosure" className="header-mobile-about-drop-nav-label">
                    {labels.disclosure}
                  </Link>
                </div>
              )}
              <NavLink title={interestedCreator ? labels.logIn : labels.signIn} icon={logout} href="/api/login" />
              {interestedCreator && (
                <NavLink title={labels.requestToJoin} icon={userIcon} href="/interested-creators/start" />
              )}
            </>
          )}
        </button>
      )}
    </>
  );
};
export default HeaderMobile;
