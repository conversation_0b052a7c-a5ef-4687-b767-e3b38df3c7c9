{"title": "<PERSON><PERSON> von deinem Gerät hochladen", "fileUpload": "<PERSON><PERSON> ho<PERSON>n", "chooseFile": "<PERSON><PERSON> w<PERSON>hlen", "noFileChoosen": "<PERSON><PERSON> ausgewählt", "acceptedFormats": "Zulässige Dateiformate", "maxFileSize": "Maximale Dateigröße", "maxLimitMessage": "Bitte wähle eine Datei aus, welche die maximale Dateigröße nicht überschreitet.", "invalidFileTypeMessage": "<PERSON>te wähle eine Datei mit zulässigen Erweiterungen aus.", "fileSelected": "Datei ausgewählt", "fileUploading": "Datei wird hochgeladen ...", "success": {"title": "Your file has been uploaded", "content": "Your file has been uploaded and is being processed. You will receive a notification when it is finished. You can leave this page or add further content while your file is processing."}, "error": {"title": "File Upload Failed", "content": "There was an error uploading your file. Please try again."}, "uploadFileProgress": "Upload file progress", "removeSelectedFile": "Remove selected file", "contentDescriptionRequired": "Content Description is required", "contentDescriptionLongMessage": "Content Description is too long", "contentDescription": "Description", "contentDescriptionPlaceholder": "Add a Description for your content", "maxcharacterLimit": "800 Characters"}