import { memo, useCallback, useMemo, useRef } from "react";
import { useTranslation } from "react-i18next";
import labelsBreadCrumb from "../../config/translations/breadcrumb";
import {
  <PERSON>ton,
  ModalBody,
  ModalCloseButton,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalTitle,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { useFormContext } from "react-hook-form";
import labelsCommon from "../../config/translations/common";

export default function MigrationModal({ setShowMigration, showMigration, onSave, onDiscard }) {
  const { t } = useTranslation(["common", "breadcrumb"]);
  const { breadcrumbLabels, layout } = useMemo(() => {
    return { layout: labelsCommon(t), breadcrumbLabels: labelsBreadCrumb(t) };
  }, [t]);
  const methods = useFormContext();
  const { formState } = methods;
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const handleCloseModal = useCallback(() => setShowMigration(!showMigration), [showMigration]);
  const cancelButtonRef = useRef(null);

  const FooterButtons = memo(function FooterButtons({
    buttons: { cancel, discard, save },
    onSave,
    onDiscard,
    cancelButtonRef
  }) {
    return (
      <>
        <Button variant="tertiary" size="sm" dark onClick={handleCloseModal} ref={cancelButtonRef}>
          {cancel}
        </Button>
        <Button
          variant="secondary"
          onClick={() => {
            handleCloseModal();
            onDiscard();
          }}
          dark
        >
          {discard}
        </Button>
        <Button disabled={Object.keys(formState.errors).length !== 0 || formState.isValid === false} onClick={onSave}>
          {save}
        </Button>
      </>
    );
  });

  return (
    formState.isDirty &&
    showMigration &&
    formModified && (
      <ModalV2 closeButtonRef={cancelButtonRef}>
        <ModalHeader>
          <ModalTitle>{breadcrumbLabels.modal.modalTitle}</ModalTitle>
          <ModalCloseButton ariaLabel={layout.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <p>{breadcrumbLabels.modal.modalMessage}</p>
        </ModalBody>
        <ModalFooter showDivider>
          <FooterButtons {...{ buttons: layout.buttons, onSave, onDiscard, cancelButtonRef }} />
        </ModalFooter>
      </ModalV2>
    )
  );
}
