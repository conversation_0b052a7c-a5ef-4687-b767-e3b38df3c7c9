import { useCallback, useEffect, useMemo, useState } from "react";
import CreatorsService, { CreatorWithCreatorCodeProfile } from "@src/api/services/CreatorsService";
import PaymentsService, {
  OpportunityType,
  PaymentsCriteria,
  PaymentsHistory,
  PaymentsHistoryWithCreatorCode,
  PaymentStatusType
} from "@src/api/services/PaymentsService";
import { LOADING } from "../../../utils";
import { DEFAULT_PAGE_SIZE } from "../../dashboard/Pagination";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";
import { CreatorProfile, CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export type PaymentTabs = "PAYEE_ONBOARDING" | "PAYMENT_HISTORY";

type CreatorPaymentsInformation = {
  id: string;
  businessName: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  email: string;
  zipCode: string;
  stateCode: string;
  city: string;
  countryCode: string;
  addressLine1: string;
  creatorCode: string;
};

export type PaymentsIFrameUrlPayload = CreatorPaymentsInformation & {
  language: string;
  urlType: string;
};

type UsePayableStatus = {
  paymentsIframe: { id?: null | string; embeddableUrl?: null | string };
  isPayable: boolean;
  paymentsHistory: PaymentsHistory;
  setPaymentsHistory: React.Dispatch<React.SetStateAction<PaymentsHistory>>;
  isCreatorCodeAssigned: boolean;
};

export const usePayableStatus = (
  activeTabId: PaymentTabs,
  locale: string,
  stableDispatch: ({ type, data }: { type: string; data: unknown }) => void,
  selectedCriteria: PaymentsCriteria,
  initializePagination: (transactionsCount: number) => void
): UsePayableStatus => {
  const {
    errorHandler,
    creatorsClient,
    configuration: { FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const [paymentsIframe, setPaymentsIframe] = useState<{ id?: null | string; embeddableUrl?: null | string }>({
    id: null,
    embeddableUrl: null
  });

  const [creatorPaymentsInformation, setCreatorPaymentsInformation] = useState<CreatorPaymentsInformation | null>(null);

  const [isPayable, setPayableStatus] = useState<boolean | null>(null);

  const [paymentsHistory, setPaymentsHistory] = useState<PaymentsHistory | PaymentsHistoryWithCreatorCode>(
    {} as PaymentsHistory | PaymentsHistoryWithCreatorCode
  );

  const fetchPaymentsIFrameUrl = async (payload: PaymentsIFrameUrlPayload) => {
    stableDispatch({ type: LOADING, data: true });
    const result = await PaymentsService.getPaymentsIFrameUrl(payload);
    setPaymentsIframe(result.data);
    stableDispatch({ type: LOADING, data: false });
  };

  const fetchCreator = useCallback(async () => {
    stableDispatch({ type: LOADING, data: true });
    // Creators BFF GET
    try {
      const creator = FLAG_PER_PROGRAM_PROFILE
        ? await creatorService.getCreator(PROGRAM_CODE)
        : (await CreatorsService.getCreatorWithCreatorCode()).data;
      let businessName = "";
      let city = "";
      let countryCode = "";
      let zipCode = "";
      let stateCode = "";
      let addressLine1 = "";
      const legalEntity: {
        businessName?: string;
        city?: string;
        country?: { value: string };
        zipCode?: string;
        stateCode?: string;
        street?: string;
      } = FLAG_PER_PROGRAM_PROFILE
        ? "legalInformation" in creator
          ? creator.legalInformation
          : {
              businessName: "",
              city: "",
              country: { value: "" },
              zipCode: "",
              stateCode: "",
              street: ""
            }
        : "legalEntity" in creator
        ? creator.legalEntity
        : {
            businessName: "",
            city: "",
            country: { value: "" },
            zipCode: "",
            stateCode: "",
            street: ""
          };
      businessName = legalEntity?.businessName || "";
      city = legalEntity.city || "";
      countryCode = legalEntity.country.value || "";
      zipCode = legalEntity.zipCode || "";
      stateCode = legalEntity.stateCode || "";
      addressLine1 = legalEntity.street || "";
      const id = "id" in creator ? creator.id : "";
      const {
        accountInformation: { firstName = "", lastName = "", dateOfBirth = "", originEmail: email = "" }
      } = creator;
      const isPayable =
        (creator as CreatorProfile).accountInformation?.payable ||
        (creator as CreatorWithCreatorCodeProfile).accountInformation?.isPayable ||
        false;
      const creatorCode =
        (creator as CreatorProfile).creatorCode?.code ||
        (creator as CreatorWithCreatorCodeProfile).accountInformation?.creatorCode ||
        "";
      const formattedDate: LocalizedDate = dateOfBirth as unknown as LocalizedDate;

      setCreatorPaymentsInformation({
        id,
        businessName,
        firstName,
        lastName,
        dateOfBirth: formattedDate.format("YYYY-MM-DD"),
        email,
        zipCode,
        stateCode,
        city,
        countryCode,
        addressLine1,
        creatorCode
      });

      setPayableStatus(isPayable);
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
  }, [stableDispatch]);

  useEffect(() => {
    setPaymentsIframe({ embeddableUrl: null, id: null });
    fetchCreator();
  }, [fetchCreator]);

  useEffect(() => {
    const language = locale.match(/\w+(?=\-\w+)/g)[0];

    if (activeTabId === "PAYMENT_HISTORY") {
      const { page, startDate, endDate, status = null, opportunityType = null } = selectedCriteria;

      const criteria: PaymentsCriteria = {
        page,
        size: DEFAULT_PAGE_SIZE,
        startDate: startDate as LocalizedDate,
        endDate: endDate as LocalizedDate
      };

      if (status) criteria["status"] = status as PaymentStatusType;
      if (opportunityType) criteria["opportunityType"] = opportunityType as OpportunityType;

      stableDispatch({ type: LOADING, data: true });

      if (creatorPaymentsInformation) {
        PaymentsService.getPaymentsHistoryWithCreatorCode(criteria)
          .then((response) => {
            stableDispatch({ type: LOADING, data: false });
            setPaymentsHistory(response.data);
            if (selectedCriteria.page === 1) {
              initializePagination(response.data.total);
            }
          })
          .catch((error) => {
            stableDispatch({ type: LOADING, data: false });
            errorHandler(stableDispatch, error);
          });
      }
    } else {
      /* Block for all 3 tabs for inactive CREATOR_WALLET and for Payment settings for active CREATOR_WALLET flag  */
      if (activeTabId && creatorPaymentsInformation) {
        fetchPaymentsIFrameUrl({
          ...creatorPaymentsInformation,
          language,
          urlType: activeTabId
        }).catch((error) => {
          stableDispatch({ type: LOADING, data: false });
          errorHandler(stableDispatch, error);
        });
      }
    }
  }, [activeTabId, creatorPaymentsInformation, selectedCriteria]);

  return {
    paymentsIframe,
    isPayable,
    paymentsHistory,
    setPaymentsHistory,
    isCreatorCodeAssigned: !!creatorPaymentsInformation?.creatorCode
  };
};
