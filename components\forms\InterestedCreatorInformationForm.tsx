import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Form from "../Form";
import Footer from "./Footer";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import {
  DateInput,
  Icon,
  Input,
  MultiSelect,
  outlineAdd,
  remove as removeIcon,
  Select
} from "@eait-playerexp-cn/core-ui-kit";
import {
  ConnectAccountLabels,
  Countries,
  Languages,
  Locale,
  Locales
} from "../pages/interested-creators/InterestedCreatorsInformationPage";
import { Fbpages, Information, Rules } from "../../pages/interested-creators/information";
import { isEmpty } from "../../src/utils/GenericUtils";
import { NextRouter } from "next/router";
import {
  COMPLETED_ONBOARDING_STEPS,
  GET_FB_PAGES,
  isAdult,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  SHOW_FACEBOOK_PAGES,
  useAsync,
  useIsMounted
} from "../../utils";
import SubmittedContentService from "../../src/api/services/SubmittedContentService";
import ConnectedAccountsService from "../../src/api/services/ConnectedAccountsService";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import NormalizedLocale from "../../src/locales/NormalizedLocale";
import { useAppContext } from "@src/context";
import { ConnectAccountsForm } from "../../pages/connect-accounts";
import ConnectedAccount from "../../src/channels/ConnectedAccount";
import cx from "classnames";
import { Layout } from "../pages/content-submission/ContentDeliverablesTab";
import ConnectFacebookPagesModal from "../pages/ConnectFacebookPagesModal";
import InterestedCreatorsService from "@src/api/services/InterestedCreatorsServices";
import { AxiosResponse } from "axios";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";

type ScanResult = {
  url: string;
  isSecure: boolean;
};

export type FormLabels = {
  infoTitle: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  preferredEmail: string;
  country: string;
  contentMediaTitle: string;
  contentMediaDescription: string;
  contentUrlPlaceholder: string;
  contentUrl: string;
  contentFollowers: string;
  contentFollowersPlaceholder: string;
  addAnother: string;
  contentLanguagesTitle: string;
  contentLanguagesDescription: string;
  contentLanguage: string;
  languageTitle: string;
  languageDescription: string;
  language: string;
  cancel: string;
  next: string;
  duplicateUrl: string;
  urlScanFailed: string;
  followersMaxLength: string;
  selectCountry: string;
  remove: string;
  ok: string;
  calendar: string;
  close: string;
  connectSocialMediaAccountTitle: string;
  connectSocialMediaAccountDescription: string;
  additionalContentAndWebsiteTitle: string;
  additionalContentAndWebsiteDescription: string;
  websiteUrlLabel: string;
  additionalLinkPlaceholder: string;
  addMoreUrlLabel: string;
  invalidUrl: string;
  ageMustBe18OrOlder: string;
};

export type InterestedCreatorInformationInputsProps = {
  formLabels: FormLabels;
  countries: Countries;
  languages: Languages;
  locales: Locales;
  interestedCreator?: Information;
  locale: string;
  rules: Rules;
  scanResults: ScanResult[] | null;
  onClose: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  defaultPreferredLanguage: Locale;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  layout: Layout;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  submitHandler: (accountToRemove: Record<string, unknown>) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  accounts: Array<ConnectedAccount>;
  invalidUrlErrors: string[];
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
} & Fbpages;

export type InterestedCreatorInformationFormProps = {
  formLabels: FormLabels;
  countries: Countries;
  languages: Languages;
  locales: Locales;
  interestedCreator?: Information;
  rules: Rules;
  router: NextRouter;
  locale: string;
  onClose?: MouseEventHandler<HTMLButtonElement>;
  stableDispatch: (action) => void;
  analytics: BrowserAnalytics;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  layout: Layout;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  accounts: Array<ConnectedAccount>;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
} & Fbpages;

const InterestedCreatorInformationInputs = memo(function InterestedCreatorInformationInputs({
  formLabels,
  countries = [],
  languages = [],
  interestedCreator,
  locales = [],
  locale,
  rules,
  scanResults,
  onClose,
  isPending,
  defaultPreferredLanguage,
  showAddConfirmation,
  setShowAddConfirmation,
  layout,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  submitHandler,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  accounts,
  pages,
  invalidUrlErrors,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: InterestedCreatorInformationInputsProps) {
  const { errorHandler } = useDependency();
  const methods = useFormContext();
  const {
    state: { showFacebookPages = false }
  } = useAppContext();
  const { control, reset, trigger, setError, watch, formState, clearErrors, setValue } = methods;
  const { fields, append, update, remove } = useFieldArray({
    control,
    name: "contentUrls"
  });
  const contentUrlChanged = watch("contentUrls");
  const [showPagesModal, setShowPagesModal] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  useEffect(() => {
    if (scanResults?.length) {
      scanResults.map(({ isSecure }, index) => {
        if (isSecure) {
          clearErrors(`contentUrls[${index}].url`);
        } else {
          setError(`contentUrls[${index}].url`, { type: "manual", message: formLabels.urlScanFailed });
        }
      });
    } else if (invalidUrlErrors.length) {
      invalidUrlErrors.map((value) => {
        const pos = value.substring(5, 6);
        setError(`contentUrls[${pos}].url`, { type: "manual", message: formLabels.invalidUrl });
      });
    }
  }, [scanResults, setError, invalidUrlErrors]);

  useEffect(() => {
    let timer;
    const urlFields = contentUrlChanged.map(({ url }) => url.trim());
    // this will trigger error on duplicate index
    urlFields.forEach((item, index) => {
      const duplicateFound = urlFields.indexOf(item) !== index;
      if (duplicateFound && item.trim() && item.trim() !== "https://") {
        if (formState?.errors?.contentUrls?.[index]?.url?.message !== formLabels.duplicateUrl) {
          timer = setTimeout(() => {
            setError(`contentUrls[${index}].url`, { type: "manual", message: formLabels.duplicateUrl });
          });
        }
      } else {
        if (formState?.errors?.contentUrls?.[index]?.url?.message === formLabels.duplicateUrl) {
          clearErrors(`contentUrls[${index}].url`);
          trigger("contentUrls");
        }
      }
    }, []);
    return () => clearTimeout(timer);
  }, [JSON.stringify(formState.errors), formLabels.duplicateUrl, JSON.stringify(contentUrlChanged)]);

  useEffect(() => {
    if (!isEmpty(interestedCreator)) {
      const {
        dateOfBirth = new Date().toISOString(),
        country = {},
        firstName,
        lastName,
        contentUrls = [{ url: "", followers: "" }],
        contentLanguages,
        preferredLanguage,
        originEmail
      } = interestedCreator;
      reset({
        dateOfBirth: LocalizedDate.fromFormattedDate(dateOfBirth).toDate(),
        country: !isEmpty(country) ? country : countries?.[0] || {},
        firstName,
        lastName,
        originEmail,
        contentUrls,
        contentLanguages,
        preferredLanguage
      });
    }
  }, [interestedCreator, countries, reset]);

  const clearInput = useCallback(
    (index, name) => () => {
      const defaultValue = name === "url" ? "https://" : "";
      update(index, { ...contentUrlChanged[index], [name]: defaultValue, saved: false });
      trigger(`contentUrls.${index}.${name}`);
    },
    [update, trigger, JSON.stringify(contentUrlChanged)]
  );
  const onDelete = useCallback((index) => () => remove(index), [remove]);
  const onAddUrls = useCallback(() => {
    append({ url: "", saved: false, followers: "" });
    trigger(`contentUrls`);
  }, [append, trigger]);
  const onSelectChange = useCallback(
    (field) => (item) => {
      if (item.value) field.onChange(item);
      else field.onChange("");
    },
    []
  );
  const {
    dispatch,
    state: { isLoading = false }
  } = useAppContext() || {};
  const isMounted = useIsMounted();
  const stableDispatch: (state, action?) => void = useCallback(dispatch, []);
  useEffect(
    function showAllFacebookPages() {
      if (pages?.length) {
        if (isMounted() && showFacebookPages) setShowPagesModal(true);
      } else {
        if (isMounted()) setShowPagesModal(false);
      }
    },
    [pages?.length, isMounted, showFacebookPages]
  );

  const onCloseFb = useCallback(async () => {
    try {
      setShowPagesModal(false);
      setSelectedPage(null);
      // Unset FBPages from session.
      await ConnectedAccountsService.clearFbPages();
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
    stableDispatch({ type: GET_FB_PAGES, data: true });
    stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
  }, [stableDispatch]);

  const onChange = ({ target: { id: pageId, value: pageAccessToken } }) => setSelectedPage({ pageId, pageAccessToken });

  const onConnectFbC = useCallback(async () => {
    if (selectedPage) {
      try {
        await ConnectedAccountsService.connectFbPages(selectedPage);
        setSelectedPage(null);
        setShowPagesModal(false);
        stableDispatch({ type: SHOW_FACEBOOK_PAGES, data: false });
      } catch (error) {
        errorHandler(stableDispatch, error);
      }
      await ConnectedAccountsService.clearAccountType();
      stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
    }
  }, [selectedPage, stableDispatch]);

  const { pending, execute: onConnectFb } = useAsync(onConnectFbC, false);

  const buttons = useMemo(() => ({ cancel: formLabels.cancel, next: formLabels.next }), [formLabels]);
  const hasAtleastOneAccountConnected = () => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      const isConnectedAccount = accounts.find((account) => account.isExpired == false);
      if (!isConnectedAccount) return false;
    }
    return accounts.length;
  };
  const disableSubmit =
    Object.keys(formState.errors).length !== 0 ||
    formState.isValid === false ||
    isPending ||
    !hasAtleastOneAccountConnected() ||
    false;

  const connectFaceBookModalLabels = {
    title: connectAccountLabels.modalConfirmationTitleFB,
    cancel: layout.buttons.cancel,
    connect: layout.buttons.connect,
    close: layout.buttons.close
  };

  return (
    <>
      <div className="mg-form-container interested-creator-information-container">
        <div className="information interested-creator-information">
          <h4 className="information-title">{formLabels.infoTitle}</h4>
        </div>
        <div className="information-form">
          <Controller
            control={control}
            name="firstName"
            rules={rules?.firstName}
            defaultValue={interestedCreator.firstName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.firstName}
                placeholder={formLabels.firstName}
                id="firstName"
              />
            )}
          />
          <Controller
            control={control}
            name="lastName"
            rules={rules?.lastName}
            defaultValue={interestedCreator.lastName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.lastName}
                placeholder={formLabels.lastName}
                id="lastName"
              />
            )}
          />
          <Controller
            control={control}
            name="dateOfBirth"
            rules={rules?.dateOfBirth}
            defaultValue={LocalizedDate.fromFormattedDate(interestedCreator.dateOfBirth).toDate()}
            render={({ field, fieldState: { error } }) => (
              <DateInput
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.dateOfBirth}
                placeholder={formLabels.dateOfBirth}
                locale={locale}
                maxDate={new Date()}
                title={formLabels.calendar}
                cancelText={formLabels.cancel}
                okText={formLabels.ok}
                onCancel={(date) => {
                  if (isAdult(date)) {
                    setError(
                      "dateOfBirth",
                      { type: "manual", message: formLabels.ageMustBe18OrOlder },
                      { shouldFocus: true }
                    );
                  } else {
                    setError("dateOfBirth", null);
                  }
                  setValue("dateOfBirth", date);
                }}
              />
            )}
          />
          <Controller
            control={control}
            name="preferredEmail"
            rules={rules?.preferredEmail}
            defaultValue={interestedCreator.originEmail}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="email"
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.preferredEmail}
                placeholder={formLabels.preferredEmail}
              />
            )}
          />
          <Controller
            control={control}
            name="country"
            rules={rules?.interestedCreatorCountry}
            render={({ field, fieldState: { error } }) => (
              <Select
                id="creator-country"
                selectedOption={interestedCreator?.country}
                errorMessage={error?.message}
                onChange={onSelectChange(field)}
                options={countries}
                label={formLabels.country}
                dark
              />
            )}
          />
        </div>
      </div>

      <>
        <div className="mg-communication-row content-in-center content-language">
          <h4 className="information-title content-language">{formLabels.contentLanguagesTitle}</h4>
          <div className="mg-communication-description content-language">{formLabels.contentLanguagesDescription}</div>
          <Controller
            control={control}
            name="contentLanguages"
            rules={rules?.contentLanguage}
            render={({ field, fieldState: { error } }) => (
              <MultiSelect
                {...field}
                selectedOptions={field.value}
                options={languages}
                errorMessage={error?.message || ""}
                label={formLabels.contentLanguage}
                placeholder={formLabels.contentLanguage}
              />
            )}
          />
        </div>

        <div className="mg-communication-row content-in-center language">
          <h4 className="information-title language">{formLabels.languageTitle}</h4>
          <div className="mg-communication-description language">{formLabels.languageDescription}</div>
          <Controller
            control={control}
            name="preferredLanguage"
            defaultValue={defaultPreferredLanguage}
            render={({ field, fieldState: { error } }) => (
              <Select
                id="creator-preferred-language"
                {...field}
                selectedOption={defaultPreferredLanguage}
                errorMessage={error && error.message}
                options={locales}
                label={formLabels.language}
                dark
              />
            )}
          />
        </div>

        <div className="mg-communication-row content-in-center add-account-container">
          <h4 className="information-title language" id="connect-accounts">
            {formLabels.connectSocialMediaAccountTitle}
          </h4>
          <div className="add-account-description">{formLabels.connectSocialMediaAccountDescription}</div>
          <div
            className={cx("content-in-center myprofile-view", { "add-account-card": accounts.length === 1 })}
            aria-labelledby="connect-accounts"
            data-testid="add-account-card"
          >
            <ConnectAccountsForm
              {...{
                layout,
                labels: connectAccountLabels,
                setShowAddConfirmation,
                accountToRemove,
                showAddConfirmation,
                setAccountToRemove,
                onClose,
                accounts,
                myProfileView: true,
                handleSubmit: submitHandler,
                isLoading,
                isPending,
                showRemoveAccountModal,
                setShowRemoveAccountModal,
                isInterestedCreator: true
              }}
            />
            {showPagesModal && (
              <ConnectFacebookPagesModal
                {...{
                  labels: connectFaceBookModalLabels,
                  onClose: onCloseFb,
                  onChange,
                  pages,
                  onConnect: onConnectFb,
                  pending,
                  selectedPage
                }}
              />
            )}
          </div>
        </div>
        <div className="mg-communication-row content-in-center interested-creator-information-additional-content-container">
          <h4
            className="interested-creator-information-additional-content-title"
            role="heading"
            id="interested-creator-information-additional-links"
          >
            {formLabels.additionalContentAndWebsiteTitle}
          </h4>
          <p className="interested-creator-information-additional-content-description">
            {formLabels.additionalContentAndWebsiteDescription}
          </p>
          <ul
            className="interested-creator-information-additional-content-urls"
            aria-labelledby="interested-creator-information-additional-links"
          >
            {fields.map((item, index) => {
              return (
                <li
                  key={item.id}
                  className={cx("interested-creator-information-additional-content-url", {
                    "interested-creator-information-additional-content-url-without-delete": index === 0
                  })}
                >
                  <Controller
                    control={control}
                    name={`contentUrls.${index}.url`}
                    key={`contentUrls.${index}.url`}
                    rules={rules?.url}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        dark
                        errorMessage={error?.message || ""}
                        id={`contentUrls.${index}.url`}
                        {...field}
                        placeholder={formLabels.additionalLinkPlaceholder}
                        label={formLabels.websiteUrlLabel}
                        clearContent
                        onClearContent={clearInput(index, "url")}
                        type="url"
                      />
                    )}
                  />
                  {!!index && (
                    <button
                      type="button"
                      aria-label={formLabels.remove}
                      className="interested-creator-information-additional-content-delete"
                      onClick={onDelete(index)}
                    >
                      <Icon icon={removeIcon} className="interested-creator-content-url-icon" />
                    </button>
                  )}
                </li>
              );
            })}
          </ul>
          {fields.length < 10 && (
            <button
              type="button"
              className="interested-creator-information-additional-content-add-more"
              onClick={onAddUrls}
            >
              <Icon icon={outlineAdd} className="interested-creator-content-url-add-more-icon" />
              <p className="interested-creator-content-url-add-more-text">{formLabels.addMoreUrlLabel}</p>
            </button>
          )}
        </div>
      </>

      <Footer {...{ buttons, onCancel: onClose, disableSubmit, isPending }} />
    </>
  );
});

export default memo(function InterestedCreatorInformationForm({
  formLabels,
  languages,
  locales,
  countries,
  interestedCreator,
  rules,
  router,
  locale,
  stableDispatch,
  onClose,
  analytics,
  showAddConfirmation,
  setShowAddConfirmation,
  layout,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  accounts,
  pages,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: InterestedCreatorInformationFormProps) {
  const { errorHandler } = useDependency();
  const [scanResults, setScanResults] = useState(null);
  const [invalidUrlErrors, setInvalidUrlErrors] = useState([]);
  const {
    state: { onboardingSteps }
  } = useAppContext();
  const defaultValues = useMemo(() => {
    const {
      dateOfBirth = new Date().toISOString(),
      country = {},
      firstName,
      lastName,
      contentUrls = [{ url: "", followers: "" }],
      contentLanguages,
      preferredLanguage = {
        code: "",
        name: ""
      },
      originEmail
    } = interestedCreator;

    return {
      dateOfBirth: LocalizedDate.fromFormattedDate(dateOfBirth).toDate(),
      country: !isEmpty(country) ? country : countries?.[0] || {},
      firstName,
      lastName,
      contentUrls,
      contentLanguages,
      preferredLanguage,
      originEmail
    };
  }, [interestedCreator, countries]);

  /* To get preferred locale */
  const getPreferredLocale = useCallback(
    (preferredLanguage): Locale => {
      const localeLanguageCode = NormalizedLocale.fromSlug(router.locale).toString();
      return locales.find(({ value }) => {
        return (
          value ===
          (preferredLanguage && preferredLanguage?.code && preferredLanguage?.code !== ""
            ? preferredLanguage?.code
            : localeLanguageCode)
        );
      });
    },
    [router, locales]
  );
  const defaultPreferredLanguage: Locale = getPreferredLocale(defaultValues.preferredLanguage);

  const handleSaveInformationForm = async (data, currentStep) => {
    const { preferredEmail, preferredLanguage } = data;
    const payload = {
      ...data,
      nucleusId: interestedCreator.nucleusId,
      defaultGamerTag: interestedCreator.defaultGamerTag,
      originEmail: interestedCreator.originEmail,
      preferredEmail: preferredEmail,
      dateOfBirth: LocalizedDate.fromFormattedDate(data.dateOfBirth).format("YYYY-MM-DD")
    };

    payload.preferredLanguage = { code: preferredLanguage?.value, name: preferredLanguage?.label };
    try {
      await InterestedCreatorsService.saveApplication(payload);
    } catch (e) {
      errorHandler(stableDispatch, e);
      return;
    }
    analytics.continuedCreatorApplication({ locale: router.locale, page: location.pathname, finalStep: false });
    stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
    router.push("/interested-creators/creator-types");
  };

  const handleSubmitWithAddAccountEnabled = async (contentUrls, data, currentStep) => {
    const urls = [];
    // Convert form values to an array of URLs
    contentUrls.forEach(({ url }) => url && url !== "https://" && urls.push(url));
    if (urls.length > 0) {
      try {
        const response = (await SubmittedContentService.validateContent(
          { urls },
          "INTERESTED_CREATORS"
        )) as AxiosResponse;
        const results = response.data.results;
        const scanFailed = !!results.find((result) => !result.isSecure);
        if (scanFailed) {
          setScanResults(results);
          return;
        }
        await handleSaveInformationForm(data, currentStep);
      } catch (e) {
        setScanResults(null);
        if (e.response.status === 422 && e.response.data.code === "validate-content-urls-invalid-input") {
          setInvalidUrlErrors(Object.keys(e.response.data.errors));
          return;
        }
        errorHandler(stableDispatch, e);
      }
    } else {
      await handleSaveInformationForm(data, currentStep);
    }
  };

  const onSubmit = useCallback(
    async (data) => {
      const { contentUrls = [] } = data;
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      await handleSubmitWithAddAccountEnabled(contentUrls, data, currentStep);
    },
    [
      interestedCreator.nucleusId,
      interestedCreator.defaultGamerTag,
      locale,
      router,
      stableDispatch,
      defaultPreferredLanguage,
      onboardingSteps
    ]
  );
  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);

  return (
    <Form mode="onChange" onSubmit={submitHandler} defaultValues={defaultValues}>
      <InterestedCreatorInformationInputs
        {...{
          formLabels,
          countries,
          languages,
          locales,
          locale,
          rules,
          interestedCreator,
          scanResults,
          onClose,
          isPending,
          defaultPreferredLanguage,
          showAddConfirmation,
          setShowAddConfirmation,
          layout,
          connectAccountLabels,
          accountToRemove,
          setAccountToRemove,
          submitHandler,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          accounts,
          pages,
          invalidUrlErrors,
          INTERESTED_CREATOR_REAPPLY_PERIOD
        }}
      />
    </Form>
  );
});
