import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsLoginError from "../config/translations/login-error";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import {
  Button,
  Input,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalTitle,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import HeaderMobile from "../components/header/HeaderMobile";
import { isValidEmail } from "../src/utils/GenericUtils";
import NotificationsService from "../src/api/services/NotificationsService";
import Head from "next/head";
import Link from "next/link";
import flags from "../utils/feature-flags";
import { sessionFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "../src/ApiContainer";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import { createRouter } from "next-connect";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import loginErrorProps from "../src/serverprops/LoginErrorProps";

const FooterButtons = memo(function FooterButtons({ buttons: { notifyMe, cancel }, onNo, onYes, cancelButtonRef }) {
  return (
    <>
      <Button variant="tertiary" size="md" onClick={onNo} ref={cancelButtonRef}>
        {cancel}
      </Button>
      <Button onClick={onYes}>{notifyMe}</Button>
    </>
  );
});

const ModalChildren = memo(function ModalChildren({
  originEmail,
  description,
  errorMessage,
  emailLabel,
  onChange,
  email
}) {
  return (
    <>
      <p>{description}</p>
      <div className="login-error-modal-confirmation-body-input">
        <Input
          errorMessage={errorMessage || ""}
          label={emailLabel}
          placeholder={emailLabel}
          onChange={onChange}
          value={email || originEmail}
        />
      </div>
    </>
  );
});

export default function LoginError({ futureCreator }) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [notifySuccess, setNotifySuccess] = useState(false);
  const [email, setEmail] = useState(futureCreator.email);
  const [emailErrorMessage, setEmailErrorMessage] = useState("");
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);
  const onChangeEmail = (e) => {
    setEmail(e.target.value);
    let email = e.target.value;
    if (!isValidEmail(email)) {
      setEmailErrorMessage("Please enter a valid email.");
    } else {
      setEmailErrorMessage("");
    }
  };
  const handleNotifyMe = async () => {
    if (email !== "" && emailErrorMessage === "") {
      await NotificationsService.notifyMe({
        email: email,
        nucleusId: futureCreator.nucleusId
      });
      setNotifySuccess(true);
      handleModalClose();
    }
  };
  const { t } = useTranslation(["common", "login-error"]);
  const { layout, loginErrorLabels } = useMemo(() => {
    return {
      layout: labelsCommon(t),
      loginErrorLabels: labelsLoginError(t)
    };
  }, [t]);

  const cancelButtonRef = useRef(null);

  return (
    <>
      <Head>
        <title>{loginErrorLabels.pageTitle}</title>
        <link rel="icon" href="/favicon_v2.0.ico" />
      </Head>
      <div className="header-mobile">
        <HeaderMobile labels={{ ...layout.header, ...layout.buttons }} />
      </div>
      <div className="login-mg-container">
        <div className="login-error-container">
          {notifySuccess ? (
            <img src="/img/icons/tick-mark-icon.png" alt="Error" className="login-error-image" />
          ) : (
            <img src="/img/icons/error-icon.png" alt="Error" className="login-error-image" />
          )}
          <div className="login-error-title">{notifySuccess ? loginErrorLabels.title2 : loginErrorLabels.title}</div>
          <div className="login-error-sub-title">
            {notifySuccess
              ? loginErrorLabels.subTitle2
              : t("login-error:subTitle", { originEmail: futureCreator.email ? futureCreator.email : "" })}
          </div>
          {!notifySuccess && (
            <>
              <div className="login-error-body">
                {loginErrorLabels.body1} {loginErrorLabels.body3}{" "}
                <span className="login-error-click-me" onClick={() => setShowConfirmation(true)}>
                  {loginErrorLabels.clickHere}
                </span>{" "}
                {loginErrorLabels.body4}
              </div>
              <div className="login-error-body">{loginErrorLabels.body2}</div>
            </>
          )}

          <div className="login-button-logout">
            <Link href="/api/logout" className="btn btn-primary btn-md">
              {layout.buttons.logout}
            </Link>
          </div>
        </div>
        {showConfirmation && (
          <ModalV2 closeButtonRef={cancelButtonRef}>
            <ModalHeader>
              <ModalTitle>{loginErrorLabels.modalHeader}</ModalTitle>
              <ModalCloseButton ariaLabel={layout.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
            </ModalHeader>
            <ModalBody>
              <ModalChildren
                originEmail={futureCreator.email ? futureCreator.email : ""}
                description={loginErrorLabels.modalBody}
                emailLabel={layout.labels.emailAddress}
                onChange={onChangeEmail}
                email={email}
                errorMessage={emailErrorMessage}
              />
            </ModalBody>
            <ModalFooter showDivider>
              <FooterButtons
                {...{ buttons: layout.buttons, onNo: handleModalClose, onYes: handleNotifyMe, cancelButtonRef }}
              />
            </ModalFooter>
          </ModalV2>
        )}
      </div>
    </>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(loginErrorProps(locale));

    return await router.run(req, res);
  }

  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());

  if (flags.isInterestedCreatorFlowEnabled()) {
    return { notFound: true };
  }

  const futureCreator = req.session.notify || null;

  if (!futureCreator) {
    res.setHeader("Location", encodeURI(`${locale}/404`));
    res.statusCode = 302;
    res.end();
  }

  delete req.session.notify;
  await req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      futureCreator,
      ...(await serverSideTranslations(locale, ["common", "login-error"]))
    }
  };
};
