import { ApplicationRejectedPageMapper } from "@src/contentManagement/ApplicationRejectedPageMapper";

describe("ApplicationRejectedPageMapper", () => {
  const microCopies = {
    "applicationRejected.email": "Email",
    "applicationRejected.reApplyDescription": "Re-apply Description",
    "applicationRejected.closed": "Closed",
    "applicationRejected.descriptionPara4": "Description Paragraph 4",
    "applicationRejected.descriptionPara1": "Description Paragraph 1",
    "applicationRejected.submissionDate": "Submission Date",
    "applicationRejected.descriptionPara3": "Description Paragraph 3",
    "applicationRejected.descriptionPara2": "Description Paragraph 2",
    "applicationRejected.submissionReviewedDescription": "Submission Reviewed Description",
    "applicationRejected.gamerTag": "Gamer Tag",
    "applicationRejected.status": "Status",
    "applicationRejected.title": "Title",
    "applicationRejected.reviewAndResubmit": "Review and Resubmit",
    "applicationRejected.returnToCreatorNetwork": "Return to Creator Network",
    "applicationRejected.submissionReviewed": "Submission Reviewed",
    "applicationRejected.reApplyTitle": "Re-apply Title",
    "applicationRejected.programLabel": "Program",
    "applicationRejected.programName": "Support a Creator",
    "applicationRejected.backHome": "Back Home"
  };

  it("maps application rejected page labels", () => {
    const mapper = new ApplicationRejectedPageMapper();
    const labels = mapper.map(microCopies).applicationRejectedLabels;

    expect(labels.email).toEqual("Email");
    expect(labels.reApplyDescription).toEqual("Re-apply Description");
    expect(labels.closed).toEqual("Closed");
    expect(labels.descriptionPara4).toEqual("Description Paragraph 4");
    expect(labels.descriptionPara1).toEqual("Description Paragraph 1");
    expect(labels.submissionDate).toEqual("Submission Date");
    expect(labels.descriptionPara3).toEqual("Description Paragraph 3");
    expect(labels.descriptionPara2).toEqual("Description Paragraph 2");
    expect(labels.submissionReviewedDescription).toEqual("Submission Reviewed Description");
    expect(labels.gamerTag).toEqual("Gamer Tag");
    expect(labels.status).toEqual("Status");
    expect(labels.title).toEqual("Title");
    expect(labels.reviewAndResubmit).toEqual("Review and Resubmit");
    expect(labels.returnToCreatorNetwork).toEqual("Return to Creator Network");
    expect(labels.submissionReviewed).toEqual("Submission Reviewed");
    expect(labels.reApplyTitle).toEqual("Re-apply Title");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ApplicationRejectedPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key applicationRejected.email is absent");
  });
});