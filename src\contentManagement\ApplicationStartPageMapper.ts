import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ApplicationStartPageLabels = {
  applicationStartPageLabels: {
    subTitle: string;
    howPlatformWork: string;
    appliedDesc: string;
    perks: string;
    alreadyApplied: string;
    description: string;
    buttons: {
      applyNow: string;
    };
    alreadyAppliedSuffix: string;
    explore: string;
    offer: string;
    title: string;
    appliedDescSuffix: string;
    descriptionSuffix: string;
    exploreLeftTitle: string;
    exploreRightTitle: string;
  };
};

export class ApplicationStartPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ApplicationStartPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      applicationStartPageLabels: {
        subTitle: microCopy.get("applicationStart.subTitle"),
        howPlatformWork: microCopy.get("applicationStart.howPlatformWork"),
        appliedDesc: microCopy.get("applicationStart.appliedDesc"),
        perks: microCopy.get("applicationStart.perks"),
        alreadyApplied: microCopy.get("applicationStart.alreadyApplied"),
        description: microCopy.get("applicationStart.description"),
        buttons: {
          applyNow: microCopy.get("applicationStart.buttons.applyNow")
        },
        alreadyAppliedSuffix: microCopy.get("applicationStart.alreadyAppliedSuffix"),
        explore: microCopy.get("applicationStart.explore"),
        offer: microCopy.get("applicationStart.offer"),
        title: microCopy.get("applicationStart.title"),
        appliedDescSuffix: microCopy.get("applicationStart.appliedDescSuffix"),
        descriptionSuffix: microCopy.get("applicationStart.descriptionSuffix"),
        exploreLeftTitle: microCopy.get("applicationStart.exploreLeftTitle"),
        exploreRightTitle: microCopy.get("applicationStart.exploreRightTitle"),
      }
    };
  }
}
