import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useEffect, useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsAccepted from "../../config/translations/accepted";
import labelsCommon from "../../config/translations/common";
import flags from "../../utils/feature-flags";
import { ApplicationAcceptedPage } from "@components/pages/interested-creators/ApplicationAcceptedPage";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationAcceptedProps from "@src/serverprops/ApplicationAcceptedProps";
import { useDependency } from "@src/context/DependencyContext";

export type ApplicationAcceptedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  locale: string;
  showInitialMessage?: boolean;
};

export default function Accepted({ locale }: ApplicationAcceptedProps): JSX.Element {
  const { analytics } = useDependency();
  const { t } = useTranslation(["common", "accepted"]);
  const { applicationAcceptedLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);

    return {
      applicationAcceptedLabels: { creatorNetwork, close, ...labelsAccepted(t) }
    };
  }, [t]);

  const { creatorNetwork, close, pageTitle } = applicationAcceptedLabels;

  useEffect(() => {
    document.title = pageTitle;
  }, []);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <ApplicationAcceptedPage
            applicationAcceptedLabels={applicationAcceptedLabels}
            locale={locale}
            analytics={analytics}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationAcceptedProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationAcceptedProps>;
  }

  const application = await withCreatorApplication(req, res);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isAccepted()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      ...(await serverSideTranslations(locale, ["common", "accepted"])),
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
