import { FC, memo } from "react";
import ToolTip from "../../../../ToolTip";
import { mapOpportunityTypeForAPI, Transaction, TransactionWithCreatorCode } from "@src/api/services/PaymentsService";
import { dollarStar, Icon } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames";

export type CreatorCodeTooltipLabelProps = {
  typeCreatorCode: string;
  typeOpportunity: string;
  simsMakerProgram: string;
};

export type CreatorCodeTooltipProps = {
  transaction: Transaction | TransactionWithCreatorCode;
  labels: CreatorCodeTooltipLabelProps;
};

const CreatorCodeTooltip: FC<CreatorCodeTooltipProps> = function ({ transaction, labels }) {
  const { opportunityType } = transaction;
  const tooltipContent =
    opportunityType === "support_a_creator" ? (
      <div className="transaction-grid-creator-code">
        <div>{(transaction as unknown as TransactionWithCreatorCode).code()}</div>
        <div> {(transaction as unknown as TransactionWithCreatorCode).activationPeriod()}</div>
      </div>
    ) : (
      ""
    );

  return opportunityType === "support_a_creator" ? (
    <div className={classNames("transaction-grid-icontext-wrapper")}>
      <ToolTip
        overlay={tooltipContent}
        directionForMobile="top"
        classes="creator-code-tooltip"
        id="support-a-creator-code-tooltip"
      >
        <Icon
          icon={dollarStar}
          id="creator-perk"
          className="transaction-grid-icontext-icon creator-perk"
          width="16px"
          height="15px"
        />
      </ToolTip>
      <span className="transaction-grid-icontext-text">{labels.typeCreatorCode}</span>
    </div>
  ) : opportunityType === mapOpportunityTypeForAPI("sims_ugx_opportunity") ? (
    <>{labels.simsMakerProgram}</>
  ) : (
    <>{labels.typeOpportunity}</>
  );
};

export default memo(CreatorCodeTooltip);
