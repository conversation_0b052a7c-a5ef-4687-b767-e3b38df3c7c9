import "reflect-metadata";
import React, { FC, useCallback, useMemo, useState } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import MigrationLayout from "../../components/MigrationLayout";
import { useTranslation } from "react-i18next";
import labels<PERSON><PERSON><PERSON> from "../../config/translations/common";
import labelsBreadCrumb from "../../config/translations/breadcrumb";
import labelsFranchisesYouPlay from "../../config/translations/franchises-you-play";
import InterestedCreatorsFranchisesYouPlayPage from "../../components/pages/interested-creators/InterestedCreatorsFranchisesYouPlayPage";
import { useAppContext } from "@src/context";
import { useRouter } from "next/router";
import { useToast } from "../../components/toast";
import Error from "../_error";
import flags from "../../utils/feature-flags";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import { AuthenticatedUser, AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator as InterestedCreatorWithFranchises } from "../../src/api/services/InterestedCreatorsServices";
import { interestedCreatorPages } from "./information";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorFranchisesYouPlayProps from "@src/serverprops/InterestedCreatorFranchisesYouPlayProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import { useDependency } from "@src/context/DependencyContext";

export type LayoutButtons = {
  yes: string;
  no: string;
  cancel: string;
  next: string;
  submit: string;
  close: string;
};
export type FranchiseMessages = {
  primaryFranchise: string;
};
export type FranchiseLabels = {
  primaryFranchise: string;
  loadMore: string;
};
export type FranchisesYouPlayLabels = {
  title: string;
  description: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  buttons: LayoutButtons;
};
export type FranchisesYouPlayFormLabels = {
  buttons: LayoutButtons;
  messages: FranchiseMessages;
  labels: FranchiseLabels;
  primaryFranchiseTitle: string;
  primaryFranchiseSubTitle: string;
  secondaryFranchiseTitle: string;
  secondaryFranchiseSubTitle: string;
};
export type InterestedCreatorFranchisesYouPlayProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InterestedCreatorWithFranchises;
  user: AuthenticatedUser;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
};

const InterestedCreatorFranchisesYouPlay: FC<InterestedCreatorFranchisesYouPlayProps> = ({
  interestedCreator,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}) => {
  const { analytics } = useDependency();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { t } = useTranslation(["common", "breadcrumb", "franchises-you-play"]);
  const { layout, franchisesYouPlay } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t)
      },
      franchisesYouPlay: labelsFranchisesYouPlay(t)
    };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const franchisesYouPlayLabels: FranchisesYouPlayLabels = {
    ...franchisesYouPlay,
    buttons: {
      yes: layout.buttons.yes,
      no: layout.buttons.no,
      cancel: layout.buttons.cancel,
      next: layout.buttons.next,
      submit: layout.buttons.submit,
      close: layout.buttons.close
    }
  };
  const layoutButton = {
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    cancel: layout.buttons.cancel,
    next: layout.buttons.next,
    submit: layout.buttons.submit,
    close: layout.buttons.close
  };
  const {
    messages,
    labels,
    secondaryFranchiseTitle,
    secondaryFranchiseSubTitle,
    primaryFranchiseTitle,
    primaryFranchiseSubTitle
  } = franchisesYouPlay;
  const franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels = {
    buttons: layoutButton,
    messages,
    labels,
    secondaryFranchiseTitle,
    secondaryFranchiseSubTitle,
    primaryFranchiseTitle,
    primaryFranchiseSubTitle
  };

  const {
    dispatch,
    state: { exceptionCode = null, sessionUser = null, isValidationError = false, isError = false } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const { locale } = useRouter();
  const router = useRouter();
  const { error: errorToast } = useToast();

  const onGoBack = () => router.push("/interested-creators/creator-types");

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={franchisesYouPlayLabels.title}
      className="interested-creator"
      {...{ ...layout, onClose }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      onGoBack={onGoBack}
    >
      <InterestedCreatorsFranchisesYouPlayPage
        {...{
          interestedCreator,
          franchisesYouPlayLabels,
          franchisesYouPlayFormLabels,
          stableDispatch,
          showConfirmation,
          setShowConfirmation,
          onClose,
          errorToast,
          isValidationError,
          isError,
          unhandledError,
          router,
          locale,
          analytics,
          INTERESTED_CREATOR_REAPPLY_PERIOD
        }}
      />
    </MigrationLayout>
  );
};

export default InterestedCreatorFranchisesYouPlay;

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorFranchisesYouPlayProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorFranchisesYouPlayProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.franchises);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      ...(await serverSideTranslations(locale, ["common", "breadcrumb", "franchises-you-play"])),
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
