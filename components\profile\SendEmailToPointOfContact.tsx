import React, { FC, ReactNode, useCallback, useState } from "react";
import { email, Icon } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { CreatorProps, PocLabels } from "./PointOfContactDetails";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import PointOfContactEmailModal from "./PointOfContactEmailModal";

type SendEmailToPointOfContactProps = {
  pocLabels: PocLabels;
  pocName: string;
  title: string;
  analytics: BrowserAnalytics;
  buttons: { close: string };
  children: ReactNode;
  creator: CreatorProps;
};

export const SendEmailToPointOfContact: FC<SendEmailToPointOfContactProps> = ({
  pocLabels,
  children,
  pocName = "",
  analytics,
  buttons,
  creator
}) => {
  const [isShown, setIsShown] = useState(false);
  const router = useRouter();
  const showModal = useCallback(() => {
    analytics.clickedEmailPocLink({ locale: router.locale });
    setIsShown(true);
  }, []);
  const closeModal = useCallback(() => setIsShown(false), []);

  return (
    <div className="poc-container">
      <button type="button" className="poc-email-placeholder" onClick={showModal}>
        <Icon icon={email} />
        <span className="icon-label">{children}</span>
      </button>

      {isShown && (
        <PointOfContactEmailModal
          {...{
            pocLabels,
            pocName,
            analytics,
            buttons,
            closeModal,
            creator
          }}
        />
      )}
    </div>
  );
};

export default SendEmailToPointOfContact;
