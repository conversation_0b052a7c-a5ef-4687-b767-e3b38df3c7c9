import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { ApplicationAcceptedPage } from "../components/pages/interested-creators/ApplicationAcceptedPage";
import BrowserAnalytics from "../src/analytics/BrowserAnalytics";

/**
 * Page shown to creators who have been accepted into the program
 */
const meta: Meta<typeof ApplicationAcceptedPage> = {
  title: "Creator Network/Pages/Interested Creators/Application Accepted Page",
  component: ApplicationAcceptedPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationAcceptedPage>;

/**
 * Default copy shown to creators who have been accepted into the program.
 */
export const Page: Story = {
  args: {
    applicationAcceptedLabels: {
      title: "You’re in!",
      descriptionPara1:
        "Congratulations you have been accepted into our EA Creator Network program! You’ve demonstrated your ability to create engaging content.",
      descriptionPara2: "All you have to do now is complete your EA Creator Network profile.",
      descriptionPara3:
        "You will also be required to connect at least one social media account (Facebook, Instagram, Twitch or YouTube).",
      returnToCreatorNetwork: "Complete your profile →"
    },
    locale: "en-us",
    analytics: { checkedApplicationStatus: () => {} } as unknown as BrowserAnalytics
  }
};
