import React, { memo, useMemo } from "react";
import Link from "next/link";
import { Icon } from "@eait-playerexp-cn/core-ui-kit";
import { useDetectScreen } from "../utils";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsSignUp from "../config/translations/signup-complete";
import labelsCommon from "../config/translations/common";
import Head from "next/head";
import EaLogo from "../components/icons/EaLogo";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import featureFlags from "utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import signupCompleteProps from "@src/serverprops/SignupCompleteProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

type HeaderProps = {
  isMobile: boolean;
  creatorNetwork: string;
};
type DetailsProps = {
  isMobile?: boolean;
  titleMobile: string;
  titleWeb: string;
  subTitle: string;
  info: string;
};

export type SignupCompleteProps = {
  runtimeConfiguration?: Record<string, unknown>;
};

const Header = memo(function Header({ isMobile, creatorNetwork }: HeaderProps) {
  return (
    <div className="signup-header-logo">
      <Link href="/">
        <span className="icon-block">
          <Icon icon={EaLogo} />
        </span>
        {!isMobile && creatorNetwork}
      </Link>
    </div>
  );
});

const Details = memo(function Details({ isMobile = false, titleMobile, titleWeb, subTitle, info }: DetailsProps) {
  return (
    <>
      <h2>{`${(isMobile && titleMobile) || titleWeb}`}</h2>
      <h5>{subTitle}</h5>
      <p>{info}</p>
    </>
  );
});

export default memo(function SignupComplete() {
  const isMobile = useDetectScreen(767);
  const { t } = useTranslation(["common", "signup-complete"]);
  const {
    exploreButton,
    header: { creatorNetwork },
    ...labels
  } = useMemo(() => {
    return { ...labelsSignUp(t), ...labelsCommon(t) };
  }, [t]);

  return (
    <>
      <Head>
        <title>{labels.titleWeb || creatorNetwork}</title>
      </Head>
      <div className="signup-container">
        <div className="signup-page">
          <Header isMobile={isMobile} creatorNetwork={creatorNetwork} />
          <section>
            {isMobile && <Details isMobile={isMobile} {...labels} />}
            <img src="/img/signup/characters-signup.png" height="386" width="497" />
            {!isMobile && <Details {...labels} />}
          </section>
          <Link href="/dashboard" className="btn btn-primary btn-md">
            {exploreButton}
          </Link>
        </div>
      </div>
    </>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(addLocaleCookie(locale))
      .get(signupCompleteProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<SignupCompleteProps>;
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      ...(await serverSideTranslations(locale, ["common", "signup-complete"]))
    }
  };
};
