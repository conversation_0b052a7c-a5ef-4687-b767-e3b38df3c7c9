import { render, screen } from "@testing-library/react";
import { RequestsToJoinStatusTable } from "../../../../components/pages/interested-creators/RequestsToJoinStatusTable";
import { axe } from "jest-axe";
import Rejected from "../../../../components/icons/ContentCard/Rejected";
import { commonTranslations } from "../../../translations";

describe("RequestsToJoinStatusTable", () => {
  const requestsToJoinStatusTableProps = {
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    submittedDate: "Jan 02, 2024",
    emailId: "<EMAIL>",
    defaultGamerTag: "Test",
    applicationStatus: "Closed",
    statusIcon: Rejected,
    requestsToJoinStatusTableLabels: commonTranslations.applicantLabels
  };

  it("shows application status table", async () => {
    render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} />);

    expect(
      screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.gamerTag)
    ).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.email)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.status)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.defaultGamerTag)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.emailId)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.applicationStatus)).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows application status table", async () => {
      render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} INTERESTED_CREATOR_REAPPLY_PERIOD />);

      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.gamerTag)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.email)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.status)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.submissionDate)
      ).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.defaultGamerTag)).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.emailId)).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.applicationStatus)).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.submittedDate)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    const { container } = render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
