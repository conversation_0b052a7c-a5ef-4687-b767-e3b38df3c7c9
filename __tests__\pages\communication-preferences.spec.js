import "reflect-metadata";
import { act, screen, waitFor, within } from "@testing-library/react";
import CommunicationPreferences from "./../../pages/communication-preferences";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import userEvent from "@testing-library/user-event";
import { aCommunicationPreferences } from "../factories/creators/CommunicationPreferences";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { useAppContext } from "../../src/context";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import { onToastClose } from "../../utils";
import CreatorsService from "../../src/api/services/CreatorsService";
import ConnectedAccountsService from "../../src/api/services/ConnectedAccountsService";
import { useDependency } from "../../src/context/DependencyContext";
import { aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { axe } from "jest-axe";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../src/api/services/ConnectedAccountsService");
jest.mock("../../src/api/services/CreatorsService");
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  useIsMounted: jest.fn().mockImplementation(() => jest.fn().mockReturnValue(true)),
  onToastClose: jest.fn()
}));
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));

describe("CommunicationPreferences", () => {
  const communicationPreferencesProps = {
    user: aUser({ status: "ACTIVE" })
  };
  const router = {
    push: jest.fn(),
    pathname: "/communication-preferences",
    locale: "en-us",
    back: jest.fn()
  };
  const mockUseDetectScreen = jest.fn();
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/onboarding/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  mockMatchMedia();
  const errorHandler = jest.fn();
  const analytics = {
    canceledOnboardingFlow: jest.fn(),
    confirmedCommunicationPreferences: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    useDependency.mockReturnValue({
      analytics,
      errorHandler,
      metadataClient: {},
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
    MetadataService.mockReturnValue({
      getLanguages: jest.fn().mockResolvedValue([aLanguage()]),
      getLocales: jest.fn().mockResolvedValue([])
    });
  });

  it("saves communication preferences", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    expect(await screen.findByText("communication-preferences:labels.discordTitle")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.preferredEmailAddressTitle")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.preferredPhoneNumberTitle")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.contentLanguagesTitle")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.languageTitle")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.discordDescription")).toBeInTheDocument();
    expect(
      await screen.findByText("communication-preferences:labels.preferredEmailAddressDescription")
    ).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.contentLanguagesDescription")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.languageDescription")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.preferredEmail")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.preferredPhoneNumber")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.contentLanguage")).toBeInTheDocument();
    expect(await screen.findByText("communication-preferences:labels.language")).toBeInTheDocument();
  });

  it("shows modal with logout option", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    await userEvent.click(screen.getByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    await userEvent.click(screen.getByRole("button", { name: /closeHeader/i }));

    await userEvent.click(await screen.findByRole("button", { name: /Yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/communication-preferences"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Confirmed Communication Preferences' event when clicking on 'Next' button in the cancel registration modal", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        communicationPreferences: aCommunicationPreferences({
          contentLanguages: [
            aLanguage({ label: "English", code: "en", name: "English" }),
            aLanguage({ label: "Japanese", code: "jp", name: "Japanese" })
          ]
        })
      })
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledWith({
        locale: "en-us",
        contentLanguages: "English,Japanese"
      });
    });
  });

  it("navigates to 'connect-accounts' page when user click on 'Back' button", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/connect-accounts"));
  });

  it("removes a connected discord account", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    ConnectedAccountsService.removeDiscordAccount.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    await userEvent.click(await screen.findByRole("button", { name: /connect-accounts:removeAccount/i }));

    await userEvent.click(await screen.findByRole("button", { name: /^remove$/i }));

    await waitFor(() => {
      expect(ConnectedAccountsService.removeDiscordAccount).toHaveBeenCalledTimes(1);
    });
  });

  it("shows confirmation modal when user click on 'Back' button with form has unsaved changes", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    // There are two cancel buttons in this page used within to find the one in the modal
    const { findByRole, findByText } = within(await screen.findByRole("dialog"));
    expect(await findByRole("heading", { name: /breadcrumb:modalTitle/i })).toBeInTheDocument();
    expect(await findByText(/breadcrumb:modalMessage/i)).toBeInTheDocument();
    expect(await findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /discard/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /save/i })).toBeInTheDocument();
    expect(router.push).not.toBeCalled();
    expect(analytics.confirmedCommunicationPreferences).not.toBeCalled();
  });

  it("closes the confirmation modal on discard", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /discard/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
    expect(router.push).toHaveBeenCalledWith("/connect-accounts");
  });

  it("saves the form from confirmation modal and navigates back", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/connect-accounts");
  });

  it("closes the confirmation modal", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /Close$/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
  });

  it("detects viewport size and shows page back button for mobile", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    expect(await screen.findByRole("button", { name: /Back/i })).toHaveClass("display-back-bt");
  });

  it("saves the form from confirmation modal and navigates with stepper links", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: /Creator Type/i }));
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/creator-type");
  });

  it("navigates to 'terms-and-conditions' page when 'Next' is clicked after closing confirmation popup", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);
    const phoneNumber = await screen.findByLabelText(/^communication-preferences:labels.preferredPhoneNumber/i);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));
    await userEvent.click(await findByRole("button", { name: /cancel/i }));
    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/terms-and-conditions"));
  });

  it("navigates to 'Creator type' page when user click on stepper links without updating any form fields", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Creator type/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/creator-type"));
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage, userNavigated: true, onboardingSteps: steps }
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());

    const { unmount } = renderWithToast(<CommunicationPreferences {...communicationPreferencesProps} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();
    await user.click(screen.getByRole("button", { name: "close" }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(<CommunicationPreferences {...communicationPreferencesProps} />);

    await act(async () => {
      results = await axe(container);
    });
    expect(results).toHaveNoViolations();
  });
});
