import React, { FC, memo, useCallback, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Modal<PERSON>lose<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalTitle, ModalV2 } from "@eait-playerexp-cn/core-ui-kit";
import UploadContentSubmissionForm from "./UploadContentSubmissionForm";
import axios from "axios";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { ContentDetails } from "./ContentDeliverablesTab";

let source =
  axios.CancelToken.source(); /* If we declare axios cancelToke inside the component, cancel token reference is not working in the child component. Hence, declaring globally */

export type ContentSubmissionLabels = {
  contentTitleRequired: string;
  contentTypeRequired: string;
  contentTitleLongMessage: string;
  maxLimitMessage: string;
  invalidFileTypeMessage: string;
  success: {
    title: string;
    content: string;
  };
  error: {
    title: string;
    content: string;
  };
  contentDescriptionRequired?: string;
  contentDescriptionLongMessage?: string;
};

export type FormLabels = {
  contentTitle: string;
  contentTitlePlaceholder: string;
  contentType: string;
  fileUpload: string;
  chooseFile: string;
  noFileChoosen: string;
  acceptedFormats: string;
  maxFileSize: string;
  fileSelected: string;
  fileUploading: string;
  removeSelectedFile: string;
  contentDescription?: string;
  contentDescriptionPlaceholder?: string;
  maxcharacterLimit?: string;
  uploadFileProgress: string;
};

export type ContentTypes = {
  name: string;
  label: string;
  value: string;
}[];

export type ButtonLabels = {
  cancel: string;
  upload: string;
  close: string;
};

export type FileTypes = {
  type: string;
  extensions: string[];
}[];
export type UploadContentSubmissionModalProps = {
  setUpdatedContent: (boolean) => void;
  onClose: () => void;
  title: string;
  formLabels: FormLabels;
  contentTypes: ContentTypes;
  buttonsLabels: ButtonLabels;
  participationId: string;
  thumbnail: string;
  contentSubmissionLabels: ContentSubmissionLabels;
  fileTypes: FileTypes;
  analytics: BrowserAnalytics;
  locale: string;
  opportunity: OpportunityWithDeliverables;
  isForUpdateUploadedFile?: boolean;
  content?: ContentDetails;
  deliverableId?: string;
  deliverableTitle?: string;
  deliverableType?: string;
  nucleusId?: number;
  creatorId: string;
};

const UploadContentSubmissionModal: FC<UploadContentSubmissionModalProps> = ({
  setUpdatedContent,
  onClose,
  title,
  formLabels,
  contentTypes,
  buttonsLabels,
  contentSubmissionLabels,
  participationId,
  thumbnail,
  fileTypes,
  analytics,
  locale,
  opportunity,
  isForUpdateUploadedFile,
  content,
  deliverableId,
  deliverableTitle,
  deliverableType,
  creatorId,
  nucleusId
}) => {
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [uploadPercentage, setUploadPercentage] = useState(0);

  const cancelUploadProgress = useCallback(() => {
    if (showProgressBar && uploadPercentage < 100) {
      source.cancel("successfully cancelled the upload"); /* we can set any message and use it later, it can be empty */
      source = axios.CancelToken.source();
    }
    resetUploadForm();
  }, [showProgressBar, uploadPercentage]);

  const resetUploadForm = useCallback(() => {
    setShowProgressBar(false);
    setUploadPercentage(0);
    onClose();
  }, []);
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  return (
    <ModalV2 closeButtonRef={cancelButtonRef} closeOnOutsideClick={false}>
      <ModalHeader>
        <ModalTitle>{title}</ModalTitle>
        <ModalCloseButton
          ariaLabel={buttonsLabels.close}
          ref={cancelButtonRef}
          closeButtonRef={cancelButtonRef}
          onClose={cancelUploadProgress}
        ></ModalCloseButton>
      </ModalHeader>
      <ModalBody>
        <UploadContentSubmissionForm
          {...{
            setUpdatedContent,
            formLabels,
            contentTypes,
            buttonsLabels,
            cancelUploadProgress,
            resetUploadForm,
            participationId,
            thumbnail,
            contentSubmissionLabels,
            fileTypes,
            showProgressBar,
            setShowProgressBar,
            source,
            uploadPercentage,
            setUploadPercentage,
            analytics,
            locale,
            opportunity,
            isForUpdateUploadedFile,
            content,
            deliverableId,
            deliverableTitle,
            deliverableType,
            creatorId,
            nucleusId
          }}
        />
      </ModalBody>
    </ModalV2>
  );
};

UploadContentSubmissionModal.defaultProps = {
  isForUpdateUploadedFile: false,
  content: null
};

export default memo(UploadContentSubmissionModal);
