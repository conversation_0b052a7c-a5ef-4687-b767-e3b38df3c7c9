import { Icon, logout, user as userIcon } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { useRef, useState } from "react";
import cx from "classnames";

const ProfileDropdown = ({ labels, children, analytics }) => {
  const router = useRouter();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isKeyDownEventCalled, setIsKeyDownEventCalled] = useState(false);
  const avatar_img_ref = useRef(null);

  const ENTER_KEY = "Enter";
  const TAB_KEY = "Tab";
  const ARROW_DOWN_KEY = "ArrowDown";
  const ARROW_UP_KEY = "ArrowUp";
  const MOUSE_DOWN_PRIMARY_CLICK = 0;
  const navigateToMyProfile = () => {
    analytics.visitedMyProfile({ locale: router.locale });
    router.push("/profile");
  };
  const logoutApplication = () => {
    analytics.signedOutOfCreatorNetwork({ locale: router.locale });
    router.push("/api/logout");
  };
  const onMouseOverProfileMenu = () => {
    setIsProfileMenuOpen(true);
    avatar_img_ref.current.focus();
  };
  const onBlurProfileMenu = () => {
    if (!isKeyDownEventCalled) setIsProfileMenuOpen(false);
  };
  const navigateProfileMenus = (e) => {
    const menuNodes = document.querySelectorAll(".ea-header-web-profile-menu-link") as unknown as Array<HTMLElement>;
    const nodeLength = menuNodes.length;
    const menuIndex = [...menuNodes].indexOf(document.activeElement as HTMLElement);
    if (e.key === ARROW_DOWN_KEY) {
      if (menuIndex < nodeLength - 1) menuNodes[menuIndex + 1].focus();
      else menuNodes[nodeLength - 1].focus();
    } else if (e.key === ARROW_UP_KEY) {
      if (menuIndex > 0) menuNodes[menuIndex - 1].focus();
      else menuNodes[0].focus();
    }
  };
  const onKeyDownProfileMenu = (e) => {
    navigateProfileMenus(e);
    if (e.key === ENTER_KEY) {
      setIsProfileMenuOpen(true);
      setIsKeyDownEventCalled(true);
    } else if (e.key === TAB_KEY) {
      setIsProfileMenuOpen(false);
      setIsKeyDownEventCalled(false);
    }
  };
  const onKeyDownOnProfileIcon = (e) => {
    navigateProfileMenus(e);
    if (e.key === ENTER_KEY) {
      navigateToMyProfile();
      setIsProfileMenuOpen(false);
      setIsKeyDownEventCalled(false);
    } else if (e.key === TAB_KEY) {
      setIsProfileMenuOpen(false);
      setIsKeyDownEventCalled(false);
    }
  };
  const onKeyDownOnLogOutIcon = (e) => {
    navigateProfileMenus(e);
    if (e.key === ENTER_KEY) {
      logoutApplication();
      setIsProfileMenuOpen(false);
      setIsKeyDownEventCalled(false);
    } else if (e.key === TAB_KEY) {
      setIsProfileMenuOpen(false);
      setIsKeyDownEventCalled(false);
    }
  };
  const onMouseDownOnProfileIcon = (e) => {
    if (e.button === MOUSE_DOWN_PRIMARY_CLICK) {
      navigateToMyProfile();
    }
  };
  const onMouseDownOnLogOutIcon = (e) => {
    if (e.button === MOUSE_DOWN_PRIMARY_CLICK) {
      logoutApplication();
    }
  };

  return (
    <div
      className={cx({
        "ea-header-web-profile-image-container": !isProfileMenuOpen,
        "ea-header-web-profile-image-container-focus": isProfileMenuOpen
      })}
      ref={avatar_img_ref}
      tabIndex={0}
      onMouseOver={onMouseOverProfileMenu}
      onBlur={onBlurProfileMenu}
      onKeyDown={(e) => onKeyDownProfileMenu(e)}
    >
      {children}
      <div
        tabIndex={0}
        className={cx({
          "ea-header-web-profile-menu-hide": !isProfileMenuOpen,
          "ea-header-web-profile-menu": isProfileMenuOpen
        })}
      >
        <button
          tabIndex={0}
          className="ea-header-web-profile-menu-link"
          onMouseDown={onMouseDownOnProfileIcon}
          onKeyDown={onKeyDownOnProfileIcon}
        >
          <Icon
            icon={userIcon}
            width="1.5rem"
            height="1.5rem"
            color={"#3028C7"}
            className="ea-header-web-profile-menu-link-icon"
          />
          <span className="ea-header-web-profile-menu-link-label">{labels.myProfile}</span>
        </button>

        <button
          tabIndex={0}
          className="ea-header-web-profile-menu-link"
          onMouseDown={onMouseDownOnLogOutIcon}
          onKeyDown={onKeyDownOnLogOutIcon}
        >
          <Icon
            icon={logout}
            width="1.5rem"
            height="1.25rem"
            color={"#3028C7"}
            className="ea-header-web-profile-menu-link-icon"
          />
          <span className="ea-header-web-profile-menu-link-label">{labels.signout}</span>
        </button>
      </div>
    </div>
  );
};

export default ProfileDropdown;
