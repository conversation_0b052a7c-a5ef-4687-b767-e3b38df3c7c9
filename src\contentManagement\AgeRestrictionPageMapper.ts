import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type AgeRestrictionPageLabels = {
  ageRestrictionLabels: {
    title: string;
    bannerImageLabel: string;
    subTitle: string;
    close: string;
  };
};

export class AgeRestrictionPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): AgeRestrictionPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      ageRestrictionLabels: {
        title: microCopy.get("ageRestriction.title"),
        bannerImageLabel: microCopy.get("ageRestriction.bannerImageLabel"),
        subTitle: microCopy.get("ageRestriction.subTitle"),
        close: microCopy.get("common.close")
      }
    };
  }
}
