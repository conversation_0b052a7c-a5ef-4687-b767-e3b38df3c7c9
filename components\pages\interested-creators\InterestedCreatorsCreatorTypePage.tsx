import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import InterestedCreatorsCreatorTypeForm from "../../forms/InterestedCreatorsCreatorTypeForm";
import {
  FormLabels,
  InterestedCreatorsCreatorType,
  PageLabels
} from "../../../pages/interested-creators/creator-types";
import { ERROR, onToastClose, toastContent, VALIDATION_ERROR } from "../../../utils";
import { Toast } from "../../toast";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";
import { TFunction } from "next-i18next";
import { NextRouter } from "next/router";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import CancelRegistrationModal from "./CancelRegistrationModal";
import { useDependency } from "@src/context/DependencyContext";

export type CloseHandler = {
  onClose?: MouseEventHandler<HTMLButtonElement>;
};

export const getCreatorsTypes = async (
  metadataService: MetadataService,
  stableDispatch,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  interestedCreator,
  errorHandler
) => {
  try {
    const creatorTypes = await metadataService.getCreatorTypes();
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      const interestedCreatorsCreatorType = [];
      interestedCreator.creatorTypes.forEach((item: CreatorType) => {
        const savedCreatorType = typeof item === "object" ? item.value : item;
        const foundType = creatorTypes.find((creatorType) => creatorType.value === savedCreatorType);
        if (foundType) interestedCreatorsCreatorType.push(foundType);
      });
      interestedCreator.creatorTypes = interestedCreatorsCreatorType;
    }
    return creatorTypes;
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export type InterestedCreatorsCreatorTypePageProps = {
  formLabels: FormLabels;
  pageLabels: PageLabels;
  t: TFunction;
  showConfirmation?: boolean;
  children?: ReactNode;
  interestedCreator?: InterestedCreatorsCreatorType;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  state: any; // will need to revisit this later
  stableDispatch: () => void;
  errorToast: (JSXElementConstructor, CloseHandler) => void;
  unhandledError?: string;
  setShowConfirmation: (a: boolean) => void;
  router: NextRouter;
  locale: string;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
} & CloseHandler;

export default memo(function InterestedCreatorsCreatorTypePage({
  formLabels,
  pageLabels,
  t,
  onClose,
  showConfirmation = false,
  interestedCreator,
  stableDispatch,
  unhandledError = "",
  state,
  errorToast,
  setShowConfirmation,
  router,
  locale,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD
}: InterestedCreatorsCreatorTypePageProps) {
  const { errorHandler } = useDependency();
  const { interestedCreatorTitle, interestedCreatorDescription } = pageLabels;
  const { close } = formLabels;
  const { isError = false, isValidationError = false } = state;
  const [creatorTypes, setCreatorsTypes] = useState([]);
  const { metadataClient } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);

  useEffect(() => {
    async function fetchData() {
      const creatorTypes = await getCreatorsTypes(
        metadataService,
        stableDispatch,
        INTERESTED_CREATOR_REAPPLY_PERIOD,
        interestedCreator,
        errorHandler
      );
      if (creatorTypes) setCreatorsTypes(creatorTypes);
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);

  const handleCancelRegistration = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [router, locale]);

  const modalLabels = {
    title: pageLabels.modalConfirmationTitle,
    yes: formLabels.yes,
    no: formLabels.no,
    close: formLabels.close,
    confirmationDesc1: pageLabels.confirmationDesc1,
    confirmationDesc2: pageLabels.confirmationDesc2
  };

  return (
    <section className="interested-creators-creator-type-container">
      <div className="mg-intro">
        <h3 className="interested-creators-creator-type-title">{interestedCreatorTitle}</h3>
      </div>
      <div className="interested-creators-creator-type-description">{interestedCreatorDescription}</div>
      <InterestedCreatorsCreatorTypeForm
        {...{
          formLabels,
          onClose,
          creatorTypes,
          interestedCreator,
          t,
          router,
          analytics,
          stableDispatch
        }}
      />

      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: modalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </section>
  );
});
