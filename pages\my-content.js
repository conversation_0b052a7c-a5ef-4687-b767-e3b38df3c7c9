import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsDashboard from "../config/translations/dashboard";
import { useRouter } from "next/router";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import Pagination from "../components/dashboard/Pagination";
import SubmittedContentService from "../src/api/services/SubmittedContentService";
import { HAS_EXCEPTION, SESSION_USER } from "../utils";
import Error from "./_error";
import { useAppContext } from "../src/context";
import withRegisteredUser from "../src/utils/WithRegisteredUser";
import Loading from "../components/Loading";
import withTermsAndConditionsUpToDate from "../src/utils/WithTermsAndConditionsUpToDate";
import { ContentCard } from "@eait-playerexp-cn/core-ui-kit";
import labelsMyContent from "../config/translations/my-content";
import RedirectException from "../src/utils/RedirectException";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import Link from "next/link";
import classNames from "classnames/bind";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Footer from "../components/footer/ProgramFooter";
import Header from "../components/header/Header";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import { useDependency } from "../src/context/DependencyContext";
import flags from "../utils/feature-flags";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "../src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import myContentProps from "../src/serverprops/MyContentProps";

const PAGE_SIZE = 10;
const DEFAULT_PAGE_NUMBER = 1;

/**
 * A component for listing the contents submitted by current logged in user.
 *
 * @param {object} user - a current logged in user details
 * @param {string} locale - a locale to translate the app
 * @returns {JSX.Element}
 */
export default function MyContent({
  user,
  locale,
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_CONTENT_WITH_FINAL_REMARK
}) {
  const {
    analytics,
    errorHandler,
    configuration: { FLAG_SUBMITTED_CONTENT_WITH_PROGRAM }
  } = useDependency();
  const router = useRouter();
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const [isLoading, setIsLoading] = useState(false);
  const [pages, setPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(null);
  const [currentPageContents, setCurrentPageContents] = useState(null);
  const [contents, setContents] = useState({});
  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout, dashboardLabels, notificationsLabels, myContentLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      dashboardLabels: labelsDashboard(t),
      layout: labelsCommon(t),
      notificationsLabels: notificationBellLabels,
      myContentLabels: labelsMyContent(t)
    };
    labels.layout.footer = { locale: router.locale, labels: labels.layout.footer };
    return labels;
  }, [t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer.labels;

  const stableDispatch = useCallback(dispatch, []);
  const [contentsFeedback, setContentsFeedback] = useState(null);
  const [selectedContentId, setSelectedContentId] = useState("");
  const [feedbackLoading, setFeedbackLoading] = useState(false);

  const formatSubmittedContent = (contents) => {
    let formattedContent = contents;
    formattedContent.forEach((content) => (content.contentTypeLabel = myContentLabels[content.contentType]));
    setCurrentPageContents(formattedContent);
  };

  useEffect(() => {
    function getSubmittedContents() {
      if (!router.isReady) return;
      let currentPage = DEFAULT_PAGE_NUMBER;
      if (router.query && router.query.page && parseInt(router.query.page) > 0) {
        currentPage = parseInt(router.query.page);
      }
      setIsLoading(true);
      const getSubmittedContent = FLAG_CONTENT_WITH_FINAL_REMARK
        ? SubmittedContentService.getSubmittedContentsFinalRemarks
        : SubmittedContentService.getSubmittedContents;
      const getFeatureSubmittedContent = FLAG_SUBMITTED_CONTENT_WITH_PROGRAM
        ? SubmittedContentService.getSubmittedContentsFinalRemarksWithProgramCode
        : getSubmittedContent;

      getFeatureSubmittedContent(PAGE_SIZE, currentPage)
        .then((res) => {
          setIsLoading(false);
          if (res.data && res.data.total > 0) {
            let pages = [];
            for (let i = 0; i < Math.ceil(res.data.total / PAGE_SIZE); i++) {
              pages.push(i + 1);
            }
            setPages(pages);
            setContents({ ...contents, [currentPage]: res.data.contents });
            setCurrentPage(currentPage);
            formatSubmittedContent(res.data.contents);
          }
        })
        .catch((e) => {
          setIsLoading(false);
          dispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
        });
    }
    getSubmittedContents();
  }, [router.isReady]);

  useEffect(() => {
    if (currentPage > 0) {
      if (contents[currentPage] && contents[currentPage].length > 0) {
        setCurrentPageContents(contents[currentPage]);
      } else {
        setIsLoading(true);
        const getSubmittedContent = FLAG_CONTENT_WITH_FINAL_REMARK
          ? SubmittedContentService.getSubmittedContentsFinalRemarks
          : SubmittedContentService.getSubmittedContents;
        const getFeatureSubmittedContent = FLAG_SUBMITTED_CONTENT_WITH_PROGRAM
          ? SubmittedContentService.getSubmittedContentsFinalRemarksWithProgramCode
          : getSubmittedContent;
        getFeatureSubmittedContent(PAGE_SIZE, currentPage)
          .then((res) => {
            setIsLoading(false);
            if (res.data && res.status === 200 && res.data.total > 0) {
              setContents({ ...contents, [currentPage]: res.data.contents });
              formatSubmittedContent(res.data.contents);
            }
          })
          .catch((e) => dispatch({ type: HAS_EXCEPTION, data: e?.response?.status }));
      }
    }
  }, [currentPage]);

  useEffect(() => {
    if (user) dispatch({ type: SESSION_USER, data: user });
  }, [user]);

  const navigateToOpportunityDetails = (opportunityId) => {
    router.push(`/opportunities/${opportunityId}`);
  };
  const onToggleFeedback = async (collapsed, contentId, status, cardType) => {
    setSelectedContentId(contentId);
    if (collapsed == false) return;
    if (status === "REJECTED" || status === "APPROVED") return;
    const currentFeedback = contentsFeedback?.[contentId]
      ? contentsFeedback?.[contentId]
      : await getFeedbacks({ contentId }, cardType);
    setContentsFeedback({ ...contentsFeedback, [contentId]: currentFeedback });
    setFeedbackLoading(false);
  };
  const getFeedbacks = async (criteria, type) => {
    setFeedbackLoading(true);
    try {
      const response = await SubmittedContentService.getContentsFeedback(criteria);
      return response.data.contentsFeedback.slice(0, 1).map((feedback) => ({
        ...feedback,
        ...layout.contentCard,
        sentOnLabel: layout.contentCard.sentOn,
        fromLabel: layout.contentCard.from,
        title: layout.contentCard.changesRequired,
        contentVersion: feedback.contentVersion,
        lastUpdateDate: `${feedback.formattedSubmittedDate(locale)}`,
        content: feedback.description,
        note: t("common:additionalDescription", {
          ...{
            contentType: type === "FILE" ? type.toLowerCase() : layout.contentCard.url?.toLowerCase(),
            updateType:
              type === "FILE" ? layout.buttons.upload?.toLowerCase() : layout.contentCard.update?.toLowerCase()
          }
        })
      }));
    } catch (e) {
      setFeedbackLoading(false);
      errorHandler(stableDispatch, e);
    }
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.myContent}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={null}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        {isLoading && <Loading />}
        <div className="my-content-container">
          <div className="my-content-full-screen">
            <h3 className="my-content-title">{layout.header.myContent}</h3>
            <div className="my-content-sub-title">{dashboardLabels.myContentDescription}</div>
            <div className={classNames("my-content-list-container", { "empty-list": !currentPageContents })}>
              {currentPageContents ? (
                <div className="my-content-list my-content-list-remove-padding-x">
                  <ContentCardsWithFeedback
                    currentPageContents={currentPageContents}
                    layout={layout}
                    handleToggleFeedback={onToggleFeedback}
                    navigateToOpportunityDetails={navigateToOpportunityDetails}
                    locale={router.locale}
                    contentsFeedback={contentsFeedback}
                    selectedContentId={selectedContentId}
                    feedbackLoading={feedbackLoading}
                  />
                </div>
              ) : (
                <div className="my-content-no-content">
                  <div className="my-content-no-content-row1">
                    <img src="/img/dashboard/Lightbulb.png" alt="icon" className="no-my-content-icon" />
                    <div>
                      <div className="my-content-no-content-title">{dashboardLabels.noSubmittedContent}</div>
                      <div className="my-content-no-content-desc">
                        {dashboardLabels.noSubmittedContentDesc}{" "}
                        <Link href="/opportunities" className="dashboard-opportunities-no-content-desc-link">
                          {layout.header.opportunities}
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            {pages && pages.length > 1 && (
              <Pagination
                next={layout.buttons.next}
                prev={layout.buttons.prev}
                pages={pages}
                currentPage={currentPage}
                onPageChange={(page) => setCurrentPage(page)}
              />
            )}
          </div>
        </div>
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={router.locale}
          labels={footerLabels}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
}

const ContentCardsWithFeedback = memo(function ContentCardsWithFeedback({
  currentPageContents,
  layout,
  handleToggleFeedback,
  navigateToOpportunityDetails,
  locale,
  contentsFeedback,
  selectedContentId,
  feedbackLoading
}) {
  const { configuration: config } = useDependency();
  return (
    <div>
      {currentPageContents.map((item, index) => (
        <div key={item.id}>
          <ContentCard
            key={index}
            content={{
              ...item,
              reviewFinalRemark: item.reviewFinalRemark
                ? {
                    ...item.reviewFinalRemark,
                    date: item.formattedReviewFinalRemarkDate(locale)
                  }
                : item.reviewFinalRemark
            }}
            labels={layout.contentCard}
            accountType={
              item.type ? item.type : item.sourceType === "USER_DEVICE" ? "UPLOAD" : item.sourceType
            } /* type will be present only when SOURCE_TYPE is 'SOCIAL'.
          When it's a website or file content, type will be null and Content Card we'll have to rely on sourceType attribute instead of type. */
            opportunityClickHandler={() => {
              navigateToOpportunityDetails(item.opportunityId);
            }}
            submittedDate={`${item.formattedSubmittedDate(locale)}`}
            changesRequested={item.requiresChanges()}
            handleToggleFeedback={(collapsed) =>
              handleToggleFeedback(
                collapsed,
                item.id,
                item.status,
                item.type ? item.type : item.sourceType === "USER_DEVICE" ? "FILE" : item.sourceType
              )
            }
            feedback={{
              ...contentsFeedback?.[item.id]?.[0]
            }}
            isLoading={item.id === selectedContentId ? feedbackLoading : false}
            isMcrEnabled={config.FLAG_SIGNED_URL_V2_ENABLED || config.FLAG_SIGNED_URL_V1_ENABLED}
          />
        </div>
      ))}
    </div>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(myContentProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_CONTENT_WITH_FINAL_REMARK: flags.isContentWithFinalRemarksEnabled(),
      locale,
      ...(await serverSideTranslations(locale, [
        "common",
        "dashboard",
        "my-content",
        "notifications",
        "connect-accounts",
        "opportunities"
      ]))
    }
  };
};
