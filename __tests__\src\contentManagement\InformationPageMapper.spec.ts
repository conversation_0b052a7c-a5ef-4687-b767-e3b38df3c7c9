import { InformationPageMapper } from "@src/contentManagement/InformationPageMapper";

describe("InformationPageMapper", () => {
  const microCopies = {
    "information.messages.firstName": "First Name",
    "information.messages.emailTooLong": "Email Too Long",
    "information.messages.email": "Email",
    "information.messages.duplicateUrl": "Duplicate URL",
    "information.messages.lastNameTooLong": "Last Name Too Long",
    "information.messages.state": "State",
    "information.messages.tShirtSize": "T-Shirt Size",
    "information.messages.dateOfBirthInvalid": "Date of Birth Invalid",
    "information.messages.country": "Country",
    "information.messages.city": "City",
    "information.messages.emailInvalid": "Email Invalid",
    "information.messages.ageMustBe18OrOlder": "Age Must Be 18 or Older",
    "information.messages.street": "Street",
    "information.messages.invalidUrl": "Invalid URL",
    "information.messages.hardwarePartners": "Hardware Partners",
    "information.messages.dateOfBirth": "Date of Birth",
    "information.messages.firstNameTooLong": "First Name Too Long",
    "information.messages.businessNameTooLong": "Business Name Too Long",
    "information.messages.primaryPlatform": "Primary Platform",
    "information.messages.businessName": "Business Name",
    "information.messages.streetTooLong": "Street Too Long",
    "information.messages.stateTooLong": "State Too Long",
    "information.messages.urlScanFailed": "URL Scan Failed",
    "information.messages.entityType": "Entity Type",
    "information.messages.zipCode": "Zip Code",
    "information.messages.zipCodeTooLong": "Zip Code Too Long",
    "information.messages.url": "URL",
    "information.messages.cityTooLong": "City Too Long",
    "information.messages.followersMaxLength": "Followers Max Length",
    "information.messages.lastName": "Last Name",
    "information.labels.state": "State",
    "information.labels.hardwarePartners": "Hardware Partners",
    "information.labels.city": "City",
    "information.labels.contentFollowers": "Content Followers",
    "information.labels.EAEmail": "EA Email",
    "information.labels.tShirtSize": "T-Shirt Size",
    "information.labels.country": "Country",
    "information.labels.contentFollowersPlaceholder": "Content Followers Placeholder",
    "information.labels.contentUrl": "Content URL",
    "information.labels.contentMediaTitle": "Content Media Title",
    "information.labels.none": "None",
    "information.labels.contentLanguage": "Content Language",
    "information.labels.additionalLinkPlaceholder": "Additional Link Placeholder",
    "information.labels.lastName": "Last Name",
    "information.labels.preferredEmail": "Preferred Email",
    "information.labels.zipCode": "Zip Code",
    "information.labels.business": "Business",
    "information.labels.individual": "Individual",
    "information.labels.businessName": "Business Name",
    "information.labels.legalAddressAsMailingAddress": "Legal Address as Mailing Address",
    "information.labels.additionalContentAndWebsiteDescription": "Additional Content and Website Description",
    "information.labels.EAID": "EA ID",
    "information.labels.firstName": "First Name",
    "information.labels.dateOfBirth": "Date of Birth",
    "information.labels.selectCountry": "Select Country",
    "information.labels.additionalContentAndWebsiteTitle": "Additional Content and Website Title",
    "information.labels.primaryPlatform": "Primary Platform",
    "information.labels.websiteUrlLabel": "Website URL Label",
    "information.labels.contentUrlPlaceholder": "Content URL Placeholder",
    "information.labels.entityType": "Entity Type",
    "information.labels.street": "Street",
    "information.labels.contentMediaDescription": "Content Media Description",
    "information.info.businessName": "Business Name",
    "information.success.miscellaneous": "Miscellaneous",
    "information.success.personalInformation": "Personal Information",
    "information.success.legalEntityType": "Legal Entity Type",
    "information.success.updatedInformationHeader": "Updated Information Header",
    "information.success.mailingAddress": "Mailing Address",
    "information.success.platformPreferences": "Platform Preferences",
    "information.profilePicture.termsAndConditionsLast": "Terms and Conditions Last",
    "information.profilePicture.avatarRequired": "Avatar Required",
    "information.profilePicture.message": "Message",
    "information.profilePicture.termsAndConditionsFirst": "Terms and Conditions First",
    "information.profilePicture.title": "Title",
    "information.profilePicture.avatarInvalid": "Avatar Invalid",
    "information.profilePicture.avatarMoreThanLimit": "Avatar More Than Limit",
    "information.profilePicture.termsAndConditionsMiddle": "Terms and Conditions Middle",
    "information.miscellaneous": "Miscellaneous",
    "information.creatorSince": "Creator Since",
    "information.secondaryPlatformsTitle": "Secondary Platforms Title",
    "information.interestedUserDescription2": "Interested User Description 2",
    "information.legalEntityDescription": "Legal Entity Description",
    "information.interestedUserDescription1": "Interested User Description 1",
    "information.secondaryPlatforms": "Secondary Platforms",
    "information.primaryPlatform": "Primary Platform",
    "information.mailingAddress": "Mailing Address",
    "information.legalEntityType": "Legal Entity Type",
    "information.platformPreferencesTitle": "Platform Preferences Title",
    "information.description": "Description",
    "information.platformPreferences": "Platform Preferences",
    "information.title": "Title",
    "information.interestedUserDescription": "Interested User Description",
    "information.infoSubTitle": "Info Sub Title",
    "information.infoTitle": "Info Title",
    "information.interestedCreatorTitle": "Interested Creator Title",
    "information.personalInformation": "Personal Information",
    "information.basicInformation": "Basic Information",
    "information.eaEmailID": "EA Email ID",
    "information.informationPageTitle": "SaC - Information"
  };

  it("maps information page labels", () => {
    const mapper = new InformationPageMapper();
    const labels = mapper.map(microCopies).informationLabels;

    expect(labels.messages.firstName).toEqual("First Name");
    expect(labels.messages.emailTooLong).toEqual("Email Too Long");
    expect(labels.messages.email).toEqual("Email");
    expect(labels.labels.duplicateUrl).toEqual("Duplicate URL");
    expect(labels.messages.lastNameTooLong).toEqual("Last Name Too Long");
    expect(labels.messages.state).toEqual("State");
    expect(labels.messages.tShirtSize).toEqual("T-Shirt Size");
    expect(labels.messages.dateOfBirthInvalid).toEqual("Date of Birth Invalid");
    expect(labels.messages.country).toEqual("Country");
    expect(labels.messages.city).toEqual("City");
    expect(labels.messages.emailInvalid).toEqual("Email Invalid");
    expect(labels.messages.ageMustBe18OrOlder).toEqual("Age Must Be 18 or Older");
    expect(labels.messages.street).toEqual("Street");
    expect(labels.labels.invalidUrl).toEqual("Invalid URL");
    expect(labels.messages.hardwarePartners).toEqual("Hardware Partners");
    expect(labels.messages.dateOfBirth).toEqual("Date of Birth");
    expect(labels.messages.firstNameTooLong).toEqual("First Name Too Long");
    expect(labels.messages.businessNameTooLong).toEqual("Business Name Too Long");
    expect(labels.messages.primaryPlatform).toEqual("Primary Platform");
    expect(labels.messages.businessName).toEqual("Business Name");
    expect(labels.messages.streetTooLong).toEqual("Street Too Long");
    expect(labels.messages.stateTooLong).toEqual("State Too Long");
    expect(labels.labels.urlScanFailed).toEqual("URL Scan Failed");
    expect(labels.messages.entityType).toEqual("Entity Type");
    expect(labels.messages.zipCode).toEqual("Zip Code");
    expect(labels.messages.zipCodeTooLong).toEqual("Zip Code Too Long");
    expect(labels.messages.url).toEqual("URL");
    expect(labels.messages.cityTooLong).toEqual("City Too Long");
    expect(labels.messages.followersMaxLength).toEqual("Followers Max Length");
    expect(labels.messages.lastName).toEqual("Last Name");
    expect(labels.labels.state).toEqual("State");
    expect(labels.labels.hardwarePartners).toEqual("Hardware Partners");
    expect(labels.labels.city).toEqual("City");
    expect(labels.labels.contentFollowers).toEqual("Content Followers");
    expect(labels.labels.EAEmail).toEqual("EA Email");
    expect(labels.labels.tShirtSize).toEqual("T-Shirt Size");
    expect(labels.labels.country).toEqual("Country");
    expect(labels.labels.contentFollowersPlaceholder).toEqual("Content Followers Placeholder");
    expect(labels.labels.contentUrl).toEqual("Content URL");
    expect(labels.labels.contentMediaTitle).toEqual("Content Media Title");
    expect(labels.labels.none).toEqual("None");
    expect(labels.labels.contentLanguage).toEqual("Content Language");
    expect(labels.labels.additionalLinkPlaceholder).toEqual("Additional Link Placeholder");
    expect(labels.labels.lastName).toEqual("Last Name");
    expect(labels.labels.preferredEmail).toEqual("Preferred Email");
    expect(labels.labels.zipCode).toEqual("Zip Code");
    expect(labels.labels.business).toEqual("Business");
    expect(labels.labels.individual).toEqual("Individual");
    expect(labels.labels.businessName).toEqual("Business Name");
    expect(labels.labels.legalAddressAsMailingAddress).toEqual("Legal Address as Mailing Address");
    expect(labels.labels.additionalContentAndWebsiteDescription).toEqual("Additional Content and Website Description");
    expect(labels.labels.EAID).toEqual("EA ID");
    expect(labels.labels.firstName).toEqual("First Name");
    expect(labels.labels.dateOfBirth).toEqual("Date of Birth");
    expect(labels.labels.selectCountry).toEqual("Select Country");
    expect(labels.labels.additionalContentAndWebsiteTitle).toEqual("Additional Content and Website Title");
    expect(labels.labels.primaryPlatform).toEqual("Primary Platform");
    expect(labels.labels.websiteUrlLabel).toEqual("Website URL Label");
    expect(labels.labels.contentUrlPlaceholder).toEqual("Content URL Placeholder");
    expect(labels.labels.entityType).toEqual("Entity Type");
    expect(labels.labels.street).toEqual("Street");
    expect(labels.labels.contentMediaDescription).toEqual("Content Media Description");
    expect(labels.info.businessName).toEqual("Business Name");
    expect(labels.success.miscellaneous).toEqual("Miscellaneous");
    expect(labels.success.personalInformation).toEqual("Personal Information");
    expect(labels.success.legalEntityType).toEqual("Legal Entity Type");
    expect(labels.success.updatedInformationHeader).toEqual("Updated Information Header");
    expect(labels.success.mailingAddress).toEqual("Mailing Address");
    expect(labels.success.platformPreferences).toEqual("Platform Preferences");
    expect(labels.profilePicture.termsAndConditionsLast).toEqual("Terms and Conditions Last");
    expect(labels.profilePicture.avatarRequired).toEqual("Avatar Required");
    expect(labels.profilePicture.message).toEqual("Message");
    expect(labels.profilePicture.termsAndConditionsFirst).toEqual("Terms and Conditions First");
    expect(labels.profilePicture.title).toEqual("Title");
    expect(labels.profilePicture.avatarInvalid).toEqual("Avatar Invalid");
    expect(labels.profilePicture.avatarMoreThanLimit).toEqual("Avatar More Than Limit");
    expect(labels.profilePicture.termsAndConditionsMiddle).toEqual("Terms and Conditions Middle");
    expect(labels.miscellaneous).toEqual("Miscellaneous");
    expect(labels.creatorSince).toEqual("Creator Since");
    expect(labels.secondaryPlatformsTitle).toEqual("Secondary Platforms Title");
    expect(labels.interestedUserDescription2).toEqual("Interested User Description 2");
    expect(labels.legalEntityDescription).toEqual("Legal Entity Description");
    expect(labels.interestedUserDescription1).toEqual("Interested User Description 1");
    expect(labels.secondaryPlatforms).toEqual("Secondary Platforms");
    expect(labels.primaryPlatform).toEqual("Primary Platform");
    expect(labels.mailingAddress).toEqual("Mailing Address");
    expect(labels.legalEntityType).toEqual("Legal Entity Type");
    expect(labels.platformPreferencesTitle).toEqual("Platform Preferences Title");
    expect(labels.description).toEqual("Description");
    expect(labels.platformPreferences).toEqual("Platform Preferences");
    expect(labels.title).toEqual("Title");
    expect(labels.interestedUserDescription).toEqual("Interested User Description");
    expect(labels.infoSubTitle).toEqual("Info Sub Title");
    expect(labels.infoTitle).toEqual("Info Title");
    expect(labels.interestedCreatorTitle).toEqual("Interested Creator Title");
    expect(labels.personalInformation).toEqual("Personal Information");
    expect(labels.basicInformation).toEqual("Basic Information");
    expect(labels.eaEmailID).toEqual("EA Email ID");
    expect(labels.informationPageTitle).toEqual("SaC - Information");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new InformationPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key information.messages.firstName is absent");
  });
});