import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsCommon from "../config/translations/common";
import labelsAccessError from "../config/translations/access-error";
import Link from "next/link";
import Head from "next/head";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import accessErrorProps from "@src/serverprops/AccessErrorProps";

export type AccessErrorProps = {
  runtimeConfiguration?: Record<string, unknown>;
  showInitialMessage: boolean;
};

export default function AccessError() {
  const { t } = useTranslation(["common", "access-error"]);
  const { layout, accessErrorLabels } = useMemo(() => {
    return {
      layout: labelsCommon(t),
      accessErrorLabels: labelsAccessError(t)
    };
  }, [t]);

  return (
    <>
      <Head>
        <title>{accessErrorLabels.pageTitle}</title>
      </Head>
      <div className="login-mg-container">
        <div className="login-error-container">
          <img src="/img/icons/error-icon.png" alt="Error" className="login-error-image" />
          <div className="login-error-title">{accessErrorLabels.title}</div>
          <div className="login-error-sub-title">{accessErrorLabels.subTitle}</div>
          <div className="login-error-body">{accessErrorLabels.description}</div>
          <div className="login-button-logout">
            <Link href="/api/logout" type="submit" className="btn btn-primary btn-md">
              {layout.buttons.logout}
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(accessErrorProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<AccessErrorProps>;
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      ...(await serverSideTranslations(locale, ["common", "access-error"])),
      showInitialMessage: req?.session?.showInitialMessage || false
    }
  };
};
