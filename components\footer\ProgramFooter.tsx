import React, { memo, useCallback } from "react";
import { useRouter } from "next/router";
import { setCookie } from "../../utils";
import LegacyFooter from "./LegacyFooter";
import usa from "../icons/Usa";
import japan from "../icons/Japan";
import germany from "../icons/Germany";
import france from "../icons/France";
import spain from "../icons/Spain";
import italy from "../icons/Italy";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import { FooterLabels } from "../Layout";
import { eaALogoCreatorNetwork, Footer } from "@eait-playerexp-cn/core-ui-kit";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";

const supportedLanguages = {
  "en-us": { value: "en-us", label: "United States", icon: usa, locale: "en-us" },
  "ja-jp": { value: "ja-jp", label: "日本", icon: japan, locale: "ja-jp" },
  "de-de": { value: "de-de", label: "Deutschland", icon: germany, locale: "de-de" },
  "fr-fr": { value: "fr-fr", label: "France", icon: france, locale: "fr-fr" },
  "es-es": { value: "es-es", label: "Espana", icon: spain, locale: "es-es" },
  "it-it": { value: "it-it", label: "Italia", icon: italy, locale: "it-it" }
};

export type FooterProps = {
  locale: string;
  labels: FooterLabels;
  analytics: BrowserAnalytics;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
};

export default memo(function ProgramFooter({ locale, labels, analytics, FLAG_NEW_FOOTER_ENABLED }: FooterProps) {
  const router = useRouter();
  const { configuration: config } = useDependency();
  const programCode = config.PROGRAM_CODE;
  const countries = config.SUPPORTED_LOCALES.map((language) => supportedLanguages[language]);

  const navigateToMarketingPage = (url: string) => {
    analytics.clickedFooterLink({ locale, url });
    router.push(url);
  };
  const howItWorksLink = { label: labels.how, onClick: () => navigateToMarketingPage("/how-it-works") };
  const faqsLink = { label: labels.faqs, onClick: () => navigateToMarketingPage("/faq") };
  const policyLink = { label: labels.policy, onClick: () => navigateToMarketingPage("/trust-and-safety-guidelines") };
  const disclosureLink = { label: labels.disclosure, onClick: () => navigateToMarketingPage("/disclosure") };
  const legalLink = { label: labels.legal, href: "https://www.ea.com/legal-notices" };
  const disclaimerLink = { label: labels.disclaimer, href: "http://www.ea.com/1/product-eulas" };
  const updatesLink = { label: labels.updates, href: "http://www.ea.com/1/service-updates" };
  const termsLink = { label: labels.terms, href: "http://tos.ea.com/legalapp/WEBTERMS/US/en/PC/" };
  const privacyLink = { label: labels.privacy, href: "https://www.ea.com/privacy-policy" };
  const reportLink = {
    label: labels.report,
    href: `https://help.ea.com/${locale}/help-contact-us/?product=origin&topic=report-toxicity&category=report-concerns-or-harassment&subCategory=report-player`
  };
  const perksLink = { label: labels.perks, onClick: () => navigateToMarketingPage("/opportunities-rewards") };
  const footerLinksWeb = {
    internal: [howItWorksLink, perksLink, faqsLink, policyLink, disclosureLink],
    external: {
      sections: [
        [legalLink, disclaimerLink, updatesLink],
        [termsLink, privacyLink, reportLink]
      ]
    }
  };

  const footerLinksMobile = {
    internal: [howItWorksLink, faqsLink, policyLink, disclosureLink, perksLink],
    external: {
      sections: [[legalLink, disclaimerLink], [updatesLink], [termsLink], [privacyLink], [reportLink]]
    }
  };

  const updateLocale = useCallback(
    (selectedItem) => {
      const { locale = undefined } = selectedItem;
      const { pathname, asPath, query } = router;
      if (locale) {
        setCookie(locale);
        localStorage.setItem("locale", JSON.stringify(selectedItem));
        router.push({ pathname, query }, asPath, { locale });
      }
    },
    [router]
  );

  return FLAG_NEW_FOOTER_ENABLED ? (
    <Footer
      countriesDropdown={{
        countries,
        onChange: updateLocale,
        defaultLocale: locale
      }}
      programIcon={eaALogoCreatorNetwork}
      footerLinksWeb={footerLinksWeb}
      footerLinksMobile={footerLinksMobile}
      labels={{
        rights: labels.rights,
        year: `${LocalizedDate.year()}`
      }}
      programCode={programCode}
    />
  ) : (
    <LegacyFooter locale={locale} labels={labels} analytics={analytics} />
  );
});
