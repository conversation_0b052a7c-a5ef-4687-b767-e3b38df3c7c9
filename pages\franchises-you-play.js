import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import labelsBreadCrumb from "../config/translations/breadcrumb";
import labelsCommon from "../config/translations/common";
import labelsFranchisesYouPlay from "../config/translations/franchises-you-play";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import MigrationLayout from "../components/MigrationLayout";
import Form from "../components/Form";
import Footer from "../components/migrations/Footer";
import PrimaryFranchise from "../components/migrations/PrimaryFranchise";
import CreatorsService from "../src/api/services/CreatorsService";
import { useRouter } from "next/router";
import { useAppContext } from "../src/context";
import Error from "./_error";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  onToastClose,
  SESSION_USER,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import withUnregisteredUser from "../src/utils/WithUnregisteredUser";
import Loading from "../components/Loading";
import { Toast, useToast } from "../components/toast";
import RedirectException from "../src/utils/RedirectException";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import MigrationModal from "../components/migrations/MigrationModal";
import { useFormContext } from "react-hook-form";
import labelsOpportunities from "../config/translations/opportunities";
import CancelRegistrationModal from "../components/pages/interested-creators/CancelRegistrationModal";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import franchisesYouPlayProps from "../src/serverprops/FranchisesYouPlayProps";
import verifyIncompleteRegistration from "../src/serverprops/middleware/VerifyIncompleteRegistration";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const FranchisesForm = ({
  franchises,
  creator,
  franchisesYouPlayLabels,
  setShowMigration,
  showMigration,
  layout,
  onClose,
  isPending,
  submitHandle,
  stableDispatch,
  router,
  navigateToPage
}) => {
  const { getValues, formState } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const {
    state: { userNavigated },
    dispatch
  } = useAppContext();

  const onSave = () => submitHandle(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    navigateToPage ? router.push(navigateToPage) : router.push("/onboarding/information");
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      navigateToPage ? router.push(navigateToPage) : router.push("/onboarding/information");
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  return (
    <>
      <div className="franchises-you-play-form">
        <div className="franchises-you-play">
          <PrimaryFranchise {...{ franchises: franchises, creator, franchisesYouPlayLabels }} />
        </div>
      </div>
      <Footer {...{ buttons: layout.buttons, onCancel: onClose, disableSubmit: isPending, isPending }} />
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard }} />
    </>
  );
};

export default function FranchisesYouPlay({ user }) {
  const {
    analytics,
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(
    () => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE),
    [creatorsClient, DEFAULT_AVATAR_IMAGE]
  );
  const {
    dispatch,
    state: { exceptionCode = null, sessionUser = null, isValidationError, isError, onboardingSteps } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const router = useRouter();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [creator, setCreator] = useState(null);
  const [franchises, setFranchises] = useState([]);
  const { t } = useTranslation(["common", "breadcrumb", "franchises-you-play", "opportunities"]);
  const { layout, franchisesYouPlayLabels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      franchisesYouPlayLabels: labelsFranchisesYouPlay(t)
    };
  }, [t]);
  const {
    main: { unhandledError }
  } = layout;
  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = franchisesYouPlayLabels;
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");

  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  const submitHandle = useCallback(
    async (data, navigateBack, navigateToPage) => {
      stableDispatch({ type: USER_NAVIGATED, data: false });
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      // Creators BFF PUT
      try {
        const primaryFranchiseValue = { id: data.primaryFranchise.value, type: "PRIMARY" };
        const values = data.secondaryFranchise.map(
          (secondaryFranchise) =>
            secondaryFranchise.value && {
              id: secondaryFranchise.value,
              type: "SECONDARY"
            }
        );
        values.push(primaryFranchiseValue);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              preferredFranchises: values,
              program: { code: PROGRAM_CODE }
            })
          : await CreatorsService.update({ franchisesYouPlay: data });
        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        if (FLAG_PER_PROGRAM_PROFILE) {
          creator.preferredPrimaryFranchise = data.primaryFranchise;
        } else {
          creator.preferredPrimaryFranchises = data.primaryFranchise;
        }
        creator.preferredSecondaryFranchises = data.secondaryFranchise;
        analytics.confirmedFranchise({ locale: router.locale, creator });
        navigateToPage
          ? router.push(navigateToPage)
          : navigateBack
          ? router.push("/onboarding/information")
          : router.push("/creator-type");
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, creator, router, onboardingSteps]
  );

  const { pending: isPending, execute: submitHandleClb } = useAsync(submitHandle, false);

  useEffect(() => {
    async function fetchData() {
      try {
        // Creators BFF GET
        const creator = FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.getCreator(PROGRAM_CODE)
          : (await CreatorsService.getCreatorWithPayableStatus()).data;
        if (FLAG_PER_PROGRAM_PROFILE) {
          if (creator.preferredPrimaryFranchise) {
            creator.preferredPrimaryFranchise = {
              value: creator.preferredPrimaryFranchise.id,
              label: creator.preferredPrimaryFranchise.name
            };
          }
          creator.preferredSecondaryFranchises = creator.preferredSecondaryFranchises.map((platform) => {
            return {
              ...platform,
              value: platform.id,
              label: platform.name
            };
          });
        }
        setCreator(creator);
        const franchises = await metadataService.getFranchises();
        franchises && setFranchises(franchises);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => user && dispatch({ type: SESSION_USER, data: user }), [user]);
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <MigrationLayout
      pageTitle={franchisesYouPlayLabels.title}
      {...{
        ...layout,
        onClose,
        isRegistrationFlow: true,
        isOnboardingFlow: true,
        stableDispatch,
        onGoBack,
        setShowMigration,
        setNavigateToPage,
        completed: layout.completed
      }}
    >
      <div className="mg-franchises-you-play">
        <h3 className="mg-franchises-you-play-title">{franchisesYouPlayLabels.title}</h3>
        <div className="mg-franchises-you-play-description">{franchisesYouPlayLabels.description}</div>
      </div>
      {franchises && creator && (
        <Form mode="onChange" onSubmit={submitHandleClb}>
          <FranchisesForm
            {...{
              franchises,
              creator,
              franchisesYouPlayLabels,
              setShowMigration,
              showMigration,
              layout,
              onClose,
              isPending,
              submitHandle,
              stableDispatch,
              router,
              navigateToPage
            }}
          />
        </Form>
      )}
      {!creator && (
        <div className="loader">
          <Loading />
        </div>
      )}
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyIncompleteRegistration)
      .use(addLocaleCookie(locale))
      .get(franchisesYouPlayProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  delete req.session.futureCreator;
  await req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      ...(await serverSideTranslations(locale, ["common", "breadcrumb", "franchises-you-play", "opportunities"]))
    }
  };
};
