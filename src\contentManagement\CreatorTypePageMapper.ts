import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type CreatorTypePageLabels = {
  creatorTypePageLabels: {
    title: string;
    infoTitle: string;
    modalContent: string;
    interestedCreatorTitle: string;
    interestedCreatorDescription: string;
    creatorTypePageTitle: string;
    messages: {
      creatorTypes: string;
    };
    success: {
      updatedInformationHeader: string;
      creatorType: string;
    };
    labels: {
      youtuber: string;
      vlogger: string;
      photographer: string;
      designer_artist: string;
      blogger: string;
      live_streamer: string;
      podcaster: string;
      cosplayer: string;
      animator: string;
      screenshoter: string;
      lifestyle: string;
      other: string;
    };
  };
};

export class CreatorTypePageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): CreatorTypePageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      creatorTypePageLabels: {
        title: microCopy.get("creatorType.title"),
        infoTitle: microCopy.get("creatorType.infoTitle"),
        modalContent: microCopy.get("creatorType.modalContent"),
        creatorTypePageTitle: microCopy.get("creatorType.creatorTypePageTitle"),
        interestedCreatorTitle: microCopy.get("creatorType.interestedCreatorTitle"),
        interestedCreatorDescription: microCopy.get("creatorType.interestedCreatorDescription"),
        messages: {
          creatorTypes: microCopy.get("creatorType.messages.creatorTypes")
        },
        success: {
          updatedInformationHeader: microCopy.get("creatorType.success.updatedInformationHeader"),
          creatorType: microCopy.get("creatorType.success.creatorType")
        },
        labels: {
          youtuber: microCopy.get("creatorType.labels.youtuber"),
          vlogger: microCopy.get("creatorType.labels.vlogger"),
          photographer: microCopy.get("creatorType.labels.photographer"),
          designer_artist: microCopy.get("creatorType.labels.designer_artist"),
          blogger: microCopy.get("creatorType.labels.blogger"),
          live_streamer: microCopy.get("creatorType.labels.liveStreamer"),
          podcaster: microCopy.get("creatorType.labels.podcaster"),
          cosplayer: microCopy.get("creatorType.labels.cosplayer"),
          animator: microCopy.get("creatorType.labels.animator"),
          screenshoter: microCopy.get("creatorType.labels.screenshoter"),
          lifestyle: microCopy.get("creatorType.labels.lifestyle"),
          other: microCopy.get("creatorType.labels.other")
        }
      }
    };
  }
}
