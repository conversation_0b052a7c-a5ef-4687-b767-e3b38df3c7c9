import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import InterestedCreatorsFranchisesYouPlayPage from "@components/pages/interested-creators/InterestedCreatorsFranchisesYouPlayPage";
import { anInterestedCreator } from "../../../factories/interestedCreators/InterestedCreator";
import { useRouter } from "next/router";
import { franchisesYouPlayFormLabels, franchisesYouPlayLabels } from "../../../translations/utils";
import { axe } from "jest-axe";
import userEvent from "@testing-library/user-event";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import config from "../../../../config";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { useDependency } from "@src/context/DependencyContext";
import { aFranchise } from "@eait-playerexp-cn/metadata-test-fixtures";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../../src/context/DependencyContext");

describe("InterestedCreatorsFranchisesYouPlayPage", () => {
  const locale = "en-us";
  (useRouter as jest.Mock).mockImplementation(() => ({ push: jest.fn(), locale }));
  const interestedCreatorsFranchisesYouPlayPageProps = {
    interestedCreator: anInterestedCreator({
      nucleusId: 123,
      defaultGamerTag: "121",
      preferredFranchises: [{ id: "Fifa", type: "PRIMARY" }]
    }) as unknown as InterestedCreator,
    franchisesYouPlayLabels,
    franchisesYouPlayFormLabels,
    stableDispatch: jest.fn(),
    showConfirmation: false,
    setShowConfirmation: jest.fn(),
    onClose: jest.fn(),
    isError: false,
    isValidationError: false,
    errorToast: jest.fn(),
    unhandledError: "",
    router: useRouter(),
    locale,
    analytics: {} as unknown as BrowserAnalytics,
    SUPPORTED_LOCALES: config.SUPPORTED_LOCALES,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ metadataClient: {} });
    (MetadataService as jest.Mock).mockReturnValue({
      getFranchises: jest
        .fn()
        .mockResolvedValue([aFranchise({ value: "Apex", label: "Apex" }), aFranchise({ value: "Fifa", label: "Fifa" })])
    });
  });

  it("shows labels and placeholders for new applicants", async () => {
    render(<InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} />);

    expect(await screen.findByText("Franchise you play the most")).toBeInTheDocument();
    expect(
      await screen.findByText("Select the one that you enjoy the most or spend the most time in.")
    ).toBeInTheDocument();
    await waitFor(() => {
      const searchBox = screen.getByRole("textbox");
      expect(searchBox).toHaveValue("Fifa");
      expect(searchBox).toHaveAttribute("placeholder", "Select a Franchise");
    });
  });

  it("logs 'Cancelled Creator Application' on clicking Yes in modal footer", async () => {
    const analytics = { cancelledCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(
      <InterestedCreatorsFranchisesYouPlayPage
        {...interestedCreatorsFranchisesYouPlayPageProps}
        showConfirmation
        analytics={analytics}
      />
    );

    await userEvent.click(await screen.findByRole("button", { name: "Yes" }));

    await waitFor(() => {
      expect(analytics.cancelledCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.cancelledCreatorApplication).toHaveBeenCalledWith({ locale: "en-us", page: "/" });
    });
  });

  it("is accessible", async () => {
    const { container } = render(
      <InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} />
    );
    await screen.findByRole("textbox");

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
