import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import { createRouter } from "next-connect";
import ViewConnectedAccountsController from "../../src/controllers/ViewConnectedAccountsController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewConnectedAccountsController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
