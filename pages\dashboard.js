import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { Fragment, memo, useCallback, useEffect, useMemo, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsDashboard from "../config/translations/dashboard";
import { useRouter } from "next/router";
import classNames from "classnames/bind";
import Pagination from "../components/dashboard/Pagination";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import Link from "next/link";
import SubmittedContentService from "../src/api/services/SubmittedContentService";
import OpportunityService from "../src/api/services/OpportunityService";
import labelsOpportunities from "../config/translations/opportunities";
import { useAppContext } from "../src/context";
import ApiContainer from "@src/ApiContainer";
import {
  ACTIVE_CONTENT_SUBMISSION,
  ACTIVE_GAME_CODE,
  CREATOR_CODE_DETAILS,
  ERROR,
  GET_PLATFORMS,
  LOADING,
  onToastClose,
  SESSION_USER,
  toastContent,
  useIsMounted,
  VALIDATION_ERROR
} from "../utils";
import Error from "./_error";
import withRegisteredUser from "../src/utils/WithRegisteredUser";
import Loading from "../components/Loading";
import { Toast, useToast } from "../components/toast";
import withTermsAndConditionsUpToDate from "../src/utils/WithTermsAndConditionsUpToDate";
import { OpportunityCardV2 } from "@eait-playerexp-cn/core-ui-kit";
import CreatorDisplayName from "../components/CreatorDisplayName";
import { ContentCard } from "@eait-playerexp-cn/core-ui-kit";
import labelNotifications from "../config/translations/notifications";
import { useDetectScreen } from "../utils";
import RedirectException from "../src/utils/RedirectException";
import labelsMyContent from "../config/translations/my-content";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import {
  COMPLETED as COMPLETED_STATUS,
  INVITED as INVITED_STATUS,
  JOINED as JOINED_STATUS,
  PAST as PAST_STATUS
} from "../utils/constants";
import MorePerksModal from "../components/pages/MorePerksModal";
import SupportACreatorModal from "../components/pages/SupportACreatorModal";
import GameCodeModal from "../components/pages/GameCodeModal";
import OperationsService from "../src/api/services/OperationsService";
import EventDetailsModal from "../components/pages/EventDetailsModal";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import Footer from "../components/footer/ProgramFooter";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import flags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../src/serverprops/middleware/SaveInitialPage";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import verifyAccessToProgram from "../src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import dashboardProps from "../src/serverprops/DashboardProps";
import dynamic from "next/dynamic";
import ContentManagementService from "../src/api/services/ContentManagementService";

const JOINED = "Joined";
const PAST = "Past";
const INVITED = "Invited";
const PAGE_SIZE = 5;
const PAGE = 1;
const SUBMITTED_CONTENT_PAGE_SIZE = 3;
const DEFAULT_PAGE_NUMBER = 1;
let activeGameCodes;

export const getPlatforms = async (metadataService, stableDispatch, errorHandler) => {
  try {
    return await metadataService.getPlatformsMatching({ type: "ALL" });
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const getGameCodes = async (participationIds, stableDispatch, errorHandler) => {
  try {
    const response = await OperationsService.viewAssignedGameCodes(participationIds);
    return response.data;
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const mapGameCodeToOpportunity = async (mappingInformation, errorHandler) => {
  const {
    opportunities,
    participationIds,
    platforms,
    dispatch: stableDispatch,
    storedActiveGameCode
  } = mappingInformation;
  activeGameCodes = storedActiveGameCode;
  const gameCodes = await getGameCodes(participationIds, stableDispatch, errorHandler);
  opportunities?.forEach((opportunity) => {
    gameCodes?.forEach(({ participationId, gameCode, platformId }) => {
      if (opportunity.participationId === participationId) {
        const selectedPlatform = platforms?.find((platform) => platform.value === platformId);
        if (selectedPlatform) {
          opportunity.gameCode = {
            platform: selectedPlatform.label.toUpperCase(),
            code: gameCode?.code,
            status: gameCode?.status
          };
        }
        activeGameCodes = {
          ...activeGameCodes,
          [opportunity.id]: opportunity?.gameCode?.status === "ASSIGNED"
        };
      }
    });
  });
  stableDispatch({ type: ACTIVE_GAME_CODE, data: activeGameCodes });
  return opportunities;
};

export const claimGameCode = async (participationId, stableDispatch, errorHandler) => {
  try {
    await OperationsService.claimGameCode(participationId);
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const getActiveStatus = (opportunity, hasChangesRequested, hasNotSubmittedContent) => {
  return (
    hasChangesRequested ||
    (!hasChangesRequested && hasNotSubmittedContent && !opportunity?.contentSubmissionWindowHasEnded())
  );
};

export const formatEventAddress = (opportunity, opportunitiesLabels) => {
  const eventAddress = [
    opportunity.eventVenue(),
    opportunity.eventStreetAddress(),
    opportunity.eventAddress(),
    opportunity.eventCountryName()
  ]
    .filter((address) => address)
    .join(", ");
  return {
    label: opportunitiesLabels.eventDetails.eventLocation,
    value: eventAddress
  };
};

export const formatRemoteEvent = (opportunity, opportunitiesLabels) => {
  if (opportunity.event.meetingLink && opportunity.event.meetingPassword) {
    return {
      label: opportunitiesLabels.eventDetails.password,
      value: opportunity.event.meetingPassword
    };
  }
  if (!opportunity.event.meetingLink && !opportunity.event.meetingPassword) {
    return {
      label: opportunitiesLabels.eventDetails.info,
      value: opportunitiesLabels.remoteEvent.NoMeetingLinkInfo
    };
  }
};

export const formatEventWindow = (eventWindow, opportunitiesLabels) => {
  return [
    {
      label: opportunitiesLabels.eventDetails.eventStartTime,
      value: eventWindow.startDate
    },
    {
      label: opportunitiesLabels.eventDetails.eventEndTime,
      value: eventWindow.endDate
    }
  ];
};

const FeatureNotifications = dynamic(
  // @ts-ignore
  () => import("notifications/FeaturedNotifications"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

/**
 * A component for showing notifications, list of joined or invited or past opportunities and list of content submitted by current user
 *
 * @param {object} user - a current logged-in user details
 * @param {string} locale - a locale to translate the app
 * @param {BrowserAnalytics} analytics
 * @returns {JSX.Element}
 */
export default function Dashboard({
  user,
  locale,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_CONTENT_WITH_FINAL_REMARK,
  pageLabels
}) {
  const {
    analytics,
    metadataClient,
    notificationsClient,
    errorHandler,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      PROGRAM_CODE,
      DEFAULT_NOTIFICATION_PROGRAM,
      FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM,
      FLAG_SUBMITTED_CONTENT_WITH_PROGRAM
    }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const {
    dispatch,
    state: {
      exceptionCode = null,
      sessionUser = null,
      isLoading,
      isError,
      isValidationError,
      creatorCodeDetails,
      platformDetails,
      activeGameCode,
      activeContentSubmission
    } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const { t } = useTranslation(["common", "dashboard", "my-content", "connect-accounts", "opportunities"]);
  const { layout, dashboardLabels, opportunitiesLabels, notificationsLabels, notificationBellLabels, myContentLabels } =
    useMemo(() => {
      const { notificationsPageLabels } = pageLabels;
      const notificationBellLabels = mapNotificationsBellLabels(t);
      const labels = {
        dashboardLabels: labelsDashboard(t),
        layout: labelsCommon(t),
        opportunitiesLabels: labelsOpportunities(t),
        notificationsLabels: notificationsPageLabels,
        notificationBellLabels,
        myContentLabels: labelsMyContent(t)
      };
      labels.layout.contentCard.rejected = FLAG_CONTENT_WITH_FINAL_REMARK
        ? labels.layout.contentCard.notApproved
        : labels.layout.contentCard.rejected;
      labels.layout.footer = { locale: router.locale, labels: labels.layout.footer };
      return labels;
    }, [t]);
  const {
    main: { unhandledError },
    header,
    footer,
    buttons
  } = layout;
  const { error: errorToast } = useToast();
  const isMounted = useIsMounted();
  const [pages, setPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [pastPages, setPastPages] = useState(null);
  const [pastCurrentPage, setPastCurrentPage] = useState(0);
  const [invitedPages, setInvitedPages] = useState(null);
  const [invitedCurrentPage, setInvitedCurrentPage] = useState(0);
  const [showMyContentButton, setShowMyContentButton] = useState(false);
  const [submittedContent, setSubmittedContent] = useState(null);
  const [joinedTotal, setJoinedTotal] = useState(0);
  const [pastTotal, setPastTotal] = useState(0);
  const [invitedTotal, setInvitedTotal] = useState(0);
  const [joinedOpportunities, setJoinedOpportunities] = useState({});
  const [pastOpportunities, setPastOpportunities] = useState({});
  const [invitedOpportunities, setInvitedOpportunities] = useState({});
  const [currentPageJoinedOpportunities, setCurrentPageJoinedOpportunities] = useState([]);
  const [currentPagePastOpportunities, setCurrentPagePastOpportunities] = useState([]);
  const [currentPageInvitedOpportunities, setCurrentPageInvitedOpportunities] = useState([]);
  const [tabSelected, setTabSelected] = useState(INVITED);
  const [joinedOpportunitiesResponse, setJoinedOpportunitiesResponse] = useState(null);
  const [pastOpportunitiesResponse, setPastOpportunitiesResponse] = useState(null);
  const [invitedOpportunitiesResponse, setInvitedOpportunitiesResponse] = useState(null);
  const isMobile = useDetectScreen("767");
  const [contentsFeedback, setContentsFeedback] = useState(null);
  const [selectedContentId, setSelectedContentId] = useState("");
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [showMorePerksForInvited, setShowMorePerksForInvited] = useState(null);
  const [showMorePerksForPast, setShowMorePerksForPast] = useState(null);
  const [showSupportACreatorModal, setShowSupportACreatorModal] = useState(null);
  const [showEventDetailsModal, setShowEventDetailsModal] = useState(null);
  const [creatorCodeLoading, setCreatorCodeLoading] = useState(null);
  const [showGameCodeModal, setShowGameCodeModal] = useState(null);
  const [gameCodeLoading, setGameCodeLoading] = useState(null);
  let contentSubmissionActiveStatus = activeContentSubmission; // storing the data from context
  const statusLabel = {
    [PAST_STATUS]: dashboardLabels.past,
    [COMPLETED_STATUS]: opportunitiesLabels.completed
  };
  const headerLabels = { labels: { ...header, ...buttons } };
  const formatSubmittedContent = useCallback(
    (content) => {
      let formattedContent = content;
      formattedContent.contents.forEach((content) => (content.contentTypeLabel = myContentLabels[content.contentType]));
      setSubmittedContent(formattedContent);
    },
    [myContentLabels]
  );

  useEffect(() => {
    if (isMounted()) {
      dispatch({ type: LOADING, data: true });
      const getSubmittedContent = FLAG_CONTENT_WITH_FINAL_REMARK
        ? SubmittedContentService.getSubmittedContentsFinalRemarks
        : SubmittedContentService.getSubmittedContents;
      const getFeatureSubmittedContent = FLAG_SUBMITTED_CONTENT_WITH_PROGRAM
        ? SubmittedContentService.getSubmittedContentsFinalRemarksWithProgramCode
        : getSubmittedContent;

      getFeatureSubmittedContent(SUBMITTED_CONTENT_PAGE_SIZE, DEFAULT_PAGE_NUMBER)
        .then((res) => {
          setShowMyContentButton(res.data.total > SUBMITTED_CONTENT_PAGE_SIZE);
          formatSubmittedContent(res.data);
        })
        .catch((e) => {
          errorHandler(dispatch, e);
        })
        .finally(() => {
          dispatch({ type: LOADING, data: false });
        });
    }
  }, [isMounted]);

  useEffect(() => {
    if (submittedContent && currentPageJoinedOpportunities && currentPagePastOpportunities) {
      dispatch({ type: LOADING, data: false });
    }
  }, [currentPageJoinedOpportunities, currentPagePastOpportunities, submittedContent]);

  const pageCalculation = (total) => {
    let pages = [];
    for (let i = 0; i < Math.ceil(total / PAGE_SIZE); i++) {
      pages.push(i + 1);
    }
    return pages;
  };

  const updateOpportunitiesStatus = async (data) => {
    let opportunitiesIds = [];
    data.forEach((element) => {
      opportunitiesIds.push(element.id);
    });
    if (opportunitiesIds.length === 0) return data;
    try {
      const res = await OpportunityService.getParticipationStatusWithSubmissionInformation(opportunitiesIds);
      if (res?.data?.length === 0) {
        dispatch({ type: LOADING, data: false });
        return data;
      }
      data.map((opportunity, index) => {
        if (res.data[index].status === "JOINED") {
          opportunity.status = "JOINED";
          opportunity.isCompleted() && (opportunity.status = "COMPLETED");
          opportunity.participationId = res.data[index].participationId;
          opportunity.hasChangesRequested = res.data[index].hasChangesRequested;
          opportunity.hasNotSubmittedContent = res.data[index].hasNotSubmittedContent;
          contentSubmissionActiveStatus = {
            ...contentSubmissionActiveStatus,
            [opportunity.id]: getActiveStatus(
              opportunity,
              opportunity.hasChangesRequested,
              opportunity.hasNotSubmittedContent
            )
          };
        } else if (res.data[index].status === INVITED_STATUS) {
          opportunity.status = opportunity.isPastOpportunity() ? PAST_STATUS : INVITED_STATUS;
        }
      });
      dispatch({ type: ACTIVE_CONTENT_SUBMISSION, data: contentSubmissionActiveStatus });
      dispatch({ type: LOADING, data: false });
    } catch (e) {
      errorHandler(dispatch, e);
    }
    return data;
  };

  const getMetaInformationForGameCodeOpportunities = (opportunities, platforms) => {
    return {
      opportunities,
      participationIds: opportunities
        .filter((opportunity) => opportunity.hasGameCodes && opportunity.participationId)
        .map((opportunity) => opportunity.participationId),
      platforms,
      dispatch,
      storedActiveGameCode: activeGameCode
    };
  };

  useEffect(() => {
    async function getOpportunityPages() {
      const platforms = await getPlatforms(metadataService, stableDispatch, errorHandler);
      const pastCurrentPage = PAGE;
      const pastCriteria = {
        page: pastCurrentPage,
        size: PAGE_SIZE,
        status: PAST_STATUS
      };

      // Past Opportunities
      OpportunityService.matchingWithEventDetails(pastCriteria)
        .then(async (res) => {
          setPastOpportunitiesResponse(res.data);
          if (res.data.total > 0) {
            let opportunities = res.data.opportunities;
            opportunities = await updateOpportunitiesStatus(opportunities);
            const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platforms);
            const { participationIds } = mappingInformation;
            if (participationIds.length > 0) {
              opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
            }
            setCurrentPagePastOpportunities(opportunities);
            setPastOpportunities({ ...pastOpportunities, [pastCurrentPage]: opportunities });
            setPastPages(pageCalculation(res.data.total));
            setPastTotal(res.data.total);
            setPastCurrentPage(pastCurrentPage);
            dispatch({ type: GET_PLATFORMS, data: platforms });
          }
        })
        .catch((e) => errorHandler(dispatch, e));

      const currentPage = PAGE;
      const joinedCriteria = {
        page: currentPage,
        size: PAGE_SIZE,
        status: "JOINED"
      };

      // Joined Opportunities
      OpportunityService.matchingWithEventDetails(joinedCriteria)
        .then(async (opportunitiesRespons) => {
          setJoinedOpportunitiesResponse(opportunitiesRespons.data);
          if (opportunitiesRespons.data.total > 0) {
            let opportunities = opportunitiesRespons.data.opportunities;
            opportunities = await updateOpportunitiesStatus(opportunities);
            const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platforms);
            const { participationIds } = mappingInformation;
            if (participationIds.length > 0) {
              opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
            }
            setCurrentPageJoinedOpportunities(opportunities);
            setJoinedOpportunities({ ...joinedOpportunities, [currentPage]: opportunities });
            setPages(pageCalculation(opportunitiesRespons.data.total));
            setJoinedTotal(opportunitiesRespons.data.total);
            setCurrentPage(currentPage);
            dispatch({ type: GET_PLATFORMS, data: platforms });
          }
        })
        .catch((e) => errorHandler(dispatch, e));

      const invitedCurrentPage = PAGE;
      const invitedCriteria = {
        page: invitedCurrentPage,
        size: PAGE_SIZE,
        status: INVITED_STATUS
      };

      OpportunityService.matchingWithEventDetails(invitedCriteria)
        .then((res) => {
          setInvitedOpportunitiesResponse(res.data);
          if (res.data.total > 0) {
            let data = res.data.opportunities;
            setCurrentPageInvitedOpportunities(data);
            setInvitedOpportunities({ ...pastOpportunities, [invitedCurrentPage]: data });
            setInvitedPages(pageCalculation(res.data.total));
            setInvitedTotal(res.data.total);
            setInvitedCurrentPage(invitedCurrentPage);
          }
        })
        .catch((e) => errorHandler(dispatch, e));
    }
    getOpportunityPages();
  }, []);

  useEffect(() => {
    const criteria = {
      page: currentPage,
      size: PAGE_SIZE,
      status: "JOINED"
    };

    if (currentPage > 0) {
      if (joinedOpportunities && joinedOpportunities[currentPage] && joinedOpportunities[currentPage].length > 0) {
        setCurrentPageJoinedOpportunities(joinedOpportunities[currentPage]);
      } else {
        // Joined Opportunities
        OpportunityService.matchingWithEventDetails(criteria)
          .then(async (res) => {
            if (res.data.total > 0) {
              let opportunities = res.data.opportunities;
              opportunities = await updateOpportunitiesStatus(opportunities);
              const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platformDetails);
              const { participationIds } = mappingInformation;
              if (participationIds.length > 0) {
                opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
              }
              setJoinedOpportunities({ ...joinedOpportunities, [currentPage]: opportunities });
              setCurrentPageJoinedOpportunities(opportunities);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [currentPage]);

  useEffect(() => {
    const criteria = {
      page: pastCurrentPage,
      size: PAGE_SIZE,
      status: PAST_STATUS
    };
    if (pastCurrentPage > 0) {
      if (pastOpportunities && pastOpportunities[pastCurrentPage] && pastOpportunities[pastCurrentPage].length > 0) {
        setCurrentPagePastOpportunities(pastOpportunities[pastCurrentPage]);
      } else {
        // Past Opportunities
        OpportunityService.matchingWithEventDetails(criteria)
          .then(async (res) => {
            if (res.data.total > 0) {
              let opportunities = res.data.opportunities;
              opportunities = await updateOpportunitiesStatus(opportunities);
              const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platformDetails);
              const { participationIds } = mappingInformation;
              if (participationIds.length > 0) {
                opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
              }
              setPastOpportunities({ ...pastOpportunities, [pastCurrentPage]: opportunities });
              setCurrentPagePastOpportunities(opportunities);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [pastCurrentPage]);

  useEffect(() => {
    const criteria = {
      page: invitedCurrentPage,
      size: PAGE_SIZE,
      status: INVITED_STATUS
    };

    if (invitedCurrentPage > 0) {
      if (
        invitedOpportunities &&
        invitedOpportunities[invitedCurrentPage] &&
        invitedOpportunities[invitedCurrentPage].length > 0
      ) {
        setCurrentPageInvitedOpportunities(invitedOpportunities[invitedCurrentPage]);
      } else {
        // Invited Opportunities
        OpportunityService.matchingWithEventDetails(criteria)
          .then((res) => {
            if (res.data.total > 0) {
              const data = res.data.opportunities;
              setInvitedOpportunities({ ...invitedOpportunities, [invitedCurrentPage]: data });
              setCurrentPageInvitedOpportunities(data);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [invitedCurrentPage]);

  useEffect(() => user && dispatch({ type: SESSION_USER, data: user }), [user]);
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, dispatch)
        }
      );
    }
  }, [isError, isValidationError, unhandledError]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const noOpportunities = (noOpportunitiesTitle, noOpportunitiesDesc) => {
    return (
      <div className="dashboard-opportunities-no-content">
        <div className="dashboard-opportunities-no-content-row1">
          <img src="/img/dashboard/console.png" alt="icon" className="dashboard-opportunities-icon" />
          <div>
            <div className="dashboard-opportunities-no-content-title">{noOpportunitiesTitle}</div>
            <div className="dashboard-opportunities-no-content-desc">{noOpportunitiesDesc}</div>
          </div>
        </div>
        <Link href="/opportunities" className="btn btn-primary btn-md">
          {dashboardLabels.viewAllOpportunities}
        </Link>
      </div>
    );
  };

  const opportunityClickHandler = (opportunityId) => {
    router.push(`/opportunities/${opportunityId}`);
  };

  const onToggleFeedback = async (collapsed, contentId, status, cardType) => {
    setSelectedContentId(contentId);
    if (collapsed == false) return;
    if (status === "REJECTED" || status === "APPROVED") return;
    const currentFeedback = contentsFeedback?.[contentId]
      ? contentsFeedback?.[contentId]
      : await getFeedbacks({ contentId }, cardType);
    setContentsFeedback({ ...contentsFeedback, [contentId]: currentFeedback });
    setFeedbackLoading(false);
  };
  const getFeedbacks = async (criteria, type) => {
    setFeedbackLoading(true);
    try {
      const response = await SubmittedContentService.getContentsFeedback(criteria);
      return response.data.contentsFeedback.slice(0, 1).map((feedback) => ({
        ...feedback,
        ...layout.contentCard,
        sentOnLabel: layout.contentCard.sentOn,
        fromLabel: layout.contentCard.from,
        title: layout.contentCard.changesRequired,
        contentVersion: feedback.contentVersion,
        lastUpdateDate: `${feedback.formattedSubmittedDate(locale)}`,
        content: feedback.description,
        note: t("common:additionalDescription", {
          ...{
            contentType: type === "FILE" ? type.toLowerCase() : layout.contentCard.url?.toLowerCase(),
            updateType:
              type === "FILE" ? layout.buttons.upload?.toLowerCase() : layout.contentCard.update?.toLowerCase()
          }
        })
      }));
    } catch (e) {
      setFeedbackLoading(false);
      errorHandler(stableDispatch, e);
    }
  };

  const modalLabels = {
    title: dashboardLabels.allPerks,
    close: layout.buttons.close
  };

  const handleContentSubmission = (opportunitiesId, hasDeliverables) => {
    router.push(
      `/opportunities/${opportunitiesId}?tab=${hasDeliverables ? "content-deliverables" : "content-submission"}`
    );
  };

  const handleCreatorCode = async (opportunityId) => {
    if (creatorCodeDetails?.[opportunityId]) {
      setShowSupportACreatorModal({ [opportunityId]: true });
      return;
    }
    setCreatorCodeLoading({ [opportunityId]: true });
    try {
      const response = await OpportunityService.getParticipationDetails(opportunityId);
      const opportunityCreatorCodeDetails = response.data.find((opportunity) => opportunity);
      dispatch({ type: CREATOR_CODE_DETAILS, data: { [opportunityId]: opportunityCreatorCodeDetails } });
      setCreatorCodeLoading({ [opportunityId]: false });
      setShowSupportACreatorModal({ [opportunityId]: true });
    } catch (e) {
      setCreatorCodeLoading({ [opportunityId]: false });
      errorHandler(dispatch, e);
    }
  };

  const handleEventDetails = (opportunityId) => {
    [...currentPageJoinedOpportunities, ...currentPagePastOpportunities].find(
      (opportunity) => opportunity.id === opportunityId
    );
    setShowEventDetailsModal({ [opportunityId]: true });
  };
  const handleGameCode = async (opportunityId, participationId, hasGameCodesAssigned) => {
    if (hasGameCodesAssigned) {
      setGameCodeLoading({ [opportunityId]: true });
      await claimGameCode(participationId, stableDispatch, errorHandler);
      setGameCodeLoading({ [opportunityId]: false });
      dispatch({ type: ACTIVE_GAME_CODE, data: { ...activeGameCode, [opportunityId]: false } });
    }
    setShowGameCodeModal({ [opportunityId]: true });
  };

  const navigationOptionHandlers = {
    handleContentSubmission,
    handleCreatorCode,
    handleGameCode,
    handleEventDetails
  };

  const activeStatusHandlers = {
    handleGameCode: (opportunityId) => activeGameCode?.[opportunityId] ?? false,
    handleContentSubmission: (opportunityId) => activeContentSubmission?.[opportunityId] ?? false
  };

  const loadingStatusHandlers = {
    handleCreatorCode: (opportunityId) => creatorCodeLoading?.[opportunityId] ?? false,
    handleGameCode: (opportunityId) => gameCodeLoading?.[opportunityId] ?? false
  };

  const formatCodeActivationWindow = (codeWindow) => {
    return [
      {
        label: opportunitiesLabels.creatorCode.codeActivationStartTime,
        value: codeWindow.startDate
      },
      {
        label: opportunitiesLabels.creatorCode.codeActivationEndTime,
        value: codeWindow.endDate
      }
    ];
  };
  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.dashboard}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationBellLabels}
          analytics={analytics}
          interestedCreator={null}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user} className="dashboard-container">
        <div className="dashboard-full-screen">
          <div className="dashboard-title-container">
            <CreatorDisplayName labels={dashboardLabels} user={user} tooltip={layout?.toolTip?.badge} />
          </div>
          {(isLoading && <Loading />) || (
            <>
              <div className="dashboard-notification-container">
                <FeatureNotifications
                  labels={notificationsLabels}
                  locale={router.locale}
                  configuration={{
                    client: notificationsClient,
                    programHosts: NOTIFICATION_BASE_URLS,
                    program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
                    defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
                  }}
                />
              </div>
              <div className="dashboard-sub-title">{dashboardLabels.myOpportunities}</div>
              <div className="dashboard-opportunities-container">
                <div className="dashboard-opportunities-tabs-container">
                  <span
                    className={classNames(
                      {
                        "dashboard-opportunities-tabs-item-selected": tabSelected === INVITED
                      },
                      "dashboard-opportunities-tabs-item"
                    )}
                    onClick={() => {
                      setTabSelected(INVITED);
                      setCurrentPage(PAGE);
                      setPastCurrentPage(PAGE);
                    }}
                  >
                    {dashboardLabels.invited} ({invitedTotal})
                  </span>
                  <span
                    className={classNames(
                      {
                        "dashboard-opportunities-tabs-item-selected": tabSelected === JOINED
                      },
                      "dashboard-opportunities-tabs-item"
                    )}
                    onClick={() => {
                      setTabSelected(JOINED);
                      setPastCurrentPage(PAGE);
                      setInvitedCurrentPage(PAGE);
                    }}
                    data-testid="dashboard-opportunities-joined-tab"
                  >
                    {dashboardLabels.joined} ({joinedTotal})
                  </span>
                  <span
                    className={classNames(
                      {
                        "dashboard-opportunities-tabs-item-selected": tabSelected === PAST
                      },
                      "dashboard-opportunities-tabs-item"
                    )}
                    onClick={() => {
                      setTabSelected(PAST);
                      setCurrentPage(PAGE);
                      setInvitedCurrentPage(PAGE);
                    }}
                    data-testid="dashboard-opportunities-past-tab"
                  >
                    {dashboardLabels.past} ({pastTotal})
                  </span>
                </div>

                <div className={"dashboard-opportunities-list-with-perks"}>
                  {tabSelected === INVITED &&
                    (invitedTotal === 0 ? (
                      <>
                        {noOpportunities(
                          dashboardLabels.noInvitedOpportunities,
                          dashboardLabels.noInvitedOpportunitiesDesc
                        )}
                      </>
                    ) : (
                      <>
                        {(currentPageInvitedOpportunities || []).map((opportunity, index) => (
                          <Fragment key={`opportunity-card-${index}`}>
                            <OpportunityCardV2
                              title={opportunity.title}
                              description={opportunity.description}
                              opportunityId={opportunity.id}
                              heroImage={opportunity.heroImage}
                              perksTitle={opportunitiesLabels.perks}
                              perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                              settings={opportunity.settings(opportunitiesLabels)}
                              href={`/opportunities/${opportunity.id}`}
                              morePerksLabel={opportunitiesLabels.more}
                              pillStatus={{ status: INVITED_STATUS, label: dashboardLabels.invited }}
                              handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                              details={opportunity.settingDetails(opportunitiesLabels, router.locale, INVITED_STATUS)}
                              handleMoreButtonClick={() => setShowMorePerksForInvited({ [index]: true })}
                            />
                            {showMorePerksForInvited?.[index] && (
                              <MorePerksModal
                                labels={modalLabels}
                                onClose={() => setShowMorePerksForInvited({ [index]: false })}
                                perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                              />
                            )}
                          </Fragment>
                        ))}
                        {invitedPages && invitedPages.length > 1 && (
                          <div className="dashboard-pagination-container">
                            <Pagination
                              next={layout.buttons.next}
                              prev={layout.buttons.prev}
                              pages={invitedPages}
                              currentPage={invitedCurrentPage}
                              onPageChange={(page) => setInvitedCurrentPage(page)}
                            />
                          </div>
                        )}
                      </>
                    ))}
                  {tabSelected === JOINED &&
                    (joinedTotal === 0 ? (
                      <>
                        {noOpportunities(
                          dashboardLabels.noJoinedOpportunities,
                          dashboardLabels.noJoinedOpportunitiesDesc
                        )}
                      </>
                    ) : (
                      <>
                        {(currentPageJoinedOpportunities || []).map((opportunity, index) => (
                          <Fragment key={`opportunity-card-${index}`}>
                            <OpportunityCardV2
                              title={opportunity.title}
                              opportunityId={opportunity.id}
                              heroImage={opportunity.heroImage}
                              perksTitle={opportunitiesLabels.perks}
                              perks={[]}
                              settings={opportunity.settings(opportunitiesLabels)}
                              settingsAction={opportunity.settingActions({
                                opportunitiesLabels,
                                status: JOINED_STATUS,
                                navigationOptionHandlers,
                                loadingStatusHandlers,
                                activeStatusHandlers,
                                platform: opportunity?.gameCode?.platform,
                                participationId: opportunity?.participationId,
                                hasGameCodesAssigned: opportunity?.gameCode?.status === "ASSIGNED"
                              })}
                              href={`/opportunities/${opportunity.id}`}
                              morePerksLabel={opportunitiesLabels.more}
                              pillStatus={{ status: JOINED_STATUS, label: dashboardLabels.joined }}
                              handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                              details={opportunity.settingDetails(opportunitiesLabels, router.locale, JOINED_STATUS)}
                            />
                            {showSupportACreatorModal?.[opportunity.id] && (
                              <SupportACreatorModal
                                labels={{
                                  title: opportunitiesLabels.creatorCode.title,
                                  close: modalLabels.close,
                                  copied: opportunitiesLabels.copied,
                                  copyText: opportunitiesLabels.creatorCode.copyText
                                }}
                                onClose={() => setShowSupportACreatorModal({ [opportunity.id]: false })}
                                codeWindow={formatCodeActivationWindow(opportunity.activationWindow(locale))}
                                gameTitle={opportunity.gameTitle}
                                code={creatorCodeDetails?.[opportunity.id]?.creatorCode?.code}
                              />
                            )}
                            {showEventDetailsModal?.[opportunity.id] && (
                              <EventDetailsModal
                                labels={{
                                  title: opportunity.isInPerson()
                                    ? opportunitiesLabels.inPersonEvent
                                    : opportunitiesLabels.remote,
                                  joinEvent: opportunitiesLabels.remoteEvent.joinEvent,
                                  close: modalLabels.close,
                                  copied: opportunitiesLabels.copied,
                                  copy: opportunity.isInPerson()
                                    ? opportunitiesLabels.eventDetails.copyAddress
                                    : opportunitiesLabels.eventDetails.copyPassword
                                }}
                                onClose={() => setShowEventDetailsModal({ [opportunity.id]: false })}
                                eventWindow={formatEventWindow(opportunity.eventWindow(locale), opportunitiesLabels)}
                                eventAddress={
                                  opportunity.isInPerson() && formatEventAddress(opportunity, opportunitiesLabels)
                                }
                                isInPerson={opportunity.isInPerson()}
                                eventPeriodEnded={
                                  opportunity?.eventPeriodHasEnded()
                                    ? {
                                        label: opportunitiesLabels.eventDetails.info,
                                        value: opportunitiesLabels.remoteEvent.description
                                      }
                                    : null
                                }
                                remoteEventDetails={
                                  !opportunity.isInPerson() && formatRemoteEvent(opportunity, opportunitiesLabels)
                                }
                                meetingLink={!opportunity.isInPerson() && opportunity.event.meetingLink}
                                isMeetingLinkDisabled={
                                  (!opportunity.event.meetingLink && !opportunity.event.meetingPassword) ||
                                  opportunity?.eventPeriodHasEnded()
                                }
                              />
                            )}
                            {showGameCodeModal?.[opportunity.id] && (
                              <GameCodeModal
                                labels={{
                                  title: opportunitiesLabels.getGameCode,
                                  close: modalLabels.close,
                                  copied: opportunitiesLabels.copied,
                                  copyText: opportunitiesLabels.getGameCode
                                }}
                                onClose={() => setShowGameCodeModal({ [opportunity.id]: false })}
                                platform={opportunity?.gameCode?.platform}
                                gameTitle={opportunity.gameTitle}
                                code={opportunity?.gameCode?.code || opportunitiesLabels.checkBackSoon}
                                hasCode={!!opportunity?.gameCode?.code}
                              />
                            )}
                          </Fragment>
                        ))}
                        {pages && pages.length > 1 && (
                          <div className="dashboard-pagination-container">
                            <Pagination
                              next={layout.buttons.next}
                              prev={layout.buttons.prev}
                              pages={pages}
                              currentPage={currentPage}
                              onPageChange={(page) => setCurrentPage(page)}
                            />
                          </div>
                        )}
                      </>
                    ))}
                  {tabSelected === PAST &&
                    (pastTotal === 0 ? (
                      <>
                        {noOpportunities(dashboardLabels.noPastOpportunities, dashboardLabels.noPastOpportunitiesDesc)}
                      </>
                    ) : (
                      <>
                        {(currentPagePastOpportunities || []).map((opportunity, index) => (
                          <Fragment key={`opportunity-card-${index}`}>
                            {[PAST_STATUS, COMPLETED_STATUS].includes(opportunity.status) && (
                              <>
                                <OpportunityCardV2
                                  title={opportunity.title}
                                  description={opportunity.description}
                                  opportunityId={opportunity.id}
                                  heroImage={opportunity.heroImage}
                                  perksTitle={opportunitiesLabels.perks}
                                  perks={
                                    opportunity.status === PAST_STATUS
                                      ? opportunity.formattedPerks(opportunitiesLabels.perksLabels)
                                      : []
                                  }
                                  settings={opportunity.settings(opportunitiesLabels)}
                                  settingsAction={
                                    opportunity.status === PAST_STATUS
                                      ? []
                                      : opportunity.settingActions({
                                          opportunitiesLabels,
                                          status: COMPLETED_STATUS,
                                          navigationOptionHandlers,
                                          platform: opportunity?.gameCode?.platform,
                                          activeStatusHandlers,
                                          participationId: opportunity?.participationId,
                                          hasGameCodesAssigned: opportunity?.gameCode?.status === "ASSIGNED"
                                        })
                                  }
                                  href={`/opportunities/${opportunity.id}`}
                                  morePerksLabel={opportunitiesLabels.more}
                                  pillStatus={{ status: opportunity.status, label: statusLabel[opportunity.status] }}
                                  handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                                  details={opportunity.settingDetails(
                                    opportunitiesLabels,
                                    router.locale,
                                    opportunity.status
                                  )}
                                  handleMoreButtonClick={() => setShowMorePerksForPast({ [index]: true })}
                                />
                                {showMorePerksForPast?.[index] && (
                                  <MorePerksModal
                                    labels={modalLabels}
                                    onClose={() => setShowMorePerksForPast({ [index]: false })}
                                    perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                                  />
                                )}
                                {showSupportACreatorModal?.[opportunity.id] && (
                                  <SupportACreatorModal
                                    labels={{
                                      title: opportunitiesLabels.creatorCode.title,
                                      close: modalLabels.close,
                                      copied: opportunitiesLabels.copied,
                                      copyText: opportunitiesLabels.creatorCode.copyText
                                    }}
                                    onClose={() => setShowSupportACreatorModal({ [opportunity.id]: false })}
                                    codeWindow={formatCodeActivationWindow(opportunity.activationWindow(locale))}
                                    gameTitle={opportunity.gameTitle}
                                    code={creatorCodeDetails?.[opportunity.id]?.creatorCode?.code}
                                  />
                                )}
                                {showEventDetailsModal?.[opportunity.id] && (
                                  <EventDetailsModal
                                    labels={{
                                      title: opportunity.isInPerson()
                                        ? opportunitiesLabels.inPersonEvent
                                        : opportunitiesLabels.remote,
                                      close: modalLabels.close,
                                      joinEvent: opportunitiesLabels.remoteEvent.joinEvent
                                    }}
                                    onClose={() => setShowEventDetailsModal({ [opportunity.id]: false })}
                                    eventWindow={formatEventWindow(
                                      opportunity.eventWindow(locale),
                                      opportunitiesLabels
                                    )}
                                    eventPeriodEnded={{
                                      label: opportunitiesLabels.eventDetails.info,
                                      value: opportunitiesLabels.remoteEvent.description
                                    }}
                                    isMeetingLinkDisabled={true}
                                    isInPerson={opportunity.isInPerson()}
                                  />
                                )}
                                {showGameCodeModal?.[opportunity.id] && (
                                  <GameCodeModal
                                    labels={{
                                      title: opportunitiesLabels.getGameCode,
                                      close: modalLabels.close,
                                      copied: opportunitiesLabels.copied,
                                      copyText: opportunitiesLabels.getGameCode
                                    }}
                                    onClose={() => setShowGameCodeModal({ [opportunity.id]: false })}
                                    platform={opportunity?.gameCode?.platform}
                                    gameTitle={opportunity.gameTitle}
                                    code={opportunity?.gameCode?.code || opportunitiesLabels.checkBackSoon}
                                    hasCode={!!opportunity?.gameCode?.code}
                                  />
                                )}
                              </>
                            )}
                          </Fragment>
                        ))}
                        {pastPages && pastPages.length > 1 && (
                          <div className="dashboard-pagination-container">
                            <Pagination
                              next={layout.buttons.next}
                              prev={layout.buttons.prev}
                              pages={pastPages}
                              currentPage={pastCurrentPage}
                              onPageChange={(page) => setPastCurrentPage(page)}
                            />
                          </div>
                        )}
                      </>
                    ))}
                </div>
              </div>
              <div className="dashboard-my-content-container">
                <div className="dashboard-sub-title-my-content">{dashboardLabels.myContent}</div>
                <div className="dashboard-tab-content-my-content">
                  {!submittedContent || submittedContent?.contents.length === 0 ? (
                    <div className="dashboard-opportunities-no-content">
                      <div className="dashboard-opportunities-no-content-row1">
                        <img src="/img/dashboard/Lightbulb.png" alt="icon" className="dashboard-opportunities-icon" />
                        <div>
                          <div className="dashboard-opportunities-no-content-title">
                            {dashboardLabels.noSubmittedContent}
                          </div>
                          <div className="dashboard-opportunities-no-content-desc">
                            {dashboardLabels.noSubmittedContentDesc}{" "}
                            <Link href="/opportunities" className="dashboard-opportunities-no-content-desc-link">
                              {layout.header.opportunities}
                            </Link>
                            .
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <ContentCardsWithFeedback
                      submittedContent={submittedContent}
                      layout={layout}
                      handleToggleFeedback={onToggleFeedback}
                      opportunityClickHandler={opportunityClickHandler}
                      locale={router.locale}
                      contentsFeedback={contentsFeedback}
                      selectedContentId={selectedContentId}
                      feedbackLoading={feedbackLoading}
                    />
                  )}
                </div>
                {showMyContentButton && (
                  <div className="dashboard-content-button">
                    <Link href="/my-content" className="dashboard-content-button btn btn-primary btn-md">
                      {dashboardLabels.viewMyContent}
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </LayoutBody>
      <LayoutFooter>
        <Footer
          locale={locale}
          labels={footer?.labels}
          analytics={analytics}
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
        />
      </LayoutFooter>
    </Layout>
  );
}

const ContentCardsWithFeedback = memo(function ContentCardsWithFeedback({
  submittedContent,
  layout,
  handleToggleFeedback,
  opportunityClickHandler,
  locale,
  contentsFeedback,
  selectedContentId,
  feedbackLoading
}) {
  const {
    configuration: { FLAG_SIGNED_URL_V2_ENABLED, FLAG_SIGNED_URL_V1_ENABLED }
  } = useDependency();
  return (
    <div>
      {submittedContent?.contents.map((card) => (
        <div key={card.id}>
          <ContentCard
            content={{
              ...card,
              reviewFinalRemark: card.reviewFinalRemark
                ? {
                    ...card.reviewFinalRemark,
                    date: card.formattedReviewFinalRemarkDate(locale)
                  }
                : card.reviewFinalRemark
            }}
            labels={layout.contentCard}
            accountType={
              card.type ? card.type : card.sourceType === "USER_DEVICE" ? "UPLOAD" : card.sourceType
            } /* type will be present only when SOURCE_TYPE is 'SOCIAL'.
          When it's a website or file content, type will be null and Content Card we'll have to rely on sourceType attribute instead of type. */
            opportunityClickHandler={() => {
              opportunityClickHandler(card.opportunityId);
            }}
            submittedDate={`${card.formattedSubmittedDate(locale)}`}
            changesRequested={card.requiresChanges()}
            handleToggleFeedback={(collapsed) =>
              handleToggleFeedback(
                collapsed,
                card.id,
                card.status,
                card.type ? card.type : card.sourceType === "USER_DEVICE" ? "FILE" : card.sourceType
              )
            }
            feedback={{
              ...contentsFeedback?.[card.id]?.[0]
            }}
            isLoading={card.id === selectedContentId ? feedbackLoading : false}
            isMcrEnabled={FLAG_SIGNED_URL_V2_ENABLED || FLAG_SIGNED_URL_V1_ENABLED}
          />
        </div>
      ))}
    </div>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (flags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(dashboardProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "notifications");
  const authenticatedUser = user ? AuthenticatedUserFactory.fromSession(user, flags.isCreatorsAPIWithProgram()) : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      locale,
      pageLabels,
      ...(await serverSideTranslations(locale, [
        "common",
        "dashboard",
        "my-content",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      FLAG_CONTENT_WITH_FINAL_REMARK: flags.isContentWithFinalRemarksEnabled(),
      showInitialMessage: req.session.showInitialMessage || null
    }
  };
};
