import React from "react";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { NoAccountPage } from "../components/pages/interested-creators/NoAccountPage";
import BrowserAnalytics from "../src/analytics/BrowserAnalytics";

const meta: Meta<typeof NoAccountPage> = {
  title: "Creator Network/Pages/Interested Creators/No Account Page",
  component: NoAccountPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof NoAccountPage>;

export const Page: Story = {
  args: {
    noAccountLabels: {
      title: "Sorry!",
      subTitlePart1: "You are currently logged in as",
      subTitlePart2: "It appears you are not a member of the EA Creator Network.",
      descriptionPara1:
        "If you’re interested in joining, you can submit a request to join and one of our EA Creator Network team members will review your information.",
      descriptionPara2: "To submit, you need to be over 18 years old and will need an ",
      descriptionPara3: "Electronic Arts Account.",
      creatorNetwork: "Creator Network",
      applyNow: "Request to Join",
      exploreTitle: "Explore what the EA Creator Network has to Offer"
    },
    email: "<EMAIL>",
    explorePages: [
      {
        title: "How does the new platform work?",
        image: "img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: "How it works",
        href: "/how-it-works"
      },
      {
        title: "Opportunities, communities and partnerships",
        image: "img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: "Available Perks",
        href: "/opportunities-rewards"
      }
    ],
    analytics: {} as unknown as BrowserAnalytics
  }
};
