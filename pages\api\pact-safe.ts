import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../src/ApiContainer";
import { createRouter } from "next-connect";
import GenerateTermsAndConditionsUrlAction from "../../src/actions/pactSafe/GenerateTermsAndConditionsUrlAction";
import SignerInformation from "../../src/pactSafe/SignerInformation";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const { action: actionType } = req.query;
    const {
      businessName = null,
      creatorId = "",
      country = "",
      email = "",
      firstName = "",
      lastName = "",
      screenName = "",
      locale,
      program
    } = req.body.params;
    const signer = { creatorId, locale, firstName, lastName, screenName, businessName, country, email, program };
    let termsUrl = {};
    if (actionType === "getUrl") {
      const urlAction = ApiContainer.get(GenerateTermsAndConditionsUrlAction);
      termsUrl = await urlAction.getUrl(signer as SignerInformation);
    }
    res.status(200).json(termsUrl);
  });

export default router.handler({ onError });
