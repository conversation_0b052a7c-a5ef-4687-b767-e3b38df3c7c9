import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../../../src/ApiContainer";
import { createRouter } from "next-connect";
import ViewAssignedGameCodeAction from "../../../../src/actions/Participations/ViewAssignedGameCode/ViewAssignedGameCodeAction";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  verifySession as legacyVerifySession,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../../config";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : legacyVerifySession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(ViewAssignedGameCodeAction);
    const participationId = req.query.id as string;

    const assignedGameCode = await action.execute(participationId);

    res.status(200).json(assignedGameCode);
    res.end();
  });

export default router.handler({ onError });
