import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { ApplicationPendingPage } from "../components/pages/interested-creators/ApplicationPendingPage";
import BrowserAnalytics from "../src/analytics/BrowserAnalytics";

/**
 * Page shown to creators whose application has not been reviewed by any community manager yet
 */
const meta: Meta<typeof ApplicationPendingPage> = {
  title: "Creator Network/Pages/Interested Creators/Application Pending Page",
  component: ApplicationPendingPage,
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationPendingPage>;

/**
 * Default copy shown to creators whose request has not been reviewed yet
 */
export const Page: Story = {
  args: {
    applicationPendingLabels: {
      title: "Submission Complete",
      description:
        "We have already received your submission request to join the Creator Network from this Electronic Arts account. You can view the status of your submission below:",
      returnToCreatorNetwork: "Return to Creator Network",
      pending: "Pending",
      unReviewed: "Unreviewed",
      email: "Email",
      gamerTag: "Gamer Tag",
      status: "Status",
      submissionReceived: "Submission Received",
      submissionReceivedDescription: "We have your submission",
      submissionDate: "January 17th, 2024",
      submissionUpdate: "Still interested in joining?",
      submissionUpdateDescription:
        "Due to the high volume of submissions, your previous submission has not yet been reviewed. If you are still interested in joining EA Creator Network, please review and update your information to resubmit.",
      reviewAndResubmit: "Review & Resubmit"
    },
    emailId: "<EMAIL>",
    defaultGamerTag: "GG2214",
    locale: "en-us",
    analytics: { checkedApplicationStatus: () => {} } as unknown as BrowserAnalytics
  }
};
