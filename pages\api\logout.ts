import "reflect-metadata";
import { createRouter } from "next-connect";
import onError from "../../src/api/AuthErrorHandler";
import { NextApiResponse } from "next";
import config from "../../config";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import { RedirectToLogoutController } from "@eait-playerexp-cn/authentication";
import ApiContainer from "@src/ApiContainer";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  if (config.FLAG_PER_PROGRAM_PROFILE) {
    const controller = ApiContainer.get(RedirectToLogoutController);
    await controller.handle(req, res);
  } else {
    delete req.session.user;
    req.session.save(() =>
      req.session.regenerate(() => {
        res.setHeader("Location", encodeURI(config.LOGOUT_URL.toString()));
        res.status(302);
        res.end();
      })
    );
  }
});

export default router.handler({ onError });
