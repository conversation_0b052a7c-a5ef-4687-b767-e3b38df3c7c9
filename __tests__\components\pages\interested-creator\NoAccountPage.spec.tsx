import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { NoAccountPage, NoAccountProps } from "../../../../components/pages/interested-creators/NoAccountPage";
import { axe } from "jest-axe";
import { noAccountTranslations } from "../../../translations/index";
import { NextRouter } from "next/router";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";

describe("NoAccountPage", () => {
  const mockPush = jest.fn().mockResolvedValue(true);
  const { noAccountLabels } = noAccountTranslations;
  const noAccountProps: NoAccountProps = {
    noAccountLabels,
    email: "<EMAIL>",
    explorePages: [
      {
        title: "How does the new platform work?",
        image: "/img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: "How it works",
        href: "/how-it-works"
      },
      {
        title: "Opportunities, communities and partnerships",
        image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: "Available Perks",
        href: "/opportunities-rewards"
      }
    ],
    router: {
      push: mockPush,
      locale: "en-us"
    } as unknown as NextRouter,
    analytics: {} as unknown as BrowserAnalytics
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows no account found labels", () => {
    render(<NoAccountPage {...{ ...noAccountProps }} />);

    const { noAccountLabels, email } = noAccountProps;

    expect(screen.getByText(noAccountLabels.title)).toBeInTheDocument();
    const subTitle = screen.getByText(`${noAccountLabels.subTitlePart1} ${email}. ${noAccountLabels.subTitlePart2}`);
    expect(subTitle).toBeInTheDocument();
    expect(screen.getByText(noAccountLabels.descriptionPara1)).toBeInTheDocument();
    expect(screen.getByText(noAccountLabels.applyNow)).toBeInTheDocument();
    expect(screen.getByText(noAccountLabels.exploreTitle)).toBeInTheDocument();
    expect(screen.getByText(noAccountProps.explorePages[0].actionLabel)).toBeInTheDocument();
    expect(screen.getByText(noAccountProps.explorePages[1].actionLabel)).toBeInTheDocument();
    expect(screen.getByText(noAccountProps.explorePages[0].title)).toBeInTheDocument();
    expect(screen.getByText(noAccountProps.explorePages[1].title)).toBeInTheDocument();
  });

  it("logs 'Started Creator Application' on clicking Apply Now", async () => {
    const analytics = { startedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<NoAccountPage {...{ ...noAccountProps }} analytics={analytics} />);

    await userEvent.click(await screen.findByRole("button", { name: "Request to Join" }));

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledTimes(1);
      expect(mockPush).toHaveBeenCalledWith("/api/applications");
      expect(analytics.startedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.startedCreatorApplication).toHaveBeenCalledWith({ locale: "en-us", page: "/" });
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<NoAccountPage {...{ ...noAccountProps }} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
