import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useCallback, useMemo } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import labelsNoAccount from "../../config/translations/no-account";
import labelsCommon from "../../config/translations/common";
import labelsIndex from "../../config/translations/index";
import flags from "../../utils/feature-flags";
import { NoAccountPage } from "@components/pages/interested-creators/NoAccountPage";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import withNoAccountCreator from "../../src/utils/WithNoAccountCreator";
import { AuthenticatedUser, AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import { PageProps } from "@components/pages/interested-creators/ExplorePages";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Loading from "../../components/Loading";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import noAccountPageProps from "@src/serverprops/NoAccountPageProps";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { useDependency } from "@src/context/DependencyContext";

export type NoAccount = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InitialInterestedCreator | Identity;
  locale: string;
  showInitialMessage?: boolean;
  user: AuthenticatedUser;
};

export default function NoAccount({ interestedCreator, locale }: NoAccount): JSX.Element {
  const { analytics } = useDependency();
  const { t } = useTranslation(["common", "no-account", "index"]);
  const { state: { isLoading } = {} } = useAppContext();
  const router = useRouter();
  const { noAccountLabels } = useMemo(() => {
    const {
      header: { creatorNetwork },
      buttons: { close }
    } = labelsCommon(t);
    const { howItWorks } = labelsIndex(t);
    const { leftTitle, rightTitle } = howItWorks;
    return {
      noAccountLabels: {
        creatorNetwork,
        close,
        ...labelsNoAccount(t),
        leftTitle,
        rightTitle
      }
    };
  }, [t]);

  const { leftTitle, rightTitle, availablePerks, howItWorks, creatorNetwork, close } = noAccountLabels;

  const explorePages: PageProps[] = useMemo(
    () => [
      {
        title: leftTitle,
        image: "../img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: howItWorks,
        href: "/how-it-works"
      },
      {
        title: rightTitle,
        image: "../img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: availablePerks,
        href: "/opportunities-rewards"
      }
    ],
    [howItWorks, availablePerks, rightTitle, leftTitle]
  );

  const onClose = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        {isLoading && <Loading />}
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close, onClose }} />
          <NoAccountPage
            {...{ router, locale, noAccountLabels, explorePages }}
            email={
              (interestedCreator as Identity)?.email ||
              (interestedCreator as InitialInterestedCreator)?.originEmail ||
              ""
            }
            analytics={analytics}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(noAccountPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<NoAccount>;
  }

  const interestedCreator = await withNoAccountCreator(req, res);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator),
      locale,
      ...(await serverSideTranslations(locale, ["common", "no-account", "index"])),
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
