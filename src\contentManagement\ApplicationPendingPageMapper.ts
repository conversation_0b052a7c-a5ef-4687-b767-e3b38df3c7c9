import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ApplicationPendingPageLabels = {
  applicationPendingLabels: {
    returnToCreatorNetwork: string;
    gamerTag: string;
    pending: string;
    submissionUpdateDescription: string;
    submissionUpdate: string;
    title: string;
    description: string;
    reviewAndResubmit: string;
    submissionReceived: string;
    submissionReceivedDescription: string;
    unReviewed: string;
    status: string;
    email: string;
    submissionDate: string;
    returnToHomePage: string;
    subTitle: string;
    applicationPendingDescription: string;
    programLabel: string;
    programName: string;
  };
};

export class ApplicationPendingPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ApplicationPendingPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      applicationPendingLabels: {
        returnToCreatorNetwork: microCopy.get("applicationPending.returnToCreatorNetwork"),
        gamerTag: microCopy.get("applicationPending.gamerTag"),
        pending: microCopy.get("applicationPending.pending"),
        submissionUpdateDescription: microCopy.get("applicationPending.submissionUpdateDescription"),
        submissionUpdate: microCopy.get("applicationPending.submissionUpdate"),
        title: microCopy.get("applicationPending.title"),
        description: microCopy.get("applicationPending.description"),
        reviewAndResubmit: microCopy.get("applicationPending.reviewAndResubmit"),
        submissionReceived: microCopy.get("applicationPending.submissionReceived"),
        submissionReceivedDescription: microCopy.get("applicationPending.submissionReceivedDescription"),
        unReviewed: microCopy.get("applicationPending.unReviewed"),
        status: microCopy.get("applicationPending.status"),
        email: microCopy.get("applicationPending.email"),
        submissionDate: microCopy.get("applicationPending.submissionDate"),
        returnToHomePage: microCopy.get("applicationPending.backHome"),
        programLabel: microCopy.get("applicationRejected.programLabel"),
        programName: microCopy.get("applicationRejected.programName"),
        subTitle: microCopy.get("applicationPending.subTitle"),
        applicationPendingDescription: microCopy.get("applicationPending.applicationPendingDescription")
      }
    };
  }
}
