import React, { FC, FunctionComponent, useCallback, useEffect, useMemo, useState } from "react";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import {
  AccountType,
  ConnectAccountModal,
  ContentCard,
  ContentGuideLines,
  DeliverableCard,
  facebookIcon,
  instagramIcon,
  OpportunityDescription,
  tiktokIcon,
  twitchIcon,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";
import CreatorWithExpiredAccounts from "../../../src/creators/CreatorWithExpiredAccounts";
import {
  CLICKED_DELIVERABLE_ID,
  CURRENT_DELIVERABLE_PAGE,
  ERROR,
  TOAST_ERROR,
  useAsync,
  useIsMounted,
  VALIDATION_ERROR,
  WINDOW_PARAMS
} from "../../../utils";
import ConnectedAccountsService from "../../../src/api/services/ConnectedAccountsService";
import { SHOW_WARNING_FOR_CHANGES_REQUESTED, useAppContext } from "@src/context";
import { Toast, useToast } from "../../toast";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import {
  INSTA_CANNOT_CONNECT,
  INSTA_WARNING_ERROR,
  INSTAGRAM_STEPS_LINK,
  YOUTUBE_NO_CHANNEL_ERROR
} from "../interested-creators/InterestedCreatorsInformationPage";
import SubmitSocialMediaContentModal, { MediaLabelType } from "./SubmitSocialMediaContentModal";
import labelsSocialMediaContent from "../../../config/translations/submit-social-media-content";
import {
  AccountsType,
  Content,
  ContentIconType,
  ContentStatus,
  DeliverableStatus
} from "@eait-playerexp-cn/core-ui-kit";
import { useContentSubmissionForms } from "./useContentSubmissionForms";
import SubmitWebsiteContentModal from "./SubmitWebsiteContentModal";
import SubmittedContentService from "../../../src/api/services/SubmittedContentService";
import Pagination from "../../dashboard/Pagination";
import UploadContentSubmissionModal from "./UploadContentSubmissionModal";
import { useRouter } from "next/router";
import ConnectFacebookPagesModal from "../ConnectFacebookPagesModal";
import { Link } from "react-scroll";
import { AxiosResponse } from "axios";
import { useDependency } from "@src/context/DependencyContext";
export type Size = "DESKTOP" | "TABLET" | "MOBILE";

const mediaLabels = {
  YOUTUBE: "YouTube",
  TWITCH: "Twitch",
  FACEBOOK: "Facebook",
  INSTAGRAM: "Instagram",
  TIKTOK: "TikTok"
};

export type Accounts = { facebook: string; instagram: string; youTube: string; twitch: string; tiktok: string };

export type MyContentLabels = {
  blog: string;
  image: string;
  livestream: string;
  podcast: string;
  social_post: string;
  video: string;
  audio: string;
  text: string;
  zip: string;
};

export type AddContentFormLabels = {
  addContent: string;
  addNewContent: string;
  addContentInstruction: string;
  accountInformation1: string;
  accountInformation2: string;
  clickTheIcon: string;
  addAccount: string;
  connectNewAccount: string;
  connectNewAccountDescription: string;
  connectNewAccountDescriptionWithTikTok: string;
  facebook: string;
  instagram: string;
  youTube: string;
  twitch: string;
  tiktok: string;
  contentInformation1: string;
  contentInformation2: string;
  contentInformation3: string;
  contentSubmissionSucessTitle: string;
  contentSubmissionSucessDescription: string;
  connectAnAccount: string;
  modalConfirmationTitleFB: string;
  messages: {
    actionTitle: string;
    actionDescription1: string;
    actionDescription2: string;
    actionDescription3: string;
    actionDescription4: string;
    youtubeNoChannelError: string;
    cannotConnectInstaAccount: string;
    cannotConnectInstaAccountHeader: string;
  };
  removeAccount: string;
  reconnectAccount: string;
  or: string;
  expireAccount: string;
  comma?: string;
};

export type Layout = {
  buttons: {
    gotit: string;
    submitContent: string;
    cancel: string;
    submit: string;
    upload: string;
    next: string;
    prev: string;
    close: string;
    connect: string;
    update: string;
  };
  toasts: { contentSubmittedTitle: string; contentSubmittedDescription: string };
  main: {
    pageNotFound: string;
    pageNotFoundContent: string;
    unauthorized: string;
    unhandledError: string;
    unhandledErrorMessage: string;
  };
  contentCard: {
    approvalNotRequired: string;
    approved: string;
    pendingApproval: string;
    rejected: string;
    notApproved: string;
    submitted: string;
    video: string;
    changesRequired: string;
    inReview: string;
    viewChangesRequired: string;
    hideChangesRequired: string;
    sentOn: string;
    additionalDescription: string;
    file: string;
    url: string;
    updated: string;
    from: string;
    inScan: string;
    contentNotApproved: string;
    contentApproved: string;
    viewDetails: string;
    hideDetails: string;
  };
  header: {
    disclosure: string;
  };
};

export type UploadContentLabels = {
  title: string;
  fileUpload: string;
  chooseFile: string;
  noFileChoosen: string;
  acceptedFormats: string;
  maxFileSize: string;
  maxLimitMessage: string;
  invalidFileTypeMessage: string;
  fileSelected: string;
  fileUploading: string;
  success: {
    title: string;
    content: string;
  };
  removeSelectedFile: string;
  uploadFileProgress: string;
};

export type ContentSubmissionTabLabels = {
  contentSubmissionLabels: ContentSubmissionLabels;
  facebookGuideModalLabels: SocialMediaGuideModalLabels;
  instagramGuideModalLabels: SocialMediaGuideModalLabels;
  youtubeGuideModalLabels: SocialMediaGuideModalLabels;
  twitchGuideModalLabels: SocialMediaGuideModalLabels;
  tiktokGuideModalLabels: SocialMediaGuideModalLabels;
  addContentLabels: AddContentLabels;
  connectAccountLabels: ConnectAccountLabels;
  websiteContentLabels: WebsiteContentLabels;
  uploadContentLabels: UploadContentLabels;
};

export type AddContentLabels = {
  clickTheIcon: string;
  addContent: string;
  addNewContent: string;
  addContentInstruction: string;
  accountInformation1: string;
  accountInformation2: string;
  contentInformation1: string;
  contentInformation2: string;
  contentInformation3: string;
  contentSubmissionSucessTitle: string;
  contentSubmissionSucessDescription: string;
  connectAnAccount: string;
};

export type ConnectAccountLabels = {
  connectNewAccount: string;
  connectNewAccountDescription: string;
  connectNewAccountDescriptionWithTikTok: string;
  accounts: Accounts;
  addAccount: string;
  modalConfirmationTitleFB: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  messages: {
    removeAccountTitle: string;
    removeAccountDescription: string;
    cannotConnectInstaAccount: string;
    cannotConnectInstaAccountHeader: string;
    actionTitle: string;
    actionDescription1: string;
    actionDescription2: string;
    actionDescription3: string;
    actionDescription4: string;
    youtubeNoChannelError: string;
  };
  removeAccount: string;
  reconnectAccount: string;
  or: string;
  expireAccount: string;
  comma?: string;
};

export type SocialMediaGuideModalLabels = {
  title: string;
  subTitle: string;
  contentImages: {
    desc: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    imageSrc: any[];
  }[];
  pointsToNote: string[];
  footerLabel: string;
  pointsToNoteTitle: string;
  onClose: () => void;
  closeLabel: string;
};
export type ContentSubmissionLabels = {
  disclosureParagraph1: string;
  disclosureParagraph2: string;
  linkSubmissionContent: string;
  linkSubmissionGuideLine: string;
  availableResources: string;
  downloadEaLogo: string;
  downloadAttachments: string;
  contentDeliverables: string;
  deliverablesInstruction: string;
  notYetSubmitted: string;
  pendingApproval: string;
  rejected: string;
  notApproved: string;
  completed: string;
  changesRequired: string;
  submitUnlimitedContent: string;
  submissionWindowClosed: string;
  contentRequirements: string;
  contentGuideLine: string;
  paid: {
    contentGuideLineSubTitle: string;
    contentGuideLineDescription1: string;
    contentGuideLineDescription2: string;
    contentGuideLineDescription3: string;
    contentGuideLineDescription4: string;
    contentGuideLineDescription5: string;
    linkSubmissionGuideLineSubTitle1: string;
    linkSubmissionGuideLineSubTitle2: string;
  };
  nonPaid: {
    contentGuideLineSubTitle: string;
    contentGuideLineDescription1: string;
    contentGuideLineDescription2: string;
    contentGuideLineDescription3: string;
    linkSubmissionGuideLineSubTitle1: string;
    linkSubmissionGuideLineSubTitle2: string;
  };
  submissionOpensWithNoContent: string;
  needsChanges: string;
  changesRequiredTitle: string;
};

export type WebsiteContentLabels = {
  title: string;
  selectContentType: string;
  contentTitle: string;
  contentTitlePlaceholder: string;
  contentUrlPlaceholder: string;
  contentType: string;
  contentUrl: string;
  contentTitleRequired: string;
  contentUrlRequired: string;
  contentTypeRequired: string;
  duplicateUrlMessage: string;
  unsafeUrlMessage: string;
  validUrlMessage: string;
  websiteUrlMessage: string;
  contentTitleLongMessage: string;
};

export type UploadFileContentLabels = {
  title: string;
  acceptedFormats: string;
  chooseFile: string;
  fileSelected: string;
  fileUpload: string;
  fileUploading: string;
  invalidFileTypeMessage: string;
  maxFileSize: string;
  maxLimitMessage: string;
  noFileChoosen: string;
  removeSelectedFile: string;
  uploadFileProgress: string;
  success: {
    title: string;
    content: string;
  };
  error: {
    title: string;
    content: string;
  };
  contentDescription?: string;
  contentDescriptionPlaceholder?: string;
  maxcharacterLimit?: string;
  contentDescriptionRequired?: string;
  contentDescriptionLongMessage?: string;
};

export type ContentDeliverablesTabLabels = {
  connectAccountLabels: ConnectAccountLabels;
  addContentLabels: AddContentLabels;
  facebookGuideModalLabels: SocialMediaGuideModalLabels;
  youtubeGuideModalLabels: SocialMediaGuideModalLabels;
  instagramGuideModalLabels: SocialMediaGuideModalLabels;
  twitchGuideModalLabels: SocialMediaGuideModalLabels;
  tiktokGuideModalLabels: SocialMediaGuideModalLabels;
  contentSubmissionLabels: ContentSubmissionLabels;
  websiteContentLabels: WebsiteContentLabels;
  uploadContentLabels: UploadFileContentLabels;
};

type AccountConnected = {
  accountConnected: string;
};

type AdditionalDescription = {
  contentType: string;
  updateType: string;
};

type Page = {
  accessToken: string;
  id: string;
  name: string;
};

export type OpportunitiesLabels = {
  viewAll: string;
  contentGuidelinesTitle: string;
};

export type ContentDetails = {
  contentType: string;
  contentUri: string;
  id: string;
  name: string;
  opportunityName: string;
  status: ContentStatus;
  thumbnail: string;
  contentTypeLabel: string;
  type: string;
  sourceType: string;
  formattedReviewFinalRemarkDate?: (locale: string) => string;
  formattedSubmittedDate: (locale: string) => string;
  requiresChanges: () => void;
  opportunityId: string;
  contentDescription?: string;
};

export type OpportunityWithDeliverablesStatus = OpportunityWithDeliverables & {
  status: DeliverableStatus;
};

export const getChangeRequestedContent = (
  changesRequestedContent: Content[],
  needsChangesLabel: string
): JSX.Element => {
  return (
    <>
      {changesRequestedContent.map((item, index) => (
        <span key={index}>
          <Link className="toast-change-requested" offset={-220} to={item.id} spy={true} smooth={true} duration={500}>
            {item.name}
          </Link>
          <span>
            {index == changesRequestedContent.length - 1
              ? ""
              : index == changesRequestedContent.length - 2
              ? " and "
              : ","}
          </span>
        </span>
      ))}
      {needsChangesLabel}
    </>
  );
};

export const showChangeRequestedToast = (
  warning: (content: JSX.Element, options?: Record<string, unknown>) => JSX.Element,
  contentSubmissionLabels: {
    needsChanges: string;
    changesRequiredTitle: string;
  },
  changesRequestedContent: Content[],
  layout: Layout,
  stableDispatch: (action) => void
): JSX.Element => {
  return warning(
    <Toast
      header={contentSubmissionLabels.changesRequiredTitle}
      content={getChangeRequestedContent(changesRequestedContent, contentSubmissionLabels.needsChanges)}
      closeButtonAriaLabel={layout.buttons.close}
    />,
    {
      onClose: () => stableDispatch({ type: SHOW_WARNING_FOR_CHANGES_REQUESTED, data: false }),
      toastId: SHOW_WARNING_FOR_CHANGES_REQUESTED
    }
  );
};

export type ContentDeliverablesTabProps = {
  contentDeliverablesTabLabels: ContentDeliverablesTabLabels;
  layout: Layout;
  opportunity: OpportunityWithDeliverables;
  WATERMARKS_URL: string;
  isJoined: boolean;
  isSubmissionWindowClosed: boolean;
  hasContentSubmissionWindowStarted: boolean;
  size: Size;
  opportunitiesLabels: OpportunitiesLabels;
  creator: CreatorWithExpiredAccounts;
  accountConnected: string;
  setConnectedAccount: (accountName: string | null) => void;
  t: (accountTemplate: string, accountType: AccountConnected | AdditionalDescription) => string;
  analytics: BrowserAnalytics;
  locale: string;
  pages: Array<Page>;
  error: {
    code: string;
    message: string;
  };
  participationId: string;
  YOUTUBE_HOSTS: string[];
  INSTAGRAM_HOSTS: string[];
  FACEBOOK_HOSTS: string[];
  TWITCH_HOSTS: string[];
  TIKTOK_HOSTS: string[];
  setUpdatedContent: (boolean) => void;
  myContentLabels: MyContentLabels;
  content?: Record<string, ContentDetails[]>;
  thumbnail: string;
  deliverablePages: Record<string, Array<number>>;
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED?: boolean;
  UPDATE_OPPORTUNITY_DETAILS?: boolean;
  FLAG_CONTENT_WITH_FINAL_REMARK?: boolean;
};
const accountLabels = {
  YOUTUBE: "youTube",
  FACEBOOK: "facebook",
  INSTAGRAM: "instagram",
  TWITCH: "twitch",
  TIKTOK: "tiktok"
};

const ACCOUNT_ICONS: Record<AccountName, FunctionComponent> = {
  YOUTUBE: youTubeIcon,
  FACEBOOK: facebookIcon,
  INSTAGRAM: instagramIcon,
  TWITCH: twitchIcon,
  TIKTOK: tiktokIcon
};

type AccountName = "YOUTUBE" | "INSTAGRAM" | "FACEBOOK" | "TWITCH" | "TIKTOK";

export type DeliverableFormat = "SOCIAL" | "WEBSITE" | "FILE";

type DropdownButton = {
  name: string;
  type: AccountName;
  handleClick: () => void;
};

const ContentDeliverablesTab: FC<ContentDeliverablesTabProps> = ({
  contentDeliverablesTabLabels,
  layout,
  opportunity,
  WATERMARKS_URL,
  isJoined,
  isSubmissionWindowClosed,
  hasContentSubmissionWindowStarted,
  size,
  opportunitiesLabels,
  creator,
  accountConnected,
  setConnectedAccount,
  t,
  analytics,
  locale,
  pages,
  error,
  participationId,
  YOUTUBE_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TWITCH_HOSTS,
  TIKTOK_HOSTS,
  setUpdatedContent,
  thumbnail,
  myContentLabels,
  deliverablePages,
  content,
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED,
  FLAG_CONTENT_WITH_FINAL_REMARK,
  UPDATE_OPPORTUNITY_DETAILS
}) => {
  const { errorHandler } = useDependency();
  const [showConnectAccountModal, setShowConnectAccountModal] = useState({});
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const [showPagesModal, setShowPagesModal] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  const [showModalWindowForSocialAccount, setshowModalWindowForSocialAccount] = useState({});
  const [name, setName] = useState("");
  const [mediaType, setMediaType] = useState("");
  const [accountId, setAccountId] = useState("");
  const [showModalWindowForWebsite, setshowModalWindowForWebsite] = useState(false);
  const [enableUpdateWebsite, setEnableUpdateWebsite] = useState<boolean>(false);
  const [deliverableId, setDeliverableId] = useState<string>();
  const [deliverableTitle, setDeliverableTitle] = useState<string>();
  const [deliverableType, setDeliverableType] = useState<string>();
  const [contentsFeedback, setContentsFeedback] = useState(null);
  const [selectedContentId, setSelectedContentId] = useState("");
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const {
    dispatch,
    state: {
      isError = false,
      isValidationError = false,
      showWarningForChangesRequested = false,
      currentDeliverablePage = null
    } = {}
  } = useAppContext() || {};
  const stableDispatch: (state, action?) => void = useCallback(dispatch, []);
  const { success, error: errorToast, warning } = useToast();
  const isMounted = useIsMounted();
  const [showModalWindowForUploadContent, setshowModalWindowForUploadContent] = useState(false);
  const [isForUpdateUploadedFile, setEnableUpdateFileUpload] = useState<boolean>(false);
  const [contentTypesForUpload, setContentTypesForUpload] = useState([]);
  const [fileTypes, setFileTypes] = useState([]);
  const [hasAccountConnectedRun, setHasAccountConnectedRun] = useState(false);
  const [hasInstagramConnectRun, setHasInstagramConnectRun] = useState(false);
  const router = useRouter();
  const { configuration: config } = useDependency();

  const onConnectFbC = useCallback(async () => {
    if (selectedPage) {
      try {
        await ConnectedAccountsService.connectFbPages(selectedPage);
        setSelectedPage(null);
      } catch (error) {
        errorHandler(stableDispatch, error);
      }
      location.href = "?tab=content-deliverables";
    }
  }, [selectedPage, stableDispatch]);

  const { pending, execute: onConnectFb } = useAsync(onConnectFbC, false);

  const {
    main: { unhandledError }
  } = layout;

  const {
    contentSubmissionLabels,
    connectAccountLabels,
    addContentLabels,
    facebookGuideModalLabels,
    youtubeGuideModalLabels,
    instagramGuideModalLabels,
    twitchGuideModalLabels,
    tiktokGuideModalLabels,
    websiteContentLabels,
    uploadContentLabels
  }: ContentDeliverablesTabLabels = contentDeliverablesTabLabels;

  const statusLabel = {
    APPROVED: contentSubmissionLabels.completed,
    REJECTED: contentSubmissionLabels.rejected,
    CHANGE_REQUESTED: contentSubmissionLabels.changesRequired,
    AWAITING_SUBMISSION: contentSubmissionLabels.notYetSubmitted,
    PENDING: contentSubmissionLabels.pendingApproval,
    UNLIMITED_CONTENT: contentSubmissionLabels.submitUnlimitedContent,
    NOT_APPROVED: contentSubmissionLabels.notApproved
  };
  const [disclosurePolicy, setDisclosurePolicy] = useState(null);
  useEffect(() => {
    if (opportunity.hasPayments) {
      setDisclosurePolicy({
        title: contentSubmissionLabels.contentGuideLine,
        subTitle: contentSubmissionLabels.paid.contentGuideLineSubTitle,
        descriptions: [
          {
            descriptionText: contentSubmissionLabels.paid.contentGuideLineDescription1
          },
          {
            descriptionText: contentSubmissionLabels.paid.contentGuideLineDescription2
          },
          {
            descriptionText: contentSubmissionLabels.paid.contentGuideLineDescription3
          },
          {
            descriptionText: contentSubmissionLabels.paid.contentGuideLineDescription4
          },
          {
            descriptionText: contentSubmissionLabels.paid.contentGuideLineDescription5
          }
        ]
      });
    } else {
      setDisclosurePolicy({
        title: contentSubmissionLabels.contentGuideLine,
        subTitle: contentSubmissionLabels.nonPaid.contentGuideLineSubTitle,
        descriptions: [
          {
            descriptionText: contentSubmissionLabels.nonPaid.contentGuideLineDescription1
          },
          {
            descriptionText: contentSubmissionLabels.nonPaid.contentGuideLineDescription2
          },
          {
            descriptionText: contentSubmissionLabels.nonPaid.contentGuideLineDescription3
          }
        ]
      });
    }
  }, [opportunity]);

  const linkSubmissionLabels = {
    title: contentSubmissionLabels.linkSubmissionGuideLine,
    subTitle: opportunity?.hasPayments
      ? contentSubmissionLabels.paid.linkSubmissionGuideLineSubTitle1
      : contentSubmissionLabels.nonPaid.linkSubmissionGuideLineSubTitle1,
    descriptions: [
      {
        descriptionText: opportunity?.hasPayments
          ? contentSubmissionLabels.paid.linkSubmissionGuideLineSubTitle2
          : contentSubmissionLabels.nonPaid.linkSubmissionGuideLineSubTitle2,
        renderAsPoint: false
      }
    ]
  };

  const addContentFormLabels: AddContentFormLabels = {
    addContent: addContentLabels.addContent,
    addNewContent: addContentLabels.addNewContent,
    addContentInstruction: addContentLabels.addContentInstruction,
    accountInformation1: addContentLabels.accountInformation1,
    accountInformation2: addContentLabels.accountInformation2,
    addAccount: connectAccountLabels.addAccount,
    connectNewAccount: connectAccountLabels.connectNewAccount,
    connectNewAccountDescription: connectAccountLabels.connectNewAccountDescription,
    connectNewAccountDescriptionWithTikTok: connectAccountLabels.connectNewAccountDescriptionWithTikTok,
    facebook: connectAccountLabels.accounts.facebook,
    instagram: connectAccountLabels.accounts.instagram,
    youTube: connectAccountLabels.accounts.youTube,
    twitch: connectAccountLabels.accounts.twitch,
    tiktok: connectAccountLabels.accounts.tiktok,
    contentInformation1: addContentLabels.contentInformation1,
    contentInformation2: addContentLabels.contentInformation2,
    contentInformation3: addContentLabels.contentInformation3,
    contentSubmissionSucessTitle: addContentLabels.contentSubmissionSucessTitle,
    contentSubmissionSucessDescription: addContentLabels.contentSubmissionSucessDescription,
    connectAnAccount: addContentLabels.connectAnAccount,
    clickTheIcon: addContentLabels.clickTheIcon,
    ...connectAccountLabels,
    removeAccount: connectAccountLabels.removeAccount,
    reconnectAccount: connectAccountLabels.reconnectAccount,
    or: connectAccountLabels.or,
    expireAccount: connectAccountLabels.expireAccount,
    comma: connectAccountLabels.comma
  };

  const {
    messages: {
      cannotConnectInstaAccount,
      cannotConnectInstaAccountHeader,
      actionTitle,
      actionDescription1,
      actionDescription2,
      actionDescription3,
      actionDescription4,
      youtubeNoChannelError
    }
  } = addContentFormLabels;

  const { socialMediaContentLabels } = useMemo(() => {
    return {
      socialMediaContentLabels: labelsSocialMediaContent(t, mediaType, name)
    };
  }, [t, locale, mediaType, name]);

  const socialChannelModalLabels = {
    name,
    mediaType: mediaType as MediaLabelType,
    formLabels: {
      contentUrl: socialMediaContentLabels.contentUrl,
      contentUrlPlaceholder: socialMediaContentLabels.contentUrlPlaceholder
    },
    infoLabel: socialMediaContentLabels.infoLabel,
    buttonLabels: {
      cancel: layout.buttons.cancel,
      submit: layout.buttons.submit,
      close: layout.buttons.close
    },
    title: socialMediaContentLabels.title,
    errorLabels: {
      duplicateUrl: socialMediaContentLabels.duplicateUrl,
      instagramErrorUrl: socialMediaContentLabels.instagramErrorUrl,
      videoNotFromChannel: socialMediaContentLabels.videoNotFromChannel,
      youtubeVideoError: socialMediaContentLabels.youtubeVideoError,
      genericContentError: socialMediaContentLabels.genericContentError,
      unsupportedContentError: socialMediaContentLabels.unsupportedContentError,
      contentUrlRequired: socialMediaContentLabels.contentUrlRequired,
      unsafeUrlError: socialMediaContentLabels.unsafeUrlError,
      urlNotFromConnectedAccount: socialMediaContentLabels.urlNotFromConnectedAccount,
      urlNotFromConnectedChannel: socialMediaContentLabels.urlNotFromConnectedChannel,
      unknownTikTokVideo: socialMediaContentLabels.unknownTikTokVideo,
      invalidSocialSubmission: socialMediaContentLabels.invalidSocialSubmission,
      accountAuthorizationFailure: socialMediaContentLabels.accountAuthorizationFailure,
      cannotExpandUrl: socialMediaContentLabels.cannotExpandUrl,
      cannotSubmitContentInvalidInput: socialMediaContentLabels.cannotSubmitContentInvalidInput,
      invalidFacebookPage: socialMediaContentLabels.invalidFacebookPage,
      unSupportedContentType: socialMediaContentLabels.unSupportedContentType,
      unSupportedContentTypeForMedia: socialMediaContentLabels.unSupportedContentTypeForMedia,
      duplicateScannedContentUrl: socialMediaContentLabels.duplicateScannedContentUrl
    },
    successLabels: {
      title: layout.toasts.contentSubmittedTitle,
      content: layout.toasts.contentSubmittedDescription
    },
    creatorId: creator.id,
    participationId: participationId,
    YOUTUBE_HOSTS: YOUTUBE_HOSTS,
    TWITCH_HOSTS: TWITCH_HOSTS,
    INSTAGRAM_HOSTS: INSTAGRAM_HOSTS,
    FACEBOOK_HOSTS: FACEBOOK_HOSTS,
    TIKTOK_HOSTS: TIKTOK_HOSTS,
    accountId: accountId
  };

  const openModalForSocialContent = (type: string, name: string, accountId: string, position) => {
    setshowModalWindowForSocialAccount({ [position]: true });
    setName(name);
    setMediaType(type);
    setAccountId(accountId);
  };

  const getApprovedAccountLabel = (allowedAccountTypes: Array<AccountName>): string => {
    if (allowedAccountTypes.length === 0) return "";
    if (allowedAccountTypes.length === 1) {
      return mediaLabels[allowedAccountTypes[0]];
    } else {
      //Loop through account names till last but previous name and form account names
      const formAccountNames = allowedAccountTypes
        .slice(0, allowedAccountTypes.length - 1)
        .map((account) => mediaLabels[account]);
      //Join the array of account names with commas
      const accountDescription = formAccountNames.join(addContentFormLabels.comma);
      //Get the last account
      const lastAccountType =
        mediaLabels[allowedAccountTypes.slice(allowedAccountTypes.length - 1) as unknown as AccountName];
      return `${accountDescription} ${addContentFormLabels.or} ${lastAccountType}`;
    }
  };

  const openModalForConnectSocialAccount = (deliverableTitle, deliverableType, position) => {
    localStorage.setItem("deliverableTitle", deliverableTitle);
    localStorage.setItem("deliverableType", deliverableType);
    setShowConnectAccountModal({ [position]: true });
  };

  const getSubmitContentButtons = useCallback(
    (
      format: DeliverableFormat,
      allowedAccountTypes: Array<AccountName>,
      position: number,
      deliverableTitle: string,
      deliverableType: string
    ): Array<DropdownButton> => {
      const description = getApprovedAccountLabel(allowedAccountTypes);
      const dropdownButtons = [];
      if (format === "SOCIAL") {
        creator?.connectedAccounts
          ?.filter((account) => !account.isExpired && !account.disconnected)
          ?.filter((account) => allowedAccountTypes.includes(account.type))
          .forEach((account) => {
            dropdownButtons.push({
              name: account.username || account.name,
              type: account?.type,
              handleClick: () =>
                openModalForSocialContent(
                  mediaLabels[account.type],
                  account.username || account.name,
                  account.accountId,
                  position
                )
            });
          });
        dropdownButtons.push({
          name: addContentFormLabels.connectNewAccount,
          type: "ADD_ACCOUNT",
          // This localstorage logic will be removed once we restrict the page refresh while connecting social channel.
          handleClick: () => {
            openModalForConnectSocialAccount(deliverableTitle, deliverableType, position);
          },
          description
        });
      }
      return dropdownButtons;
    },
    [creator]
  );

  const handleAddAccount = (url: string) => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      setHasAccountConnectedRun(false);
      const loginWindow = window.open(url, "_blank", WINDOW_PARAMS);
      const loop = setInterval(function () {
        if (loginWindow.closed) {
          clearInterval(loop);
          location.href = "?tab=content-deliverables";
        }
      }, 100);
    }
  };

  const getApprovedAccounts = useCallback(
    (format: DeliverableFormat, allowedAccountTypes: Array<AccountName>): Array<AccountsType> => {
      let accounts = [];
      if (format === "SOCIAL") {
        accounts = allowedAccountTypes?.map?.((allowedAccountType: string) => {
          return {
            label: accountLabels[allowedAccountType],
            type: allowedAccountType.toLowerCase(),
            accountIcon: ACCOUNT_ICONS[allowedAccountType],
            accountType: allowedAccountType as AccountType,
            handleAddAccount: () => handleAddAccount(`/api/${allowedAccountType.toLowerCase()}-login`)
          };
        });
      }
      return accounts;
    },
    []
  );

  const handleCloseConnectAccountModal = (position) => {
    setShowConnectAccountModal({ [position]: false });
  };

  useEffect(
    function showFacebookPages() {
      if (pages?.length) {
        if (isMounted()) setShowPagesModal(true);
      } else {
        if (isMounted()) setShowPagesModal(false);
      }
    },
    [pages?.length, isMounted]
  );

  const onCloseFb = useCallback(async () => {
    try {
      setShowPagesModal(false);
      // Unset FBPages from session.
      await ConnectedAccountsService.clearFbPages();
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
    location.href = "?tab=content-deliverables";
  }, [stableDispatch]);

  const clearAccountConnected = async () => {
    try {
      await ConnectedAccountsService.clearAccountType();
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
    setConnectedAccount(null);
  };

  const onChange = ({ target: { id: pageId, value: pageAccessToken } }) => setSelectedPage({ pageId, pageAccessToken });

  useEffect(() => {
    if (accountConnected && !hasAccountConnectedRun) {
      setHasAccountConnectedRun(true);

      analytics.connectedNewSocialAccount({
        locale,
        accountType: accountConnected,
        deliverableTitle: localStorage.getItem("deliverableTitle"),
        deliverableType: localStorage.getItem("deliverableType")
      });
      success(
        <Toast
          header={t("add-content:contentSubmissionSucessTitle", { ...{ accountConnected } })}
          content={t("add-content:contentSubmissionSucessDescription", { ...{ accountConnected } })}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        { onClose: clearAccountConnected }
      );
      // Clearing the account type from session automatically if user doesn't close the toast manually
      clearAccountConnected();
      // Clearing the deliverableTitle and deliverableType from local storage.
      localStorage.removeItem("deliverableTitle");
      localStorage.removeItem("deliverableType");
    }
  }, [accountConnected, hasAccountConnectedRun]);

  const instaWarningContent = useMemo(() => {
    return (
      <div className="connect-account-insta-warning">
        <p>{actionDescription1}</p>
        <br />
        <p key="actionDescription2">
          {actionDescription2}{" "}
          <a className="connect-account-insta-steps" target="_blank" rel="noreferrer" href={INSTAGRAM_STEPS_LINK}>
            {actionDescription3}
          </a>
          {actionDescription4}
        </p>
      </div>
    );
  }, [actionDescription1, actionDescription2, actionDescription3, actionDescription4]);

  const errorMap = useMemo(() => {
    return {
      [INSTA_CANNOT_CONNECT]: cannotConnectInstaAccount,
      [YOUTUBE_NO_CHANNEL_ERROR]: youtubeNoChannelError
    };
  }, [youtubeNoChannelError, cannotConnectInstaAccount]);

  const clearError = useCallback(async () => {
    try {
      // Unset error from session.
      await ConnectedAccountsService.deleteConnectedAccountErrors();
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
    location.href = "?tab=content-deliverables";
  }, [stableDispatch]);

  const closeSocialContentModal = useCallback((position) => {
    setshowModalWindowForSocialAccount({ [position]: false });
  }, []);

  useEffect(
    function showToastOnError() {
      if (isError || isValidationError) {
        stableDispatch({ type: TOAST_ERROR, onToastCloseAction: clearError });
      }
    },
    [isError, isValidationError, stableDispatch]
  );

  const getErrorMessage = (errorMap, error, fallback) => errorMap[error?.code] || fallback;

  useEffect(() => {
    if (!hasInstagramConnectRun) {
      setHasInstagramConnectRun(true);
      if (error?.code === INSTA_WARNING_ERROR) {
        warning(
          <Toast header={actionTitle} content={instaWarningContent} closeButtonAriaLabel={layout.buttons.close} />,
          {
            onClose: () => clearError()
          }
        );
      } else if (error && errorMap) {
        const header = error?.code === INSTA_CANNOT_CONNECT ? cannotConnectInstaAccountHeader : unhandledError;
        const errorMessage = getErrorMessage(errorMap, error, unhandledError);
        errorToast(<Toast header={header} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />, {
          onClose: () => clearError()
        });
      }
    }
  }, [
    error,
    actionTitle,
    instaWarningContent,
    warning,
    stableDispatch,
    errorToast,
    unhandledError,
    errorMap,
    cannotConnectInstaAccountHeader,
    hasInstagramConnectRun
  ]);

  useEffect(() => {
    return () => {
      // clear context states on unmount
      stableDispatch({ type: ERROR, data: false });
      stableDispatch({ type: VALIDATION_ERROR, data: false });
    };
  }, [stableDispatch]);

  const [contentTypes, setContentTypes] = useState([]);
  const [isFileSubmissionEnabled, setIsFileSubmissionEnabled] = useState(false);
  const { websiteModalLabels, getContentTypes, uploadFileModalLabels } = useContentSubmissionForms(
    websiteContentLabels,
    layout,
    contentTypes,
    participationId,
    thumbnail,
    YOUTUBE_HOSTS,
    TWITCH_HOSTS,
    INSTAGRAM_HOSTS,
    FACEBOOK_HOSTS,
    TIKTOK_HOSTS,
    setContentTypes,
    myContentLabels,
    stableDispatch,
    uploadContentLabels
  );
  const onSubmitNonSocialContent = (deliverable) => {
    if (deliverable.status === "CHANGE_REQUESTED") {
      if (content?.[deliverable.id]?.length > 0) {
        setSelectedContentId(content?.[deliverable.id]?.[0]?.id);
      }
      if (deliverable.format === "WEBSITE") {
        setEnableUpdateWebsite(true);
      } else {
        setEnableUpdateFileUpload(true);
      }
    }
    setDeliverableId(deliverable.id);
    setDeliverableTitle(deliverable.title);
    setDeliverableType(deliverable.type);

    if (deliverable.format === "WEBSITE") {
      setshowModalWindowForWebsite(true);
      return;
    }
    setFileTypes(deliverable.uploadFileTypes);
    const formatContentTypes = deliverable.uploadFileTypes.map((fileType) => ({
      label: myContentLabels[fileType.type],
      value: fileType.type
    }));
    setContentTypesForUpload([{ label: websiteContentLabels.selectContentType, value: "" }, ...formatContentTypes]);
    setshowModalWindowForUploadContent(true);
  };
  useEffect(() => {
    getContentTypes();
    const changesRequestedContent = [];
    const changesRequestedDeliverable = opportunity.contentSubmission.deliverables?.filter(
      (deliverable) => (deliverable as unknown as OpportunityWithDeliverablesStatus).status === "CHANGE_REQUESTED"
    );
    if (changesRequestedDeliverable?.length > 0) {
      changesRequestedDeliverable.forEach((deliverable) => {
        changesRequestedContent.push(content?.[deliverable.id]?.[0]);
      });
    }
    if (!showWarningForChangesRequested && changesRequestedContent?.length > 0) {
      showChangeRequestedToast(warning, contentSubmissionLabels, changesRequestedContent, layout, stableDispatch);
    }
    setIsFileSubmissionEnabled(
      opportunity.contentSubmission.deliverables?.some((deliverable) => deliverable.format === "FILE")
    );
  }, []);

  const closeWebsiteContentModal = useCallback(() => {
    setshowModalWindowForWebsite(false);
    /** Disable the update option in the website modal */
    setEnableUpdateWebsite(false);
  }, []);

  const closeUploadContentModal = useCallback(() => {
    setshowModalWindowForUploadContent(false);
    /** Disable the update option in the website modal */
    setEnableUpdateFileUpload(false);
  }, []);

  const onToggleFeedback = async (collapsed, contentId, status, type) => {
    if (collapsed == false) return;
    if (status === "REJECTED" || status === "APPROVED") return;
    const currentFeedback = contentsFeedback?.[contentId]
      ? contentsFeedback?.[contentId]
      : await getFeedbacks({ contentId }, type);
    setContentsFeedback({ ...contentsFeedback, [contentId]: currentFeedback });
    setFeedbackLoading(false);
  };

  const getFeedbacks = async (criteria, type) => {
    setFeedbackLoading(true);
    try {
      const response = (await SubmittedContentService.getContentsFeedback(criteria)) as AxiosResponse;
      return response.data.contentsFeedback.slice(0, 1).map((feedback) => ({
        ...feedback,
        ...layout.contentCard,
        sentOnLabel: layout.contentCard.sentOn,
        fromLabel: layout.contentCard.from,
        title: layout.contentCard.changesRequired,
        contentVersion: feedback.contentVersion,
        lastUpdateDate: `${feedback.formattedSubmittedDate(locale)}`,
        content: feedback.description,
        note: t("common:additionalDescription", {
          ...{
            contentType: type === "FILE" ? type.toLowerCase() : layout.contentCard.url?.toLowerCase(),
            updateType: type === "FILE" ? layout.buttons.upload?.toLowerCase() : layout.buttons.update?.toLowerCase()
          }
        })
      }));
    } catch (e) {
      setFeedbackLoading(false);
      errorHandler(stableDispatch, e);
    }
  };

  const connectFaceBookModalLabels = {
    title: addContentFormLabels.modalConfirmationTitleFB,
    cancel: layout.buttons.cancel,
    connect: layout.buttons.connect,
    close: layout.buttons.close
  };

  return (
    <div
      className={
        !UPDATE_OPPORTUNITY_DETAILS ? "content-deliverables-container" : "content-deliverables-container-with-flag"
      }
    >
      <div className="content-deliverables-requirement-container">
        <section className="content-deliverables-details-section">
          {opportunity.contentSubmission.guidelines && (
            <div className="content-deliverables-guidelines">
              <OpportunityDescription
                title={opportunitiesLabels.contentGuidelinesTitle}
                description={opportunity.contentSubmission.guidelines}
                buttonLabel={opportunitiesLabels.viewAll}
                size={size}
              ></OpportunityDescription>
            </div>
          )}

          {!isFileSubmissionEnabled && (
            <>
              <ContentGuideLines {...disclosurePolicy} id="content-deliverables-disclosure" />
              <ContentGuideLines {...linkSubmissionLabels} id="content-deliverables-link-submission" />
            </>
          )}
          <div className="content-deliverables-available-resources-section">
            <h4 className="content-deliverables-available-resources-title">
              {contentSubmissionLabels.availableResources}
            </h4>
            <div className="content-deliverables-download-buttons-container">
              <div className="content-deliverables-download-watermark">
                <a className="btn btn-secondary btn-md inline-block" href={WATERMARKS_URL} download>
                  {contentSubmissionLabels.downloadEaLogo}
                </a>
              </div>

              {opportunity?.hasAttachments && (
                <div className="content-deliverables-download-attachments">
                  <a
                    className="btn btn-secondary btn-md inline-block"
                    href={opportunity?.attachmentsUrl}
                    download
                    onClick={() => {
                      analytics.clickedDownloadAttachment({ locale, opportunity });
                    }}
                  >
                    {contentSubmissionLabels.downloadAttachments}
                  </a>
                </div>
              )}
            </div>
          </div>
        </section>
      </div>
      <div>
        {isJoined ? (
          <>
            {hasContentSubmissionWindowStarted ? (
              <>
                <h4 className="content-deliverables-section-title">{contentSubmissionLabels.contentDeliverables}</h4>
                <section
                  className={
                    !UPDATE_OPPORTUNITY_DETAILS
                      ? "content-deliverables-after-join-opportunity-section"
                      : "content-deliverables-after-join-opportunity-section-with-flag"
                  }
                >
                  {opportunity.contentSubmission.deliverables?.map((deliverable, index) => {
                    return (
                      <div
                        key={index}
                        className="content-deliverables-deliverable-card"
                        id={content?.[deliverable?.id]?.[0]?.id}
                      >
                        <DeliverableCard
                          labels={{
                            title: deliverable.title,
                            description: deliverable.description,
                            submitText:
                              (deliverable as unknown as OpportunityWithDeliverablesStatus).status ===
                              "CHANGE_REQUESTED"
                                ? layout.buttons.update
                                : layout.buttons.submit,
                            status:
                              deliverable.type === "UNLIMITED"
                                ? "UNLIMITED_CONTENT"
                                : (deliverable as unknown as OpportunityWithDeliverablesStatus).status
                                ? (deliverable as unknown as OpportunityWithDeliverablesStatus).status
                                : "AWAITING_SUBMISSION",
                            statusLabel:
                              (isSubmissionWindowClosed && deliverable.type === "UNLIMITED") ||
                              (isSubmissionWindowClosed && content?.[deliverable?.id]?.length === 0) ||
                              (isSubmissionWindowClosed &&
                                !(deliverable as unknown as OpportunityWithDeliverablesStatus).status)
                                ? contentSubmissionLabels.submissionWindowClosed
                                : statusLabel[
                                    (deliverable as unknown as OpportunityWithDeliverablesStatus).status ===
                                      "REJECTED" && FLAG_CONTENT_WITH_FINAL_REMARK
                                      ? "NOT_APPROVED"
                                      : deliverable.type === "UNLIMITED"
                                      ? "UNLIMITED_CONTENT"
                                      : (deliverable as unknown as OpportunityWithDeliverablesStatus).status
                                      ? (deliverable as unknown as OpportunityWithDeliverablesStatus).status
                                      : "AWAITING_SUBMISSION"
                                  ]
                          }}
                          disableSubmission={
                            (isSubmissionWindowClosed &&
                              !(deliverable as unknown as OpportunityWithDeliverablesStatus).status) ||
                            (isSubmissionWindowClosed && deliverable.type === "UNLIMITED")
                          }
                          contentTypeIcons={
                            (deliverable.format === "SOCIAL"
                              ? deliverable.socialAccountTypes
                              : [deliverable.format === "FILE" ? "UPLOAD" : deliverable.format]) as
                              | ContentIconType[]
                              | []
                          }
                          format={deliverable.format}
                          submitContentButtons={getSubmitContentButtons(
                            deliverable.format as DeliverableFormat,
                            (deliverable.socialAccountTypes || []) as AccountName[],
                            index,
                            deliverable.title,
                            deliverable.type
                          )}
                          submitContentHandler={() => onSubmitNonSocialContent(deliverable)}
                          instruction={addContentFormLabels.addContentInstruction}
                          hasFinalRemark={FLAG_CONTENT_WITH_FINAL_REMARK}
                        >
                          <>
                            {content?.[deliverable?.id]?.length > 0 &&
                              content?.[deliverable?.id]?.map((content, index) => {
                                return (
                                  <div key={index} data-testid="deliverable-content-card">
                                    <ContentCard
                                      content={{
                                        ...(content as Content),
                                        reviewFinalRemark: (content as Content).reviewFinalRemark
                                          ? {
                                              ...(content as Content).reviewFinalRemark,
                                              date: content.formattedReviewFinalRemarkDate(locale)
                                            }
                                          : (content as Content).reviewFinalRemark
                                      }}
                                      labels={layout.contentCard}
                                      opportunityClickHandler={() => {
                                        router.push(`/opportunities/${content.opportunityId}`);
                                      }}
                                      accountType={
                                        (content.type
                                          ? content.type
                                          : content.sourceType === "USER_DEVICE"
                                          ? "UPLOAD"
                                          : content.sourceType) as AccountType
                                      }
                                      submittedDate={`${content.formattedSubmittedDate(locale)}`}
                                      handleToggleFeedback={(collapsed) =>
                                        onToggleFeedback(
                                          collapsed,
                                          content.id,
                                          content.status,
                                          content.type
                                            ? content.type
                                            : content.sourceType === "USER_DEVICE"
                                            ? "FILE"
                                            : content.sourceType
                                        )
                                      }
                                      feedback={{
                                        ...contentsFeedback?.[content.id]?.[0]
                                      }}
                                      changesRequested={content.requiresChanges() as unknown as boolean}
                                      isLoading={feedbackLoading}
                                      isMcrEnabled={
                                        config.FLAG_SIGNED_URL_V2_ENABLED || config.FLAG_SIGNED_URL_V1_ENABLED
                                      }
                                    />
                                  </div>
                                );
                              })}

                            {deliverable.type === "UNLIMITED" &&
                              deliverablePages?.[deliverable?.id] &&
                              deliverablePages?.[deliverable.id]?.length > 1 &&
                              currentDeliverablePage?.[deliverable?.id] && (
                                <div className="content-deliverables-add-content-submitted-list">
                                  <Pagination
                                    next={layout.buttons.next}
                                    prev={layout.buttons.prev}
                                    pages={deliverablePages[deliverable.id]}
                                    currentPage={currentDeliverablePage[deliverable.id]}
                                    onPageChange={(page) => {
                                      stableDispatch({
                                        type: CURRENT_DELIVERABLE_PAGE,
                                        data: { ...currentDeliverablePage, [deliverable.id]: page }
                                      });
                                      stableDispatch({ type: CLICKED_DELIVERABLE_ID, data: deliverable.id });
                                    }}
                                  />
                                </div>
                              )}
                          </>
                        </DeliverableCard>
                        {deliverable.format === "SOCIAL" && showConnectAccountModal[index] && (
                          <ConnectAccountModal
                            onClose={() => handleCloseConnectAccountModal(index)}
                            accounts={getApprovedAccounts(
                              deliverable?.format,
                              deliverable?.socialAccountTypes as AccountName[]
                            )}
                            labels={{
                              addAccount: addContentFormLabels.addAccount,
                              connectAnAccount: addContentFormLabels.connectAnAccount
                            }}
                            username={""}
                            showThreeColumns={deliverable.socialAccountTypes.length >= 3}
                            closeButtonAriaLabel={layout.buttons.close}
                          />
                        )}
                        {deliverable.format === "SOCIAL" && showModalWindowForSocialAccount[index] && (
                          <SubmitSocialMediaContentModal
                            {...{
                              ...socialChannelModalLabels,
                              analytics,
                              opportunity,
                              locale,
                              onClose: () => closeSocialContentModal(index),
                              setUpdatedContent: setUpdatedContent,
                              addContentFormLabels: addContentFormLabels,
                              facebookGuideModalLabels: facebookGuideModalLabels,
                              youtubeGuideModalLabels: youtubeGuideModalLabels,
                              instagramGuideModalLabels: instagramGuideModalLabels,
                              twitchGuideModalLabels: twitchGuideModalLabels,
                              tiktokGuideModalLabels: tiktokGuideModalLabels,
                              deliverableId: deliverable.id,
                              deliverableTitle: deliverable.title,
                              deliverableType: deliverable.type,
                              FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED
                            }}
                          />
                        )}
                      </div>
                    );
                  })}
                  {showPagesModal && (
                    <ConnectFacebookPagesModal
                      {...{
                        labels: connectFaceBookModalLabels,
                        onClose: onCloseFb,
                        onChange,
                        pages,
                        onConnect: onConnectFb,
                        pending,
                        selectedPage
                      }}
                    />
                  )}
                </section>
              </>
            ) : (
              <>
                <h4 className="content-deliverables-section-title">{contentSubmissionLabels.contentDeliverables}</h4>
                <p className="content-deliverables-joined-submission-window-not-started">
                  {contentSubmissionLabels.submissionOpensWithNoContent}
                </p>
                <section className="content-deliverables-after-join-opportunity-section">
                  {opportunity.contentSubmission.deliverables?.map((deliverable, index) => {
                    return (
                      <article key={index} className="content-deliverables-deliverable-card">
                        <DeliverableCard
                          labels={{
                            title: deliverable.title,
                            description: deliverable.description,
                            submitText: layout.buttons.submit
                          }}
                          disableSubmission={isSubmissionWindowClosed}
                          contentTypeIcons={
                            (deliverable.format === "SOCIAL"
                              ? deliverable.socialAccountTypes
                              : [deliverable.format === "FILE" ? "UPLOAD" : deliverable.format]) as
                              | ContentIconType[]
                              | []
                          }
                          format={deliverable.format}
                        />
                      </article>
                    );
                  })}
                </section>
              </>
            )}
            {showModalWindowForWebsite && (
              <SubmitWebsiteContentModal
                {...{
                  ...websiteModalLabels,
                  analytics,
                  opportunity,
                  locale,
                  onClose: closeWebsiteContentModal,
                  setUpdatedContent: setUpdatedContent,
                  enableUpdateWebsite,
                  deliverableId,
                  content,
                  selectedContentId,
                  deliverableTitle,
                  deliverableType
                }}
              />
            )}

            {showModalWindowForUploadContent && (
              <UploadContentSubmissionModal
                {...{
                  ...uploadFileModalLabels,
                  contentTypes: contentTypesForUpload,
                  participationId,
                  thumbnail,
                  fileTypes,
                  analytics,
                  opportunity,
                  locale,
                  show: showModalWindowForUploadContent,
                  onClose: closeUploadContentModal,
                  setUpdatedContent: setUpdatedContent,
                  isForUpdateUploadedFile,
                  content: deliverableId && content?.[deliverableId]?.find(({ id }) => id === selectedContentId),
                  deliverableId,
                  deliverableTitle,
                  deliverableType,
                  creatorId: creator.id,
                  nucleusId: creator.accountInformation.nucleusId
                }}
              />
            )}
          </>
        ) : (
          <>
            <h4 className="content-deliverables-section-title">{contentSubmissionLabels.contentDeliverables}</h4>
            <section className="content-deliverables-bofore-join-opportunity-section">
              {!isSubmissionWindowClosed && (
                <p className="content-deliverables-instruction-text">
                  {contentSubmissionLabels.deliverablesInstruction}
                </p>
              )}
              {opportunity?.contentSubmission?.deliverables?.map((deliverable, index) => {
                return (
                  <div key={index} className="content-deliverables-deliverable-card">
                    <DeliverableCard
                      labels={{
                        title: deliverable.title,
                        description: deliverable.description,
                        submitText: layout.buttons.submit
                      }}
                      contentTypeIcons={
                        (deliverable.format === "SOCIAL"
                          ? deliverable.socialAccountTypes
                          : [deliverable.format === "FILE" ? "UPLOAD" : deliverable.format]) as ContentIconType[] | []
                      }
                      disableSubmission={true}
                    />
                  </div>
                );
              })}
            </section>
          </>
        )}
      </div>
    </div>
  );
};

export default ContentDeliverablesTab;
