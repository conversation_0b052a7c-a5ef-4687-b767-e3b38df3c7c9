import { useRouter } from "next/router";
import { useEffect } from "react";
import { setCookie } from "../utils";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { useDependency } from "@src/context/DependencyContext";

// This page is used for inviting members from the SF site.
// Ex: /en-US/sign-up?code=3286-70d3-fc88-b2b4&opportunityId=a0M7c00000GbYEDEA3
export default function SignUp() {
  const { configuration: config } = useDependency();
  const SUPPORTED_LOCALES = config.SUPPORTED_LOCALES;
  const router = useRouter();

  useEffect(() => {
    if (!router.isReady) return;

    if (!router?.query?.code) {
      router.push(`/404`);
      return;
    }

    if (SUPPORTED_LOCALES.includes(router.locale)) setCookie(router.locale);
    const url = `/api/registrations?code=${router.query.code}&opportunityId=${router.query.opportunityId}`;
    router.push(url, url, { locale: router.locale });
  }, [router.isReady]);
  return <></>;
}

export const getStaticProps = async () => ({
  props: {
    runtimeConfiguration: runtimeConfiguration()
  }
});
